#!/bin/bash
# سكريبت النسخ الاحتياطي التلقائي لتطبيق Ta9affi

set -e  # إيقاف عند أي خطأ

# ألوان للرسائل
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# دوال المساعدة
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# متغيرات الإعداد
APP_DIR="/path/to/ta9affi"
BACKUP_DIR="$APP_DIR/backups"
VENV_DIR="$APP_DIR/venv"
LOG_FILE="$BACKUP_DIR/backup.log"

# إعدادات قاعدة البيانات
DB_HOST="localhost"
DB_PORT="5432"
DB_NAME="ta9affi"
DB_USER="ta9affi_user"
DB_PASSWORD="ta9affi_password"

# إعدادات النسخ الاحتياطي
MAX_LOCAL_BACKUPS=7
MAX_REMOTE_BACKUPS=30
COMPRESS_BACKUPS=true

# دالة تسجيل الرسائل
log_message() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] $message" >> "$LOG_FILE"
    echo "$message"
}

# إنشاء مجلدات النسخ الاحتياطي
create_backup_dirs() {
    log_info "إنشاء مجلدات النسخ الاحتياطي..."
    
    mkdir -p "$BACKUP_DIR"
    mkdir -p "$BACKUP_DIR/database"
    mkdir -p "$BACKUP_DIR/files"
    mkdir -p "$BACKUP_DIR/config"
    mkdir -p "$(dirname "$LOG_FILE")"
    
    log_success "تم إنشاء مجلدات النسخ الاحتياطي"
}

# نسخ احتياطي لقاعدة البيانات
backup_database() {
    log_info "بدء نسخ احتياطي لقاعدة البيانات..."
    
    local timestamp=$(date '+%Y%m%d_%H%M%S')
    local backup_file="$BACKUP_DIR/database/database_backup_$timestamp.sql"
    
    # تعيين متغير كلمة المرور
    export PGPASSWORD="$DB_PASSWORD"
    
    # تنفيذ pg_dump
    if pg_dump -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f "$backup_file" --verbose; then
        
        # ضغط الملف إذا كان مطلوباً
        if [ "$COMPRESS_BACKUPS" = true ]; then
            gzip "$backup_file"
            backup_file="$backup_file.gz"
        fi
        
        local file_size=$(du -h "$backup_file" | cut -f1)
        log_success "تم إنشاء نسخة احتياطية لقاعدة البيانات: $backup_file ($file_size)"
        
        # حفظ معلومات النسخة الاحتياطية
        echo "{\"type\":\"database\",\"file\":\"$(basename "$backup_file")\",\"size\":\"$file_size\",\"date\":\"$(date -Iseconds)\"}" >> "$BACKUP_DIR/backup_log.json"
        
        return 0
    else
        log_error "فشل في إنشاء نسخة احتياطية لقاعدة البيانات"
        return 1
    fi
    
    # إلغاء تعيين متغير كلمة المرور
    unset PGPASSWORD
}

# نسخ احتياطي للملفات
backup_files() {
    log_info "بدء نسخ احتياطي للملفات..."
    
    local timestamp=$(date '+%Y%m%d_%H%M%S')
    local backup_file="$BACKUP_DIR/files/files_backup_$timestamp.tar"
    
    # قائمة المجلدات المراد نسخها
    local include_dirs=("uploads" "static" "logs")
    local exclude_patterns=("*.tmp" "*.log" "__pycache__" "*.pyc")
    
    # بناء أمر tar
    local tar_cmd="tar -cf $backup_file"
    
    # إضافة استثناءات
    for pattern in "${exclude_patterns[@]}"; do
        tar_cmd="$tar_cmd --exclude=$pattern"
    done
    
    # إضافة المجلدات
    for dir in "${include_dirs[@]}"; do
        if [ -d "$APP_DIR/$dir" ]; then
            tar_cmd="$tar_cmd -C $APP_DIR $dir"
        fi
    done
    
    # تنفيذ الأمر
    if eval "$tar_cmd"; then
        
        # ضغط الأرشيف إذا كان مطلوباً
        if [ "$COMPRESS_BACKUPS" = true ]; then
            gzip "$backup_file"
            backup_file="$backup_file.gz"
        fi
        
        local file_size=$(du -h "$backup_file" | cut -f1)
        log_success "تم إنشاء نسخة احتياطية للملفات: $backup_file ($file_size)"
        
        # حفظ معلومات النسخة الاحتياطية
        echo "{\"type\":\"files\",\"file\":\"$(basename "$backup_file")\",\"size\":\"$file_size\",\"date\":\"$(date -Iseconds)\"}" >> "$BACKUP_DIR/backup_log.json"
        
        return 0
    else
        log_error "فشل في إنشاء نسخة احتياطية للملفات"
        return 1
    fi
}

# نسخ احتياطي للإعدادات
backup_config() {
    log_info "بدء نسخ احتياطي للإعدادات..."
    
    local timestamp=$(date '+%Y%m%d_%H%M%S')
    local backup_file="$BACKUP_DIR/config/config_backup_$timestamp.tar.gz"
    
    # قائمة الملفات المراد نسخها
    local config_files=("config.py" "requirements.txt" ".env" "gunicorn_config.py" "nginx_ta9affi.conf")
    
    # إنشاء أرشيف مضغوط
    local tar_cmd="tar -czf $backup_file -C $APP_DIR"
    
    for file in "${config_files[@]}"; do
        if [ -f "$APP_DIR/$file" ]; then
            tar_cmd="$tar_cmd $file"
        fi
    done
    
    # إضافة معلومات النظام
    local system_info_file="$BACKUP_DIR/temp_system_info.txt"
    {
        echo "تاريخ النسخة الاحتياطية: $(date)"
        echo "إصدار النظام: $(uname -a)"
        echo "إصدار Python: $(python3 --version)"
        echo "مساحة القرص المتاحة: $(df -h $APP_DIR | tail -1)"
        echo "استخدام الذاكرة: $(free -h)"
    } > "$system_info_file"
    
    tar_cmd="$tar_cmd -C $BACKUP_DIR temp_system_info.txt"
    
    # تنفيذ الأمر
    if eval "$tar_cmd"; then
        # حذف الملف المؤقت
        rm -f "$system_info_file"
        
        local file_size=$(du -h "$backup_file" | cut -f1)
        log_success "تم إنشاء نسخة احتياطية للإعدادات: $backup_file ($file_size)"
        
        # حفظ معلومات النسخة الاحتياطية
        echo "{\"type\":\"config\",\"file\":\"$(basename "$backup_file")\",\"size\":\"$file_size\",\"date\":\"$(date -Iseconds)\"}" >> "$BACKUP_DIR/backup_log.json"
        
        return 0
    else
        rm -f "$system_info_file"
        log_error "فشل في إنشاء نسخة احتياطية للإعدادات"
        return 1
    fi
}

# تنظيف النسخ الاحتياطية القديمة
cleanup_old_backups() {
    log_info "بدء تنظيف النسخ الاحتياطية القديمة..."
    
    local cleaned_count=0
    
    # تنظيف نسخ قاعدة البيانات
    if [ -d "$BACKUP_DIR/database" ]; then
        local old_db_files=$(find "$BACKUP_DIR/database" -name "database_backup_*" -mtime +$MAX_LOCAL_BACKUPS)
        for file in $old_db_files; do
            rm -f "$file"
            log_info "تم حذف النسخة الاحتياطية القديمة: $(basename "$file")"
            ((cleaned_count++))
        done
    fi
    
    # تنظيف نسخ الملفات
    if [ -d "$BACKUP_DIR/files" ]; then
        local old_file_backups=$(find "$BACKUP_DIR/files" -name "files_backup_*" -mtime +$MAX_LOCAL_BACKUPS)
        for file in $old_file_backups; do
            rm -f "$file"
            log_info "تم حذف النسخة الاحتياطية القديمة: $(basename "$file")"
            ((cleaned_count++))
        done
    fi
    
    # تنظيف نسخ الإعدادات
    if [ -d "$BACKUP_DIR/config" ]; then
        local old_config_backups=$(find "$BACKUP_DIR/config" -name "config_backup_*" -mtime +$MAX_LOCAL_BACKUPS)
        for file in $old_config_backups; do
            rm -f "$file"
            log_info "تم حذف النسخة الاحتياطية القديمة: $(basename "$file")"
            ((cleaned_count++))
        done
    fi
    
    log_success "تم تنظيف $cleaned_count نسخة احتياطية قديمة"
}

# رفع النسخ الاحتياطية للتخزين السحابي (اختياري)
upload_to_cloud() {
    local backup_file="$1"
    local backup_type="$2"
    
    # التحقق من وجود إعدادات AWS
    if [ -n "$AWS_ACCESS_KEY_ID" ] && [ -n "$AWS_SECRET_ACCESS_KEY" ] && [ -n "$AWS_BACKUP_BUCKET" ]; then
        log_info "رفع النسخة الاحتياطية للتخزين السحابي..."
        
        local s3_key="ta9affi-backups/$backup_type/$(basename "$backup_file")"
        
        if aws s3 cp "$backup_file" "s3://$AWS_BACKUP_BUCKET/$s3_key"; then
            log_success "تم رفع النسخة الاحتياطية للتخزين السحابي: $s3_key"
        else
            log_warning "فشل في رفع النسخة الاحتياطية للتخزين السحابي"
        fi
    fi
}

# عرض حالة النسخ الاحتياطي
show_backup_status() {
    log_info "حالة النسخ الاحتياطي:"
    echo "=================================="
    
    # عدد النسخ الاحتياطية
    local db_count=$(find "$BACKUP_DIR/database" -name "database_backup_*" 2>/dev/null | wc -l)
    local files_count=$(find "$BACKUP_DIR/files" -name "files_backup_*" 2>/dev/null | wc -l)
    local config_count=$(find "$BACKUP_DIR/config" -name "config_backup_*" 2>/dev/null | wc -l)
    
    echo "📊 عدد النسخ الاحتياطية:"
    echo "   قاعدة البيانات: $db_count"
    echo "   الملفات: $files_count"
    echo "   الإعدادات: $config_count"
    
    # الحجم الإجمالي
    local total_size=$(du -sh "$BACKUP_DIR" 2>/dev/null | cut -f1)
    echo "💾 الحجم الإجمالي: $total_size"
    
    # آخر نسخة احتياطية
    local last_backup=$(find "$BACKUP_DIR" -name "*backup_*" -type f -printf '%T@ %p\n' 2>/dev/null | sort -n | tail -1 | cut -d' ' -f2-)
    if [ -n "$last_backup" ]; then
        local last_backup_date=$(stat -c %y "$last_backup" 2>/dev/null | cut -d'.' -f1)
        echo "🕒 آخر نسخة احتياطية: $(basename "$last_backup") ($last_backup_date)"
    fi
    
    echo "=================================="
}

# الدالة الرئيسية
main() {
    echo "🚀 بدء عملية النسخ الاحتياطي لتطبيق Ta9affi"
    echo "============================================="
    
    # إنشاء مجلدات النسخ الاحتياطي
    create_backup_dirs
    
    # تسجيل بداية العملية
    log_message "بدء عملية النسخ الاحتياطي"
    
    local success_count=0
    local total_operations=3
    
    # نسخ احتياطي لقاعدة البيانات
    if backup_database; then
        ((success_count++))
    fi
    
    # نسخ احتياطي للملفات
    if backup_files; then
        ((success_count++))
    fi
    
    # نسخ احتياطي للإعدادات
    if backup_config; then
        ((success_count++))
    fi
    
    # تنظيف النسخ القديمة
    cleanup_old_backups
    
    # عرض النتائج
    echo "============================================="
    if [ $success_count -eq $total_operations ]; then
        log_success "تم إنجاز جميع عمليات النسخ الاحتياطي بنجاح! ($success_count/$total_operations)"
    else
        log_warning "تم إنجاز $success_count من أصل $total_operations عمليات نسخ احتياطي"
    fi
    
    # عرض حالة النسخ الاحتياطي
    show_backup_status
    
    log_message "انتهاء عملية النسخ الاحتياطي"
}

# معالجة المعاملات
case "${1:-}" in
    "database")
        create_backup_dirs
        backup_database
        ;;
    "files")
        create_backup_dirs
        backup_files
        ;;
    "config")
        create_backup_dirs
        backup_config
        ;;
    "cleanup")
        cleanup_old_backups
        ;;
    "status")
        show_backup_status
        ;;
    "help"|"-h"|"--help")
        echo "الاستخدام: $0 [database|files|config|cleanup|status|help]"
        echo ""
        echo "الخيارات:"
        echo "  database  - نسخ احتياطي لقاعدة البيانات فقط"
        echo "  files     - نسخ احتياطي للملفات فقط"
        echo "  config    - نسخ احتياطي للإعدادات فقط"
        echo "  cleanup   - تنظيف النسخ القديمة"
        echo "  status    - عرض حالة النسخ الاحتياطي"
        echo "  help      - عرض هذه المساعدة"
        echo ""
        echo "بدون معاملات: تشغيل نسخ احتياطي شامل"
        ;;
    *)
        main
        ;;
esac
