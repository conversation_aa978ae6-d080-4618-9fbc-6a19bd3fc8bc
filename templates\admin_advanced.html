{% extends "base.html" %}

{% block title %}الإدارة المتقدمة{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-users-cog me-2"></i>الإدارة المتقدمة
        </h1>
        <a href="{{ url_for('admin_dashboard') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة للوحة الأدمن
        </a>
    </div>

    <!-- تحذير عام -->
    <div class="alert alert-danger" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <strong>تحذير:</strong> هذه الصفحة تحتوي على أدوات إدارة متقدمة وحساسة. يرجى استخدامها بحذر شديد.
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>إجمالي سجلات التقدم</div>
                        <div class="h3">{{ total_progress_entries }}</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>إجمالي الأساتذة</div>
                        <div class="h3">{{ total_teachers }}</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-info text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>أساتذة لديهم تقدمات</div>
                        <div class="h3">{{ teachers_with_progress }}</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-warning text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>أساتذة بدون تقدمات</div>
                        <div class="h3">{{ total_teachers - teachers_with_progress }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- أدوات الإدارة المتقدمة -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-tools me-2"></i>أدوات الإدارة المتقدمة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- تصفير عدّاد التقدم -->
                        <div class="col-md-6 mb-4">
                            <div class="card border-danger">
                                <div class="card-header bg-danger text-white">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-trash-alt me-2"></i>تصفير عدّاد التقدم
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">
                                        <strong>الوصف:</strong> حذف جميع سجلات التقدم لكافة الأساتذة وإعادة تصفير النسب
                                        الإجمالية والتفصيلية.
                                    </p>
                                    <p class="card-text">
                                        <strong>الاستخدام:</strong> في نهاية الموسم أو السنة الدراسية لبدء موسم جديد.
                                    </p>
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه!
                                    </div>
                                    <button type="button" class="btn btn-danger btn-lg w-100" data-bs-toggle="modal"
                                        data-bs-target="#resetProgressModal">
                                        <i class="fas fa-trash-alt me-2"></i>تصفير عدّاد التقدم
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- إدارة الحسابات المهجورة -->
                        <div class="col-md-6 mb-4">
                            <div class="card border-danger">
                                <div class="card-header bg-danger text-white">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-user-times me-2"></i>إدارة الحسابات المهجورة
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">
                                        عرض وحذف الحسابات التي لم تسجل دخول لأكثر من 6 أشهر.
                                    </p>
                                    <div class="alert alert-warning alert-sm mb-3">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        <small>تتضمن حذف جميع البيانات المرتبطة نهائياً</small>
                                    </div>
                                    <a href="{{ url_for('admin_abandoned_accounts') }}" class="btn btn-danger w-100">
                                        <i class="fas fa-search me-2"></i>عرض الحسابات المهجورة
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- إدارة النسخ الاحتياطية -->
                        <div class="col-md-6 mb-4">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-database me-2"></i>إدارة النسخ الاحتياطية
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">
                                        <strong>الوصف:</strong> إنشاء وإدارة النسخ الاحتياطية لقاعدة البيانات الرئيسية
                                        ta9affi.db
                                    </p>
                                    <p class="card-text">
                                        <strong>الميزات:</strong> إنشاء نسخة احتياطية، تصدير قاعدة البيانات، استيراد
                                        قاعدة بيانات، عرض النسخ المتاحة
                                    </p>

                                    <div class="d-grid gap-2">
                                        <!-- إنشاء نسخة احتياطية -->
                                        <a href="{{ url_for('backup_database') }}" class="btn btn-success">
                                            <i class="fas fa-save me-2"></i>إنشاء نسخة احتياطية
                                        </a>

                                        <!-- تصدير قاعدة البيانات -->
                                        <a href="{{ url_for('export_database') }}" class="btn btn-primary">
                                            <i class="fas fa-download me-2"></i>تصدير قاعدة البيانات
                                        </a>

                                        <!-- استيراد قاعدة بيانات -->
                                        <button type="button" class="btn btn-warning" data-bs-toggle="modal"
                                            data-bs-target="#importDatabaseModal">
                                            <i class="fas fa-upload me-2"></i>استيراد قاعدة بيانات
                                        </button>

                                        <!-- عرض النسخ الاحتياطية -->
                                        <button type="button" class="btn btn-info" data-bs-toggle="modal"
                                            data-bs-target="#backupsModal">
                                            <i class="fas fa-list me-2"></i>عرض النسخ الاحتياطية
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal تأكيد تصفير التقدم -->
<div class="modal fade" id="resetProgressModal" tabindex="-1" aria-labelledby="resetProgressModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="resetProgressModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>تأكيد تصفير عدّاد التقدم
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                    aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <h5><i class="fas fa-exclamation-triangle me-2"></i>تحذير شديد!</h5>
                    <p class="mb-0">
                        أنت على وشك حذف <strong>جميع</strong> سجلات التقدم لكافة الأساتذة في النظام.
                        هذا الإجراء سيؤدي إلى:
                    </p>
                </div>

                <ul class="list-group list-group-flush mb-3">
                    <li class="list-group-item">
                        <i class="fas fa-times text-danger me-2"></i>
                        حذف جميع سجلات التقدم ({{ total_progress_entries }} سجل)
                    </li>
                    <li class="list-group-item">
                        <i class="fas fa-times text-danger me-2"></i>
                        تصفير نسب التقدم الإجمالية لجميع الأساتذة
                    </li>
                    <li class="list-group-item">
                        <i class="fas fa-times text-danger me-2"></i>
                        تصفير نسب التقدم التفصيلية لجميع المواد
                    </li>
                    <li class="list-group-item">
                        <i class="fas fa-times text-danger me-2"></i>
                        فقدان جميع البيانات التاريخية للتقدم
                    </li>
                </ul>

                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>ملاحظة:</strong> لن يتم حذف بيانات المستخدمين أو المناهج الدراسية، فقط سجلات التقدم.
                </div>

                <form id="resetProgressForm" action="{{ url_for('reset_all_progress') }}" method="POST">
                    <div class="mb-3">
                        <label for="adminPassword" class="form-label">
                            <strong>أدخل كلمة مرور حسابك للتأكيد:</strong>
                        </label>
                        <input type="password" class="form-control" id="adminPassword" name="admin_password"
                            placeholder="كلمة مرور الأدمن" required>
                    </div>

                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="confirmReset" name="confirm_reset" required>
                        <label class="form-check-label" for="confirmReset">
                            <strong>أؤكد أنني أفهم عواقب هذا الإجراء وأريد المتابعة</strong>
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>إلغاء
                </button>
                <button type="submit" form="resetProgressForm" class="btn btn-danger">
                    <i class="fas fa-trash-alt me-1"></i>تأكيد التصفير
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    // تأكيد إضافي قبل الإرسال
    document.getElementById('resetProgressForm').addEventListener('submit', function (e) {
        const password = document.getElementById('adminPassword').value;
        const confirmed = document.getElementById('confirmReset').checked;

        if (!password || !confirmed) {
            e.preventDefault();
            alert('يرجى إدخال كلمة المرور وتأكيد الموافقة');
            return;
        }

        const finalConfirm = confirm('هل أنت متأكد تماماً من رغبتك في تصفير جميع سجلات التقدم؟\n\nهذا الإجراء لا يمكن التراجع عنه!');
        if (!finalConfirm) {
            e.preventDefault();
        }
    });
</script>

<!-- Modal لاستيراد قاعدة البيانات -->
<div class="modal fade" id="importDatabaseModal" tabindex="-1" aria-labelledby="importDatabaseModalLabel"
    aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title" id="importDatabaseModalLabel">
                    <i class="fas fa-upload me-2"></i>استيراد قاعدة بيانات
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('import_database') }}" method="post" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تحذير:</strong> سيتم إنشاء نسخة احتياطية من قاعدة البيانات الحالية قبل الاستيراد.
                    </div>

                    <div class="mb-3">
                        <label for="database_file" class="form-label">اختر ملف قاعدة البيانات (.db)</label>
                        <input type="file" class="form-control" id="database_file" name="database_file" accept=".db"
                            required>
                        <div class="form-text">يجب أن يكون الملف من نوع .db</div>
                    </div>

                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="confirmImport" required>
                        <label class="form-check-label" for="confirmImport">
                            أؤكد أنني أريد استيراد قاعدة البيانات الجديدة واستبدال الحالية
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-upload me-2"></i>استيراد
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal لعرض النسخ الاحتياطية -->
<div class="modal fade" id="backupsModal" tabindex="-1" aria-labelledby="backupsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="backupsModalLabel">
                    <i class="fas fa-list me-2"></i>النسخ الاحتياطية المتاحة
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                    aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="backupsLoading" class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p class="mt-2">جاري تحميل النسخ الاحتياطية...</p>
                </div>

                <div id="backupsContent" style="display: none;">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>اسم الملف</th>
                                    <th>الحجم (MB)</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>آخر تعديل</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="backupsTableBody">
                            </tbody>
                        </table>
                    </div>

                    <div id="noBackupsMessage" class="text-center text-muted" style="display: none;">
                        <i class="fas fa-folder-open fa-3x mb-3"></i>
                        <p>لا توجد نسخ احتياطية متاحة</p>
                    </div>
                </div>

                <div id="backupsError" class="alert alert-danger" style="display: none;">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    حدث خطأ أثناء تحميل النسخ الاحتياطية
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="loadBackups()">
                    <i class="fas fa-sync-alt me-2"></i>تحديث
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    // تحميل النسخ الاحتياطية
    function loadBackups() {
        document.getElementById('backupsLoading').style.display = 'block';
        document.getElementById('backupsContent').style.display = 'none';
        document.getElementById('backupsError').style.display = 'none';

        fetch('{{ url_for("list_backups") }}')
            .then(response => response.json())
            .then(data => {
                document.getElementById('backupsLoading').style.display = 'none';

                if (data.error) {
                    document.getElementById('backupsError').style.display = 'block';
                    return;
                }

                document.getElementById('backupsContent').style.display = 'block';

                const tbody = document.getElementById('backupsTableBody');
                tbody.innerHTML = '';

                if (data.backups.length === 0) {
                    document.getElementById('noBackupsMessage').style.display = 'block';
                    return;
                }

                document.getElementById('noBackupsMessage').style.display = 'none';

                data.backups.forEach(backup => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                    <td>${backup.filename}</td>
                    <td>${backup.size}</td>
                    <td>${backup.created}</td>
                    <td>${backup.modified}</td>
                    <td>
                        <div class="btn-group btn-group-sm" role="group">
                            <a href="{{ url_for('download_backup', filename='') }}${backup.filename}"
                               class="btn btn-outline-primary" title="تحميل">
                                <i class="fas fa-download"></i>
                            </a>
                            <button type="button" class="btn btn-outline-danger"
                                    onclick="deleteBackup('${backup.filename}')" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                `;
                    tbody.appendChild(row);
                });
            })
            .catch(error => {
                document.getElementById('backupsLoading').style.display = 'none';
                document.getElementById('backupsError').style.display = 'block';
                console.error('Error:', error);
            });
    }

    // حذف نسخة احتياطية
    function deleteBackup(filename) {
        if (!confirm(`هل أنت متأكد من حذف النسخة الاحتياطية: ${filename}؟`)) {
            return;
        }

        fetch(`{{ url_for('delete_backup', filename='') }}${filename}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    loadBackups(); // إعادة تحميل القائمة
                } else {
                    alert('خطأ: ' + data.message);
                }
            })
            .catch(error => {
                alert('حدث خطأ أثناء حذف الملف');
                console.error('Error:', error);
            });
    }

    // تحميل النسخ الاحتياطية عند فتح الـ modal
    document.getElementById('backupsModal').addEventListener('shown.bs.modal', function () {
        loadBackups();
    });
</script>
{% endblock %}