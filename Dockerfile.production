# Dockerfile للبيئة الإنتاجية - Ta9affi
FROM python:3.11-slim

# تعيين متغيرات البيئة
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    FLASK_ENV=production \
    DEBIAN_FRONTEND=noninteractive

# تعيين مجلد العمل
WORKDIR /app

# تثبيت متطلبات النظام
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libpq-dev \
    postgresql-client \
    curl \
    wget \
    git \
    libmagic1 \
    libmagic-dev \
    libjpeg-dev \
    libpng-dev \
    libwebp-dev \
    libfreetype6-dev \
    liblcms2-dev \
    libopenjp2-7-dev \
    libtiff5-dev \
    libffi-dev \
    libssl-dev \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# إنشاء مستخدم غير جذر
RUN groupadd -r ta9affi && useradd -r -g ta9affi ta9affi

# نسخ ملفات المتطلبات
COPY requirements.txt requirements-production.txt ./

# تثبيت المتطلبات Python
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt && \
    pip install --no-cache-dir -r requirements-production.txt

# نسخ الكود المصدري
COPY . .

# إنشاء المجلدات المطلوبة
RUN mkdir -p uploads logs backups static/optimized && \
    chown -R ta9affi:ta9affi /app

# تحسين الملفات الثابتة
RUN python -c "
from frontend_optimizer import optimize_frontend_assets
from flask import Flask
app = Flask(__name__)
app.config['SECRET_KEY'] = 'temp-key'
app.static_folder = 'static'
with app.app_context():
    optimize_frontend_assets(app)
"

# ضغط الملفات الثابتة
RUN find static -name "*.css" -exec gzip -k {} \; && \
    find static -name "*.js" -exec gzip -k {} \; && \
    find static -name "*.html" -exec gzip -k {} \;

# تعيين الصلاحيات
RUN chmod +x *.sh 2>/dev/null || true && \
    chmod +x *.py

# التبديل للمستخدم غير الجذر
USER ta9affi

# فحص الصحة
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# تعريض المنفذ
EXPOSE 8000

# الأمر الافتراضي
CMD ["gunicorn", "--config", "gunicorn_config.py", "app_postgresql:app"]


