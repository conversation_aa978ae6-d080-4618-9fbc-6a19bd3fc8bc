#!/usr/bin/env python3
"""
سكريبت تشغيل Ta9affi في وضع Production
"""

import os
import sys
from dotenv import load_dotenv

def setup_production_environment():
    """إعداد بيئة Production"""
    
    # تحميل متغيرات البيئة من ملف .env إذا كان موجوداً
    if os.path.exists('.env'):
        load_dotenv('.env')
        print("✅ تم تحميل متغيرات البيئة من ملف .env")
    else:
        print("⚠️ ملف .env غير موجود، سيتم استخدام متغيرات البيئة النظام")
    
    # تعيين وضع Production
    os.environ['PRODUCTION_MODE'] = 'true'
    
    # التحقق من المتغيرات المطلوبة
    required_vars = [
        'SECRET_KEY',
        'SERVER_IP'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.environ.get(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ متغيرات البيئة المطلوبة مفقودة: {', '.join(missing_vars)}")
        print("يرجى تعيين هذه المتغيرات أو إنشاء ملف .env")
        sys.exit(1)
    
    # التحقق من إعدادات قاعدة البيانات
    database_url = os.environ.get('DATABASE_URL')
    if not database_url:
        # التحقق من المتغيرات المنفصلة
        db_vars = ['DB_HOST', 'DB_NAME', 'DB_USER', 'DB_PASSWORD']
        missing_db_vars = [var for var in db_vars if not os.environ.get(var)]
        
        if missing_db_vars:
            print(f"❌ إعدادات قاعدة البيانات مفقودة: {', '.join(missing_db_vars)}")
            print("يرجى تعيين DATABASE_URL أو المتغيرات المنفصلة للقاعدة")
            sys.exit(1)
    
    print("✅ جميع متغيرات البيئة المطلوبة متوفرة")
    return True

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء تشغيل Ta9affi في وضع Production")
    print("=" * 50)
    
    # إعداد البيئة
    setup_production_environment()
    
    # عرض معلومات التشغيل
    server_ip = os.environ.get('SERVER_IP', 'localhost')
    port = os.environ.get('PORT', '8000')
    
    print(f"📍 الخادم: {server_ip}")
    print(f"🔌 المنفذ: {port}")
    print(f"🌐 الرابط: http://{server_ip}:{port}")
    print(f"❤️ Health Check: http://{server_ip}:{port}/health")
    print("=" * 50)
    
    # استيراد وتشغيل التطبيق
    try:
        from app import app
        print("✅ تم تحميل التطبيق بنجاح")
        
        # تشغيل التطبيق
        app.run(
            host='0.0.0.0',
            port=int(port),
            debug=False,
            threaded=True
        )
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد التطبيق: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
