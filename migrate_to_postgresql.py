#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة ترحيل قاعدة البيانات من SQLite إلى PostgreSQL
مع الحفاظ على جميع البيانات والخصائص الحالية
"""

import os
import sqlite3
import psycopg2
import json
from datetime import datetime
import shutil
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import pandas as pd

class DatabaseMigrator:
    """فئة ترحيل قاعدة البيانات مع الحفاظ على جميع البيانات"""
    
    def __init__(self, sqlite_path, postgres_config):
        self.sqlite_path = sqlite_path
        self.postgres_config = postgres_config
        self.backup_dir = "migration_backup"
        self.migration_log = []
        
    def log_message(self, message):
        """تسجيل رسائل الترحيل"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.migration_log.append(log_entry)
        print(log_entry)
    
    def create_backup(self):
        """إنشاء نسخة احتياطية من قاعدة البيانات الحالية"""
        try:
            os.makedirs(self.backup_dir, exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = os.path.join(self.backup_dir, f"ta9affi_backup_{timestamp}.db")
            
            shutil.copy2(self.sqlite_path, backup_path)
            self.log_message(f"✅ تم إنشاء نسخة احتياطية: {backup_path}")
            return backup_path
        except Exception as e:
            self.log_message(f"❌ خطأ في إنشاء النسخة الاحتياطية: {str(e)}")
            raise
    
    def analyze_sqlite_schema(self):
        """تحليل هيكل قاعدة البيانات الحالية"""
        try:
            conn = sqlite3.connect(self.sqlite_path)
            cursor = conn.cursor()
            
            # الحصول على قائمة الجداول
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            schema_info = {}
            for table in tables:
                # الحصول على هيكل كل جدول
                cursor.execute(f"PRAGMA table_info({table})")
                columns = cursor.fetchall()
                
                # الحصول على الفهارس
                cursor.execute(f"PRAGMA index_list({table})")
                indexes = cursor.fetchall()
                
                # عدد السجلات
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                row_count = cursor.fetchone()[0]
                
                schema_info[table] = {
                    'columns': columns,
                    'indexes': indexes,
                    'row_count': row_count
                }
                
                self.log_message(f"📊 جدول {table}: {row_count} سجل")
            
            conn.close()
            return schema_info
        except Exception as e:
            self.log_message(f"❌ خطأ في تحليل هيكل قاعدة البيانات: {str(e)}")
            raise
    
    def test_postgresql_connection(self):
        """اختبار الاتصال بـ PostgreSQL"""
        try:
            conn = psycopg2.connect(**self.postgres_config)
            cursor = conn.cursor()
            cursor.execute("SELECT version()")
            version = cursor.fetchone()[0]
            self.log_message(f"✅ اتصال ناجح بـ PostgreSQL: {version}")
            conn.close()
            return True
        except Exception as e:
            self.log_message(f"❌ فشل الاتصال بـ PostgreSQL: {str(e)}")
            return False
    
    def create_postgresql_database(self):
        """إنشاء قاعدة البيانات في PostgreSQL"""
        try:
            # الاتصال بقاعدة البيانات الافتراضية لإنشاء قاعدة البيانات الجديدة
            temp_config = self.postgres_config.copy()
            db_name = temp_config.pop('database')
            temp_config['database'] = 'postgres'
            
            conn = psycopg2.connect(**temp_config)
            conn.autocommit = True
            cursor = conn.cursor()
            
            # التحقق من وجود قاعدة البيانات
            cursor.execute("SELECT 1 FROM pg_database WHERE datname = %s", (db_name,))
            if cursor.fetchone():
                self.log_message(f"⚠️ قاعدة البيانات {db_name} موجودة بالفعل")
            else:
                cursor.execute(f"CREATE DATABASE {db_name}")
                self.log_message(f"✅ تم إنشاء قاعدة البيانات {db_name}")
            
            conn.close()
            return True
        except Exception as e:
            self.log_message(f"❌ خطأ في إنشاء قاعدة البيانات: {str(e)}")
            return False
    
    def export_sqlite_data(self, schema_info):
        """تصدير البيانات من SQLite"""
        try:
            conn = sqlite3.connect(self.sqlite_path)
            exported_data = {}
            
            for table_name, table_info in schema_info.items():
                if table_info['row_count'] > 0:
                    # تصدير البيانات كـ DataFrame
                    df = pd.read_sql_query(f"SELECT * FROM {table_name}", conn)
                    exported_data[table_name] = df
                    self.log_message(f"📤 تم تصدير {len(df)} سجل من جدول {table_name}")
                else:
                    exported_data[table_name] = pd.DataFrame()
                    self.log_message(f"📤 جدول {table_name} فارغ")
            
            conn.close()
            return exported_data
        except Exception as e:
            self.log_message(f"❌ خطأ في تصدير البيانات: {str(e)}")
            raise
    
    def convert_sqlite_to_postgresql_types(self, sqlite_type):
        """تحويل أنواع البيانات من SQLite إلى PostgreSQL"""
        type_mapping = {
            'INTEGER': 'INTEGER',
            'TEXT': 'TEXT',
            'REAL': 'REAL',
            'BLOB': 'BYTEA',
            'NUMERIC': 'NUMERIC',
            'VARCHAR': 'VARCHAR',
            'CHAR': 'CHAR',
            'BOOLEAN': 'BOOLEAN',
            'DATE': 'DATE',
            'DATETIME': 'TIMESTAMP',
            'TIME': 'TIME'
        }
        
        # تحويل النوع إلى أحرف كبيرة للمقارنة
        sqlite_type_upper = sqlite_type.upper()
        
        # البحث عن تطابق جزئي
        for sqlite_key, postgres_type in type_mapping.items():
            if sqlite_key in sqlite_type_upper:
                return postgres_type
        
        # إذا لم نجد تطابق، نستخدم TEXT كافتراضي
        return 'TEXT'
    
    def save_migration_log(self):
        """حفظ سجل الترحيل"""
        try:
            log_file = os.path.join(self.backup_dir, f"migration_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt")
            with open(log_file, 'w', encoding='utf-8') as f:
                f.write("سجل ترحيل قاعدة البيانات من SQLite إلى PostgreSQL\n")
                f.write("=" * 60 + "\n\n")
                for log_entry in self.migration_log:
                    f.write(log_entry + "\n")
            
            self.log_message(f"📝 تم حفظ سجل الترحيل: {log_file}")
        except Exception as e:
            self.log_message(f"❌ خطأ في حفظ سجل الترحيل: {str(e)}")

def main():
    """الدالة الرئيسية للترحيل"""
    print("🚀 بدء عملية ترحيل قاعدة البيانات من SQLite إلى PostgreSQL")
    print("=" * 60)
    
    # إعدادات قاعدة البيانات
    sqlite_path = "instance/ta9affi.db"
    postgres_config = {
        'host': 'localhost',
        'port': 5432,
        'database': 'ta9affi',
        'user': 'ta9affi_user',
        'password': 'ta9affi_password'
    }
    
    # التحقق من وجود قاعدة البيانات الحالية
    if not os.path.exists(sqlite_path):
        print(f"❌ لم يتم العثور على قاعدة البيانات: {sqlite_path}")
        return False
    
    # إنشاء مثيل المرحل
    migrator = DatabaseMigrator(sqlite_path, postgres_config)
    
    try:
        # 1. إنشاء نسخة احتياطية
        migrator.create_backup()
        
        # 2. تحليل هيكل قاعدة البيانات الحالية
        schema_info = migrator.analyze_sqlite_schema()
        
        # 3. اختبار الاتصال بـ PostgreSQL
        if not migrator.test_postgresql_connection():
            print("❌ فشل في الاتصال بـ PostgreSQL. تأكد من تشغيل الخادم وصحة الإعدادات.")
            return False
        
        # 4. إنشاء قاعدة البيانات
        if not migrator.create_postgresql_database():
            return False
        
        # 5. تصدير البيانات
        exported_data = migrator.export_sqlite_data(schema_info)
        
        # 6. حفظ سجل الترحيل
        migrator.save_migration_log()
        
        print("\n✅ تم الانتهاء من المرحلة الأولى من الترحيل بنجاح!")
        print("📋 الخطوات التالية:")
        print("   1. تحديث إعدادات Flask لاستخدام PostgreSQL")
        print("   2. تشغيل Flask-Migrate لإنشاء الجداول")
        print("   3. استيراد البيانات المصدرة")
        
        return True
        
    except Exception as e:
        migrator.log_message(f"❌ خطأ عام في عملية الترحيل: {str(e)}")
        migrator.save_migration_log()
        return False

if __name__ == "__main__":
    main()
