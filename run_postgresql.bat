@echo off
echo ========================================
echo    Ta9affi - PostgreSQL Version
echo ========================================
echo.

REM Check if virtual environment exists
if not exist venv (
    echo Creating virtual environment...
    python -m venv venv
    if errorlevel 1 (
        echo Error: Failed to create virtual environment
        pause
        exit /b 1
    )
)

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate
if errorlevel 1 (
    echo Error: Failed to activate virtual environment
    pause
    exit /b 1
)

REM Install/update dependencies
echo Installing dependencies...
pip install -r requirements.txt
if errorlevel 1 (
    echo Warning: Some packages might not be installed
    echo Continuing anyway...
)

REM Check PostgreSQL connection
echo.
echo Checking PostgreSQL connection...
python -c "import psycopg2; print('PostgreSQL driver available')" 2>nul
if errorlevel 1 (
    echo Warning: PostgreSQL driver not available
    echo Installing psycopg2-binary...
    pip install psycopg2-binary
)

REM Set environment variables
echo Setting environment variables...
set FLASK_APP=app_postgresql.py
set FLASK_ENV=development

REM Check if migration is needed
echo.
echo Checking database status...
if not exist migrations (
    echo Initializing Flask-Migrate...
    flask db init
    if errorlevel 1 (
        echo Error: Failed to initialize Flask-Migrate
        echo Make sure PostgreSQL is running and configured correctly
        pause
        exit /b 1
    )
    
    echo Creating initial migration...
    flask db migrate -m "Initial migration"
    if errorlevel 1 (
        echo Error: Failed to create migration
        pause
        exit /b 1
    )
    
    echo Applying migration...
    flask db upgrade
    if errorlevel 1 (
        echo Error: Failed to apply migration
        pause
        exit /b 1
    )
) else (
    echo Checking for pending migrations...
    flask db upgrade
    if errorlevel 1 (
        echo Warning: Migration failed, continuing anyway...
    )
)

REM Start the application
echo.
echo ========================================
echo Starting Ta9affi with PostgreSQL...
echo ========================================
echo.
echo Application will be available at:
echo http://localhost:5000
echo.
echo Press Ctrl+C to stop the application
echo.

python app_postgresql.py

echo.
echo Application stopped.
pause
