# Docker Compose للبيئة الإنتاجية - Ta9affi
version: '3.8'

services:
  # تطبيق Ta9affi الرئيسي
  ta9affi-app:
    build:
      context: .
      dockerfile: Dockerfile.production
    container_name: ta9affi-app
    restart: unless-stopped
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=postgresql://ta9affi_user:${DB_PASSWORD}@postgres:5432/ta9affi
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
      - MAIL_SERVER=${MAIL_SERVER}
      - MAIL_PORT=${MAIL_PORT}
      - MAIL_USERNAME=${MAIL_USERNAME}
      - MAIL_PASSWORD=${MAIL_PASSWORD}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_BACKUP_BUCKET=${AWS_BACKUP_BUCKET}
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
      - ./backups:/app/backups
    depends_on:
      - postgres
      - redis
    networks:
      - ta9affi-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # خادم Nginx
  nginx:
    image: nginx:alpine
    container_name: ta9affi-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/sites-available:/etc/nginx/sites-available:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./static:/var/www/static:ro
      - ./uploads:/var/www/uploads:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - ta9affi-app
    networks:
      - ta9affi-network
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

  # قاعدة البيانات PostgreSQL
  postgres:
    image: postgres:15-alpine
    container_name: ta9affi-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=ta9affi
      - POSTGRES_USER=ta9affi_user
      - POSTGRES_PASSWORD=${DB_PASSWORD}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d:ro
      - ./backups:/backups
    networks:
      - ta9affi-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ta9affi_user -d ta9affi"]
      interval: 30s
      timeout: 10s
      retries: 3
    command: >
      postgres
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100
      -c random_page_cost=1.1
      -c effective_io_concurrency=200
      -c work_mem=4MB
      -c min_wal_size=1GB
      -c max_wal_size=4GB

  # Redis للتخزين المؤقت والجلسات
  redis:
    image: redis:7-alpine
    container_name: ta9affi-redis
    restart: unless-stopped
    command: >
      redis-server
      --appendonly yes
      --appendfsync everysec
      --maxmemory 512mb
      --maxmemory-policy allkeys-lru
      --tcp-keepalive 60
      --timeout 300
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/etc/redis/redis.conf:ro
    networks:
      - ta9affi-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery Worker للمهام الخلفية
  celery-worker:
    build:
      context: .
      dockerfile: Dockerfile.production
    container_name: ta9affi-celery-worker
    restart: unless-stopped
    command: celery -A app.celery worker --loglevel=info --concurrency=4
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=postgresql://ta9affi_user:${DB_PASSWORD}@postgres:5432/ta9affi
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
      - ./backups:/app/backups
    depends_on:
      - postgres
      - redis
    networks:
      - ta9affi-network
    healthcheck:
      test: ["CMD", "celery", "-A", "app.celery", "inspect", "ping"]
      interval: 60s
      timeout: 10s
      retries: 3

  # Celery Beat للمهام المجدولة
  celery-beat:
    build:
      context: .
      dockerfile: Dockerfile.production
    container_name: ta9affi-celery-beat
    restart: unless-stopped
    command: celery -A app.celery beat --loglevel=info
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=postgresql://ta9affi_user:${DB_PASSWORD}@postgres:5432/ta9affi
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
      - ./backups:/app/backups
    depends_on:
      - postgres
      - redis
    networks:
      - ta9affi-network

  # مراقبة النظام مع Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: ta9affi-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - ta9affi-network

  # Grafana للوحات المراقبة
  grafana:
    image: grafana/grafana:latest
    container_name: ta9affi-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    networks:
      - ta9affi-network

  # Node Exporter لمراقبة النظام
  node-exporter:
    image: prom/node-exporter:latest
    container_name: ta9affi-node-exporter
    restart: unless-stopped
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    networks:
      - ta9affi-network

  # Elasticsearch للبحث والسجلات
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: ta9affi-elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - ES_JAVA_OPTS=-Xms512m -Xmx512m
      - xpack.security.enabled=false
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - ta9affi-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Kibana لتحليل السجلات
  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: ta9affi-kibana
    restart: unless-stopped
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    networks:
      - ta9affi-network

  # Logstash لمعالجة السجلات
  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    container_name: ta9affi-logstash
    restart: unless-stopped
    volumes:
      - ./monitoring/logstash/pipeline:/usr/share/logstash/pipeline:ro
      - ./logs:/logs:ro
    depends_on:
      - elasticsearch
    networks:
      - ta9affi-network

  # Backup Service
  backup-service:
    build:
      context: .
      dockerfile: Dockerfile.backup
    container_name: ta9affi-backup
    restart: unless-stopped
    environment:
      - DATABASE_URL=postgresql://ta9affi_user:${DB_PASSWORD}@postgres:5432/ta9affi
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_BACKUP_BUCKET=${AWS_BACKUP_BUCKET}
    volumes:
      - ./backups:/app/backups
      - ./uploads:/app/uploads:ro
      - ./logs:/app/logs:ro
    depends_on:
      - postgres
    networks:
      - ta9affi-network
    command: >
      sh -c "
        echo '0 2 * * * python /app/backup_manager.py --all' | crontab - &&
        echo '0 5 * * * python /app/backup_manager.py --cleanup' | crontab - &&
        crond -f
      "

# الشبكات
networks:
  ta9affi-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# الأحجام المستمرة
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  elasticsearch_data:
    driver: local
