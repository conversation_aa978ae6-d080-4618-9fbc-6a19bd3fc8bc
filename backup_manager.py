#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير النسخ الاحتياطي التلقائي لتطبيق Ta9affi
يوفر نسخ احتياطي شامل لقاعدة البيانات والملفات والإعدادات
"""

import os
import shutil
import subprocess
import gzip
import tarfile
import logging
import json
import schedule
import time
from datetime import datetime, timedelta
from pathlib import Path
from threading import Thread
import boto3
from botocore.exceptions import ClientError

class BackupManager:
    """مدير النسخ الاحتياطي"""
    
    def __init__(self, app=None):
        self.app = app
        self.backup_dir = "backups"
        self.is_running = False
        self.backup_configs = {}
        
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """تهيئة مدير النسخ الاحتياطي"""
        self.app = app
        
        # إعدادات النسخ الاحتياطي
        self.backup_dir = app.config.get('BACKUP_DIR', 'backups')
        self.max_local_backups = app.config.get('MAX_LOCAL_BACKUPS', 7)
        self.max_remote_backups = app.config.get('MAX_REMOTE_BACKUPS', 30)
        
        # إعدادات قاعدة البيانات
        self.db_config = {
            'host': app.config.get('DB_HOST', 'localhost'),
            'port': app.config.get('DB_PORT', 5432),
            'database': app.config.get('DB_NAME', 'ta9affi'),
            'username': app.config.get('DB_USER', 'ta9affi_user'),
            'password': app.config.get('DB_PASSWORD', '')
        }
        
        # إعدادات التخزين السحابي (اختياري)
        self.cloud_config = {
            'provider': app.config.get('BACKUP_CLOUD_PROVIDER', None),  # 'aws', 'google', 'azure'
            'aws_access_key': app.config.get('AWS_ACCESS_KEY_ID', ''),
            'aws_secret_key': app.config.get('AWS_SECRET_ACCESS_KEY', ''),
            'aws_bucket': app.config.get('AWS_BACKUP_BUCKET', ''),
            'aws_region': app.config.get('AWS_REGION', 'us-east-1')
        }
        
        # إنشاء مجلد النسخ الاحتياطي
        os.makedirs(self.backup_dir, exist_ok=True)
        
        # تحميل إعدادات النسخ الاحتياطي
        self.load_backup_configs()
        
        # بدء جدولة النسخ الاحتياطي
        self.start_backup_scheduler()
        
        logging.info("✅ تم تهيئة مدير النسخ الاحتياطي")
    
    def load_backup_configs(self):
        """تحميل إعدادات النسخ الاحتياطي"""
        self.backup_configs = {
            'database': {
                'enabled': True,
                'schedule': 'daily',
                'time': '02:00',
                'retention_days': 30,
                'compress': True
            },
            'files': {
                'enabled': True,
                'schedule': 'daily',
                'time': '03:00',
                'retention_days': 7,
                'compress': True,
                'include_paths': ['uploads', 'static', 'logs'],
                'exclude_patterns': ['*.tmp', '*.log', '__pycache__']
            },
            'config': {
                'enabled': True,
                'schedule': 'weekly',
                'time': '04:00',
                'retention_days': 30,
                'compress': True,
                'include_files': ['config.py', 'requirements.txt', '.env']
            }
        }
    
    def start_backup_scheduler(self):
        """بدء جدولة النسخ الاحتياطي"""
        if not self.is_running:
            self.is_running = True
            
            # جدولة النسخ الاحتياطي
            self.schedule_backups()
            
            # بدء خيط الجدولة
            scheduler_thread = Thread(target=self._scheduler_loop, daemon=True)
            scheduler_thread.start()
    
    def schedule_backups(self):
        """جدولة النسخ الاحتياطي"""
        # نسخ احتياطي لقاعدة البيانات
        db_config = self.backup_configs.get('database', {})
        if db_config.get('enabled', False):
            if db_config.get('schedule') == 'daily':
                schedule.every().day.at(db_config.get('time', '02:00')).do(self.backup_database)
            elif db_config.get('schedule') == 'hourly':
                schedule.every().hour.do(self.backup_database)
        
        # نسخ احتياطي للملفات
        files_config = self.backup_configs.get('files', {})
        if files_config.get('enabled', False):
            if files_config.get('schedule') == 'daily':
                schedule.every().day.at(files_config.get('time', '03:00')).do(self.backup_files)
        
        # نسخ احتياطي للإعدادات
        config_backup = self.backup_configs.get('config', {})
        if config_backup.get('enabled', False):
            if config_backup.get('schedule') == 'weekly':
                schedule.every().week.at(config_backup.get('time', '04:00')).do(self.backup_config)
        
        # تنظيف النسخ القديمة يومياً
        schedule.every().day.at("05:00").do(self.cleanup_old_backups)
    
    def _scheduler_loop(self):
        """حلقة الجدولة"""
        while self.is_running:
            try:
                schedule.run_pending()
                time.sleep(60)  # فحص كل دقيقة
            except Exception as e:
                logging.error(f"Backup scheduler error: {str(e)}")
                time.sleep(300)  # انتظار أطول في حالة الخطأ
    
    def backup_database(self):
        """نسخ احتياطي لقاعدة البيانات"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"database_backup_{timestamp}.sql"
            backup_path = os.path.join(self.backup_dir, backup_filename)
            
            # أمر pg_dump
            cmd = [
                'pg_dump',
                '-h', self.db_config['host'],
                '-p', str(self.db_config['port']),
                '-U', self.db_config['username'],
                '-d', self.db_config['database'],
                '-f', backup_path,
                '--verbose',
                '--no-password'
            ]
            
            # تعيين متغير البيئة لكلمة المرور
            env = os.environ.copy()
            env['PGPASSWORD'] = self.db_config['password']
            
            # تنفيذ الأمر
            result = subprocess.run(cmd, env=env, capture_output=True, text=True)
            
            if result.returncode == 0:
                # ضغط الملف إذا كان مطلوباً
                if self.backup_configs.get('database', {}).get('compress', True):
                    compressed_path = f"{backup_path}.gz"
                    with open(backup_path, 'rb') as f_in:
                        with gzip.open(compressed_path, 'wb') as f_out:
                            shutil.copyfileobj(f_in, f_out)
                    
                    # حذف الملف غير المضغوط
                    os.remove(backup_path)
                    backup_path = compressed_path
                
                # حفظ معلومات النسخة الاحتياطية
                backup_info = {
                    'type': 'database',
                    'filename': os.path.basename(backup_path),
                    'size': os.path.getsize(backup_path),
                    'created_at': datetime.now().isoformat(),
                    'compressed': self.backup_configs.get('database', {}).get('compress', True)
                }
                
                self.save_backup_info(backup_info)
                
                # رفع للتخزين السحابي إذا كان متاحاً
                if self.cloud_config.get('provider'):
                    self.upload_to_cloud(backup_path, 'database')
                
                logging.info(f"✅ تم إنشاء نسخة احتياطية لقاعدة البيانات: {backup_path}")
                return backup_path
                
            else:
                logging.error(f"❌ فشل في إنشاء نسخة احتياطية لقاعدة البيانات: {result.stderr}")
                return None
                
        except Exception as e:
            logging.error(f"❌ خطأ في نسخ قاعدة البيانات: {str(e)}")
            return None
    
    def backup_files(self):
        """نسخ احتياطي للملفات"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"files_backup_{timestamp}.tar"
            backup_path = os.path.join(self.backup_dir, backup_filename)
            
            files_config = self.backup_configs.get('files', {})
            include_paths = files_config.get('include_paths', ['uploads'])
            exclude_patterns = files_config.get('exclude_patterns', [])
            
            # إنشاء أرشيف tar
            with tarfile.open(backup_path, 'w') as tar:
                for path in include_paths:
                    if os.path.exists(path):
                        # إضافة فلتر للاستثناءات
                        def exclude_filter(tarinfo):
                            for pattern in exclude_patterns:
                                if pattern in tarinfo.name:
                                    return None
                            return tarinfo
                        
                        tar.add(path, arcname=path, filter=exclude_filter)
            
            # ضغط الأرشيف إذا كان مطلوباً
            if files_config.get('compress', True):
                compressed_path = f"{backup_path}.gz"
                with open(backup_path, 'rb') as f_in:
                    with gzip.open(compressed_path, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)
                
                os.remove(backup_path)
                backup_path = compressed_path
            
            # حفظ معلومات النسخة الاحتياطية
            backup_info = {
                'type': 'files',
                'filename': os.path.basename(backup_path),
                'size': os.path.getsize(backup_path),
                'created_at': datetime.now().isoformat(),
                'compressed': files_config.get('compress', True),
                'included_paths': include_paths
            }
            
            self.save_backup_info(backup_info)
            
            # رفع للتخزين السحابي
            if self.cloud_config.get('provider'):
                self.upload_to_cloud(backup_path, 'files')
            
            logging.info(f"✅ تم إنشاء نسخة احتياطية للملفات: {backup_path}")
            return backup_path
            
        except Exception as e:
            logging.error(f"❌ خطأ في نسخ الملفات: {str(e)}")
            return None
    
    def backup_config(self):
        """نسخ احتياطي للإعدادات"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"config_backup_{timestamp}.tar.gz"
            backup_path = os.path.join(self.backup_dir, backup_filename)
            
            config_backup = self.backup_configs.get('config', {})
            include_files = config_backup.get('include_files', [])
            
            # إنشاء أرشيف مضغوط
            with tarfile.open(backup_path, 'w:gz') as tar:
                for file_path in include_files:
                    if os.path.exists(file_path):
                        tar.add(file_path, arcname=os.path.basename(file_path))
                
                # إضافة معلومات النظام
                system_info = {
                    'backup_date': datetime.now().isoformat(),
                    'python_version': os.sys.version,
                    'app_version': getattr(self.app, 'version', 'unknown'),
                    'backup_configs': self.backup_configs
                }
                
                # حفظ معلومات النظام في ملف مؤقت
                temp_info_file = 'temp_system_info.json'
                with open(temp_info_file, 'w', encoding='utf-8') as f:
                    json.dump(system_info, f, ensure_ascii=False, indent=2)
                
                tar.add(temp_info_file, arcname='system_info.json')
                os.remove(temp_info_file)
            
            # حفظ معلومات النسخة الاحتياطية
            backup_info = {
                'type': 'config',
                'filename': os.path.basename(backup_path),
                'size': os.path.getsize(backup_path),
                'created_at': datetime.now().isoformat(),
                'compressed': True,
                'included_files': include_files
            }
            
            self.save_backup_info(backup_info)
            
            # رفع للتخزين السحابي
            if self.cloud_config.get('provider'):
                self.upload_to_cloud(backup_path, 'config')
            
            logging.info(f"✅ تم إنشاء نسخة احتياطية للإعدادات: {backup_path}")
            return backup_path
            
        except Exception as e:
            logging.error(f"❌ خطأ في نسخ الإعدادات: {str(e)}")
            return None
    
    def save_backup_info(self, backup_info):
        """حفظ معلومات النسخة الاحتياطية"""
        try:
            info_file = os.path.join(self.backup_dir, 'backup_info.json')
            
            # تحميل المعلومات الموجودة
            if os.path.exists(info_file):
                with open(info_file, 'r', encoding='utf-8') as f:
                    all_backups = json.load(f)
            else:
                all_backups = []
            
            # إضافة النسخة الجديدة
            all_backups.append(backup_info)
            
            # حفظ المعلومات المحدثة
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(all_backups, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logging.error(f"Error saving backup info: {str(e)}")
    
    def upload_to_cloud(self, file_path, backup_type):
        """رفع النسخة الاحتياطية للتخزين السحابي"""
        if self.cloud_config.get('provider') == 'aws':
            return self.upload_to_aws(file_path, backup_type)
        
        # يمكن إضافة مزودين آخرين هنا
        return False
    
    def upload_to_aws(self, file_path, backup_type):
        """رفع للتخزين السحابي AWS S3"""
        try:
            if not all([
                self.cloud_config.get('aws_access_key'),
                self.cloud_config.get('aws_secret_key'),
                self.cloud_config.get('aws_bucket')
            ]):
                return False
            
            # إنشاء عميل S3
            s3_client = boto3.client(
                's3',
                aws_access_key_id=self.cloud_config['aws_access_key'],
                aws_secret_access_key=self.cloud_config['aws_secret_key'],
                region_name=self.cloud_config['aws_region']
            )
            
            # تحديد مفتاح الكائن
            filename = os.path.basename(file_path)
            s3_key = f"ta9affi-backups/{backup_type}/{filename}"
            
            # رفع الملف
            s3_client.upload_file(file_path, self.cloud_config['aws_bucket'], s3_key)
            
            logging.info(f"✅ تم رفع النسخة الاحتياطية لـ AWS S3: {s3_key}")
            return True
            
        except ClientError as e:
            logging.error(f"❌ خطأ في رفع النسخة الاحتياطية لـ AWS: {str(e)}")
            return False
        except Exception as e:
            logging.error(f"❌ خطأ عام في رفع النسخة الاحتياطية: {str(e)}")
            return False
    
    def cleanup_old_backups(self):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            current_time = datetime.now()
            
            # تنظيف النسخ المحلية
            for backup_type, config in self.backup_configs.items():
                retention_days = config.get('retention_days', 7)
                cutoff_date = current_time - timedelta(days=retention_days)
                
                # البحث عن الملفات القديمة
                pattern = f"{backup_type}_backup_"
                for filename in os.listdir(self.backup_dir):
                    if filename.startswith(pattern):
                        file_path = os.path.join(self.backup_dir, filename)
                        file_time = datetime.fromtimestamp(os.path.getctime(file_path))
                        
                        if file_time < cutoff_date:
                            os.remove(file_path)
                            logging.info(f"🗑️ تم حذف النسخة الاحتياطية القديمة: {filename}")
            
            # تنظيف النسخ السحابية (AWS)
            if self.cloud_config.get('provider') == 'aws':
                self.cleanup_aws_backups()
            
            logging.info("✅ تم تنظيف النسخ الاحتياطية القديمة")
            
        except Exception as e:
            logging.error(f"❌ خطأ في تنظيف النسخ الاحتياطية: {str(e)}")
    
    def cleanup_aws_backups(self):
        """تنظيف النسخ الاحتياطية القديمة من AWS"""
        try:
            s3_client = boto3.client(
                's3',
                aws_access_key_id=self.cloud_config['aws_access_key'],
                aws_secret_access_key=self.cloud_config['aws_secret_key'],
                region_name=self.cloud_config['aws_region']
            )
            
            # قائمة الكائنات
            response = s3_client.list_objects_v2(
                Bucket=self.cloud_config['aws_bucket'],
                Prefix='ta9affi-backups/'
            )
            
            if 'Contents' in response:
                current_time = datetime.now()
                cutoff_date = current_time - timedelta(days=self.max_remote_backups)
                
                for obj in response['Contents']:
                    if obj['LastModified'].replace(tzinfo=None) < cutoff_date:
                        s3_client.delete_object(
                            Bucket=self.cloud_config['aws_bucket'],
                            Key=obj['Key']
                        )
                        logging.info(f"🗑️ تم حذف النسخة الاحتياطية من AWS: {obj['Key']}")
            
        except Exception as e:
            logging.error(f"❌ خطأ في تنظيف النسخ الاحتياطية من AWS: {str(e)}")
    
    def restore_database(self, backup_file):
        """استعادة قاعدة البيانات من نسخة احتياطية"""
        try:
            backup_path = os.path.join(self.backup_dir, backup_file)
            
            if not os.path.exists(backup_path):
                return False, "ملف النسخة الاحتياطية غير موجود"
            
            # فك الضغط إذا لزم الأمر
            if backup_path.endswith('.gz'):
                uncompressed_path = backup_path[:-3]
                with gzip.open(backup_path, 'rb') as f_in:
                    with open(uncompressed_path, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)
                backup_path = uncompressed_path
            
            # أمر psql لاستعادة قاعدة البيانات
            cmd = [
                'psql',
                '-h', self.db_config['host'],
                '-p', str(self.db_config['port']),
                '-U', self.db_config['username'],
                '-d', self.db_config['database'],
                '-f', backup_path
            ]
            
            env = os.environ.copy()
            env['PGPASSWORD'] = self.db_config['password']
            
            result = subprocess.run(cmd, env=env, capture_output=True, text=True)
            
            # حذف الملف غير المضغوط إذا تم إنشاؤه
            if backup_file.endswith('.gz') and os.path.exists(backup_path):
                os.remove(backup_path)
            
            if result.returncode == 0:
                logging.info(f"✅ تم استعادة قاعدة البيانات من: {backup_file}")
                return True, "تم استعادة قاعدة البيانات بنجاح"
            else:
                logging.error(f"❌ فشل في استعادة قاعدة البيانات: {result.stderr}")
                return False, f"فشل في الاستعادة: {result.stderr}"
                
        except Exception as e:
            logging.error(f"❌ خطأ في استعادة قاعدة البيانات: {str(e)}")
            return False, f"خطأ في الاستعادة: {str(e)}"
    
    def get_backup_status(self):
        """الحصول على حالة النسخ الاحتياطي"""
        try:
            info_file = os.path.join(self.backup_dir, 'backup_info.json')
            
            if not os.path.exists(info_file):
                return {'backups': [], 'total_size': 0, 'last_backup': None}
            
            with open(info_file, 'r', encoding='utf-8') as f:
                all_backups = json.load(f)
            
            # حساب الحجم الإجمالي
            total_size = sum(backup.get('size', 0) for backup in all_backups)
            
            # آخر نسخة احتياطية
            last_backup = max(all_backups, key=lambda x: x.get('created_at', '')) if all_backups else None
            
            return {
                'backups': all_backups,
                'total_size': total_size,
                'last_backup': last_backup,
                'backup_count': len(all_backups)
            }
            
        except Exception as e:
            logging.error(f"Error getting backup status: {str(e)}")
            return {'backups': [], 'total_size': 0, 'last_backup': None}

# إنشاء مثيل عام لمدير النسخ الاحتياطي
backup_manager = BackupManager()

def main():
    """الدالة الرئيسية لتشغيل النسخ الاحتياطي"""
    import argparse

    parser = argparse.ArgumentParser(description="مدير النسخ الاحتياطي لتطبيق Ta9affi")
    parser.add_argument('--database', action='store_true', help='نسخ احتياطي لقاعدة البيانات')
    parser.add_argument('--files', action='store_true', help='نسخ احتياطي للملفات')
    parser.add_argument('--config', action='store_true', help='نسخ احتياطي للإعدادات')
    parser.add_argument('--all', action='store_true', help='نسخ احتياطي شامل')
    parser.add_argument('--cleanup', action='store_true', help='تنظيف النسخ القديمة')
    parser.add_argument('--status', action='store_true', help='عرض حالة النسخ الاحتياطي')
    parser.add_argument('--restore-db', help='استعادة قاعدة البيانات من ملف')

    args = parser.parse_args()

    # تهيئة مدير النسخ الاحتياطي
    from app_postgresql import app
    with app.app_context():
        backup_manager.init_app(app)

        if args.database or args.all:
            print("🗄️ بدء نسخ احتياطي لقاعدة البيانات...")
            result = backup_manager.backup_database()
            if result:
                print(f"✅ تم إنشاء نسخة احتياطية: {result}")
            else:
                print("❌ فشل في إنشاء نسخة احتياطية لقاعدة البيانات")

        if args.files or args.all:
            print("📁 بدء نسخ احتياطي للملفات...")
            result = backup_manager.backup_files()
            if result:
                print(f"✅ تم إنشاء نسخة احتياطية: {result}")
            else:
                print("❌ فشل في إنشاء نسخة احتياطية للملفات")

        if args.config or args.all:
            print("⚙️ بدء نسخ احتياطي للإعدادات...")
            result = backup_manager.backup_config()
            if result:
                print(f"✅ تم إنشاء نسخة احتياطية: {result}")
            else:
                print("❌ فشل في إنشاء نسخة احتياطية للإعدادات")

        if args.cleanup:
            print("🧹 بدء تنظيف النسخ القديمة...")
            backup_manager.cleanup_old_backups()
            print("✅ تم تنظيف النسخ القديمة")

        if args.status:
            print("📊 حالة النسخ الاحتياطي:")
            status = backup_manager.get_backup_status()
            print(f"   عدد النسخ: {status['backup_count']}")
            print(f"   الحجم الإجمالي: {status['total_size'] / (1024*1024):.2f} MB")
            if status['last_backup']:
                print(f"   آخر نسخة: {status['last_backup']['created_at']}")

        if args.restore_db:
            print(f"🔄 استعادة قاعدة البيانات من: {args.restore_db}")
            success, message = backup_manager.restore_database(args.restore_db)
            if success:
                print(f"✅ {message}")
            else:
                print(f"❌ {message}")

if __name__ == "__main__":
    main()
