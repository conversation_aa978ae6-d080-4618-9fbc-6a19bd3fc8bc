#!/usr/bin/env python3
"""
سكريپت لإنهاء العملية على المنفذ 8000 وتشغيل التطبيق
"""

import os
import sys
import subprocess
import time

def kill_process_on_port_8000():
    """إنهاء العملية التي تستخدم المنفذ 8000"""
    print("🔍 البحث عن العمليات التي تستخدم المنفذ 8000...")
    
    try:
        # محاولة باستخدام lsof
        result = subprocess.run(['lsof', '-ti', ':8000'], capture_output=True, text=True)
        if result.stdout.strip():
            pids = result.stdout.strip().split('\n')
            for pid in pids:
                try:
                    print(f"🔄 إنهاء العملية {pid}...")
                    subprocess.run(['kill', '-9', pid], check=True)
                    print(f"✅ تم إنهاء العملية {pid}")
                except Exception as e:
                    print(f"⚠️ فشل في إنهاء العملية {pid}: {str(e)}")
            return True
    except FileNotFoundError:
        print("⚠️ lsof غير متاح، محاولة باستخدام netstat...")
        
    try:
        # محاولة باستخدام netstat
        result = subprocess.run(['netstat', '-tlnp'], capture_output=True, text=True)
        lines = result.stdout.split('\n')
        for line in lines:
            if ':8000' in line and 'LISTEN' in line:
                parts = line.split()
                if len(parts) > 6:
                    pid_program = parts[6]
                    if '/' in pid_program:
                        pid = pid_program.split('/')[0]
                        try:
                            print(f"🔄 إنهاء العملية {pid}...")
                            subprocess.run(['kill', '-9', pid], check=True)
                            print(f"✅ تم إنهاء العملية {pid}")
                            return True
                        except Exception as e:
                            print(f"⚠️ فشل في إنهاء العملية {pid}: {str(e)}")
    except FileNotFoundError:
        print("⚠️ netstat غير متاح")
    
    # محاولة باستخدام fuser
    try:
        print("🔄 محاولة باستخدام fuser...")
        subprocess.run(['fuser', '-k', '8000/tcp'], check=True)
        print("✅ تم إنهاء العمليات باستخدام fuser")
        return True
    except:
        print("⚠️ fuser غير متاح أو فشل")
    
    return False

def main():
    """الدالة الرئيسية"""
    print("🔧 إنهاء العمليات على المنفذ 8000 وتشغيل Ta9affi")
    print("="*60)
    
    # إنهاء العمليات على المنفذ 8000
    if kill_process_on_port_8000():
        print("✅ تم تحرير المنفذ 8000")
        time.sleep(2)  # انتظار قصير للتأكد
    else:
        print("⚠️ لم يتم العثور على عمليات تستخدم المنفذ 8000 أو فشل في إنهائها")
    
    # تعيين متغيرات البيئة
    print("\n🔧 تعيين متغيرات البيئة...")
    os.environ['PRODUCTION_MODE'] = 'true'
    os.environ['SERVER_IP'] = '*************'
    os.environ['PORT'] = '8000'
    os.environ['SECRET_KEY'] = 'ta9affi-production-secret-key-2024'
    os.environ['DATABASE_URL'] = 'sqlite:///ta9affi_production.db'
    
    print("✅ تم تعيين متغيرات البيئة")
    
    print("\n🚀 تشغيل التطبيق...")
    print("🔗 الروابط:")
    print("   📱 التطبيق: http://*************:8000")
    print("   🔐 تسجيل الدخول: http://*************:8000/login")
    print("   📝 التسجيل: http://*************:8000/register")
    print("   ❤️ فحص الصحة: http://*************:8000/health")
    print("\n" + "="*60)
    print("⏹️ اضغط Ctrl+C لإيقاف التطبيق")
    print("="*60)
    
    try:
        # استيراد وتشغيل التطبيق
        from app import app
        
        # تشغيل التطبيق
        app.run(
            host='0.0.0.0',
            port=8000,
            debug=False,
            threaded=True,
            use_reloader=False
        )
        
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف التطبيق بواسطة المستخدم")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل التطبيق: {str(e)}")
        print("\n💡 نصائح:")
        print("1. تأكد من تثبيت المتطلبات: pip install -r requirements.txt")
        print("2. جرب تشغيل: python diagnose_server.py")
        print("3. جرب منفذ آخر: PORT=8001 python app.py")
        sys.exit(1)

if __name__ == '__main__':
    main()
