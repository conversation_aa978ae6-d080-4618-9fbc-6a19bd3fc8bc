"""
مجدول مهام الاشتراكات
"""

import schedule
import time
import threading
from datetime import datetime

class SubscriptionScheduler:
    """مجدول مهام الاشتراكات"""

    def __init__(self, app=None):
        self.running = False
        self.thread = None
        self.app = app
    
    def start(self):
        """بدء المجدول"""
        if not self.running:
            self.running = True
            
            # جدولة المهام
            schedule.every().day.at("09:00").do(self.check_expiring_subscriptions)
            schedule.every().day.at("21:00").do(self.check_expired_subscriptions)
            schedule.every().hour.do(self.cleanup_old_notifications)
            
            # بدء thread للمجدول
            self.thread = threading.Thread(target=self._run_scheduler, daemon=True)
            self.thread.start()
            
            print("Subscription scheduler started")
    
    def stop(self):
        """إيقاف المجدول"""
        self.running = False
        schedule.clear()
        print("Subscription scheduler stopped")
    
    def _run_scheduler(self):
        """تشغيل المجدول"""
        while self.running:
            try:
                schedule.run_pending()
                time.sleep(60)  # فحص كل دقيقة
            except Exception as e:
                print(f"Scheduler error: {str(e)}")
                time.sleep(60)
    
    def check_expiring_subscriptions(self):
        """فحص الاشتراكات التي تقارب على الانتهاء"""
        try:
            if self.app:
                with self.app.app_context():
                    from subscription_manager import subscription_manager
                    print(f"[{datetime.now()}] Checking expiring subscriptions...")
                    subscription_manager.check_expiring_subscriptions()
                    print("Expiring subscriptions check completed")
        except Exception as e:
            print(f"Error checking expiring subscriptions: {str(e)}")

    def check_expired_subscriptions(self):
        """فحص الاشتراكات المنتهية"""
        try:
            if self.app:
                with self.app.app_context():
                    from subscription_manager import subscription_manager
                    print(f"[{datetime.now()}] Checking expired subscriptions...")
                    subscription_manager.check_expired_subscriptions()
                    print("Expired subscriptions check completed")
        except Exception as e:
            print(f"Error checking expired subscriptions: {str(e)}")

    def cleanup_old_notifications(self):
        """تنظيف الإشعارات القديمة"""
        try:
            if self.app:
                with self.app.app_context():
                    from models_new import SubscriptionNotification, db
                    from datetime import timedelta

                    # حذف الإشعارات الأقدم من 30 يوم
                    cutoff_date = datetime.utcnow() - timedelta(days=30)
                    old_notifications = SubscriptionNotification.query.filter(
                        SubscriptionNotification.created_at < cutoff_date
                    ).all()

                    for notification in old_notifications:
                        db.session.delete(notification)

                    db.session.commit()

                    if old_notifications:
                        print(f"Cleaned up {len(old_notifications)} old notifications")

        except Exception as e:
            print(f"Error cleaning up notifications: {str(e)}")

# دالة لبدء المجدول مع التطبيق
def start_subscription_scheduler(app):
    """بدء مجدول الاشتراكات"""
    scheduler = SubscriptionScheduler(app)
    scheduler.start()
    return scheduler
