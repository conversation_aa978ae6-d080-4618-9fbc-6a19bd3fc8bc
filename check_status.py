#!/usr/bin/env python3
"""
سكريپت للتحقق من حالة Ta9affi في وضع Production
"""

import requests
import json
import sys
import os
from datetime import datetime

def check_health(server_ip, port):
    """التحقق من صحة التطبيق"""
    try:
        url = f"http://{server_ip}:{port}/health"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ التطبيق يعمل بشكل صحيح")
            print(f"📊 حالة قاعدة البيانات: {data.get('database', 'غير معروف')}")
            print(f"🏭 وضع Production: {data.get('production_mode', 'غير معروف')}")
            print(f"🌐 الخادم: {data.get('server_ip', 'غير معروف')}")
            print(f"⏰ الوقت: {data.get('timestamp', 'غير معروف')}")
            return True
        else:
            print(f"❌ التطبيق لا يعمل - كود الحالة: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print(f"❌ لا يمكن الاتصال بالتطبيق على {server_ip}:{port}")
        return False
    except requests.exceptions.Timeout:
        print(f"⏰ انتهت مهلة الاتصال بالتطبيق")
        return False
    except Exception as e:
        print(f"❌ خطأ في التحقق من الحالة: {e}")
        return False

def check_database_status(server_ip, port):
    """التحقق من حالة قاعدة البيانات (في وضع التطوير فقط)"""
    try:
        url = f"http://{server_ip}:{port}/db-status"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("\n📊 حالة قاعدة البيانات:")
            print(f"🔗 متصلة: {data.get('database_connected', 'غير معروف')}")
            print(f"🏭 وضع Production: {data.get('production_mode', 'غير معروف')}")
            if not data.get('production_mode', True):
                print(f"🔧 URI: {data.get('database_uri', 'غير معروف')}")
            if data.get('database_error'):
                print(f"❌ خطأ: {data.get('database_error')}")
            return True
        elif response.status_code == 403:
            print("\n📊 حالة قاعدة البيانات: غير متاحة في وضع Production")
            return True
        else:
            print(f"\n❌ لا يمكن الحصول على حالة قاعدة البيانات - كود الحالة: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"\n❌ خطأ في التحقق من قاعدة البيانات: {e}")
        return False

def check_login_page(server_ip, port):
    """التحقق من صفحة تسجيل الدخول"""
    try:
        url = f"http://{server_ip}:{port}/login"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            print("\n🔐 صفحة تسجيل الدخول: متاحة")
            return True
        else:
            print(f"\n❌ صفحة تسجيل الدخول غير متاحة - كود الحالة: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"\n❌ خطأ في التحقق من صفحة تسجيل الدخول: {e}")
        return False

def check_register_page(server_ip, port):
    """التحقق من صفحة التسجيل"""
    try:
        url = f"http://{server_ip}:{port}/register"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            print("📝 صفحة التسجيل: متاحة")
            return True
        else:
            print(f"❌ صفحة التسجيل غير متاحة - كود الحالة: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في التحقق من صفحة التسجيل: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔍 التحقق من حالة Ta9affi")
    print("=" * 40)
    
    # الحصول على معلومات الخادم
    server_ip = os.environ.get('SERVER_IP', '*************')
    port = os.environ.get('PORT', '8000')
    
    # إذا تم تمرير معاملات من سطر الأوامر
    if len(sys.argv) > 1:
        server_ip = sys.argv[1]
    if len(sys.argv) > 2:
        port = sys.argv[2]
    
    print(f"🌐 الخادم: {server_ip}")
    print(f"🔌 المنفذ: {port}")
    print(f"⏰ الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("-" * 40)
    
    # إجراء الفحوصات
    checks = []
    
    # فحص الصحة العامة
    checks.append(check_health(server_ip, port))
    
    # فحص قاعدة البيانات
    checks.append(check_database_status(server_ip, port))
    
    # فحص الصفحات المهمة
    checks.append(check_login_page(server_ip, port))
    checks.append(check_register_page(server_ip, port))
    
    # النتيجة النهائية
    print("\n" + "=" * 40)
    if all(checks):
        print("✅ جميع الفحوصات نجحت - التطبيق يعمل بشكل صحيح")
        sys.exit(0)
    else:
        print("❌ بعض الفحوصات فشلت - يرجى التحقق من التطبيق")
        sys.exit(1)

if __name__ == '__main__':
    main()
