#!/usr/bin/env python3
"""
سكريپت تشخيص شامل لمشكلة عدم الاستجابة الخارجية للخادم
"""

import os
import sys
import socket
import subprocess
import requests
from datetime import datetime

def print_header(title):
    """طباعة عنوان مع تنسيق"""
    print("\n" + "="*60)
    print(f"🔍 {title}")
    print("="*60)

def check_environment_variables():
    """فحص متغيرات البيئة"""
    print_header("فحص متغيرات البيئة")
    
    env_vars = {
        'PRODUCTION_MODE': os.environ.get('PRODUCTION_MODE', 'غير محدد'),
        'SERVER_IP': os.environ.get('SERVER_IP', 'غير محدد'),
        'PORT': os.environ.get('PORT', 'غير محدد'),
        'DATABASE_URL': os.environ.get('DATABASE_URL', 'غير محدد'),
        'SECRET_KEY': 'محدد' if os.environ.get('SECRET_KEY') else 'غير محدد'
    }
    
    for var, value in env_vars.items():
        status = "✅" if value != 'غير محدد' else "❌"
        print(f"{status} {var}: {value}")
    
    return all(value != 'غير محدد' for key, value in env_vars.items() if key != 'SECRET_KEY')

def check_network_configuration():
    """فحص إعدادات الشبكة"""
    print_header("فحص إعدادات الشبكة")
    
    try:
        # فحص عنوان IP المحلي
        hostname = socket.gethostname()
        local_ip = socket.gethostbyname(hostname)
        print(f"🖥️ اسم الخادم: {hostname}")
        print(f"🌐 عنوان IP المحلي: {local_ip}")
        
        # فحص المنافذ المفتوحة
        ports_to_check = [80, 8000, 5000, 443]
        print(f"\n🔌 فحص المنافذ:")
        
        for port in ports_to_check:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('localhost', port))
            sock.close()
            
            status = "🟢 مفتوح" if result == 0 else "🔴 مغلق"
            print(f"   المنفذ {port}: {status}")
            
    except Exception as e:
        print(f"❌ خطأ في فحص الشبكة: {str(e)}")

def check_firewall_and_iptables():
    """فحص إعدادات Firewall و iptables"""
    print_header("فحص إعدادات Firewall")
    
    try:
        # فحص ufw
        try:
            result = subprocess.run(['ufw', 'status'], capture_output=True, text=True, timeout=5)
            print("🛡️ حالة UFW:")
            print(result.stdout)
        except:
            print("⚠️ UFW غير متاح أو غير مثبت")
        
        # فحص iptables
        try:
            result = subprocess.run(['iptables', '-L', '-n'], capture_output=True, text=True, timeout=5)
            print("\n🔥 قواعد iptables:")
            lines = result.stdout.split('\n')[:10]  # أول 10 أسطر فقط
            for line in lines:
                if line.strip():
                    print(f"   {line}")
            if len(result.stdout.split('\n')) > 10:
                print("   ... (المزيد)")
        except:
            print("⚠️ iptables غير متاح أو لا توجد صلاحيات")
            
    except Exception as e:
        print(f"❌ خطأ في فحص Firewall: {str(e)}")

def check_process_and_ports():
    """فحص العمليات والمنافذ المستخدمة"""
    print_header("فحص العمليات والمنافذ")
    
    try:
        # فحص العمليات التي تستخدم المنافذ
        ports = [80, 8000, 5000]
        for port in ports:
            try:
                result = subprocess.run(['lsof', '-i', f':{port}'], capture_output=True, text=True, timeout=5)
                if result.stdout.strip():
                    print(f"🔍 المنفذ {port}:")
                    lines = result.stdout.split('\n')[:3]  # أول 3 أسطر
                    for line in lines:
                        if line.strip():
                            print(f"   {line}")
                else:
                    print(f"🔍 المنفذ {port}: غير مستخدم")
            except:
                print(f"⚠️ لا يمكن فحص المنفذ {port}")
                
    except Exception as e:
        print(f"❌ خطأ في فحص العمليات: {str(e)}")

def test_local_connectivity():
    """اختبار الاتصال المحلي"""
    print_header("اختبار الاتصال المحلي")
    
    test_urls = [
        'http://localhost:8000',
        'http://127.0.0.1:8000',
        'http://0.0.0.0:8000'
    ]
    
    for url in test_urls:
        try:
            response = requests.get(f"{url}/health", timeout=5)
            print(f"✅ {url}: {response.status_code}")
        except requests.exceptions.ConnectionError:
            print(f"❌ {url}: فشل الاتصال")
        except requests.exceptions.Timeout:
            print(f"⏰ {url}: انتهت المهلة")
        except Exception as e:
            print(f"❌ {url}: {str(e)}")

def test_external_connectivity():
    """اختبار الاتصال الخارجي"""
    print_header("اختبار الاتصال الخارجي")
    
    server_ip = os.environ.get('SERVER_IP', '*************')
    test_urls = [
        f'http://{server_ip}:8000',
        f'http://{server_ip}',
        f'http://{server_ip}:80'
    ]
    
    for url in test_urls:
        try:
            response = requests.get(f"{url}/health", timeout=10)
            print(f"✅ {url}: {response.status_code}")
        except requests.exceptions.ConnectionError:
            print(f"❌ {url}: فشل الاتصال")
        except requests.exceptions.Timeout:
            print(f"⏰ {url}: انتهت المهلة")
        except Exception as e:
            print(f"❌ {url}: {str(e)}")

def check_app_configuration():
    """فحص إعدادات التطبيق"""
    print_header("فحص إعدادات التطبيق")
    
    try:
        # محاولة استيراد التطبيق
        sys.path.insert(0, '.')
        from app import app, PRODUCTION_MODE, SERVER_IP
        
        print(f"✅ تم استيراد التطبيق بنجاح")
        print(f"🏭 وضع Production: {PRODUCTION_MODE}")
        print(f"🌐 عنوان الخادم: {SERVER_IP}")
        print(f"🔧 إعدادات Flask:")
        print(f"   - DEBUG: {app.debug}")
        print(f"   - TESTING: {app.testing}")
        
        # فحص إعدادات قاعدة البيانات
        db_uri = app.config.get('SQLALCHEMY_DATABASE_URI', 'غير محدد')
        print(f"🗄️ قاعدة البيانات: {db_uri[:50]}...")
        
    except Exception as e:
        print(f"❌ خطأ في استيراد التطبيق: {str(e)}")

def generate_fix_suggestions():
    """اقتراح حلول للمشاكل"""
    print_header("اقتراحات الحلول")
    
    print("🔧 خطوات الحل المقترحة:")
    print()
    print("1. تأكد من تعيين متغيرات البيئة:")
    print("   export PRODUCTION_MODE=true")
    print("   export SERVER_IP=*************")
    print("   export PORT=8000")
    print()
    print("2. تأكد من فتح المنفذ في Firewall:")
    print("   sudo ufw allow 8000")
    print("   sudo ufw allow 80")
    print()
    print("3. تشغيل التطبيق بالطريقة الصحيحة:")
    print("   python app.py")
    print("   # أو")
    print("   gunicorn --bind 0.0.0.0:8000 app:app")
    print()
    print("4. اختبار الاتصال:")
    print("   curl http://*************:8000/health")
    print()
    print("5. فحص السجلات:")
    print("   tail -f /var/log/syslog")
    print("   journalctl -f")

def main():
    """الدالة الرئيسية"""
    print("🔍 تشخيص شامل لمشكلة عدم الاستجابة الخارجية للخادم")
    print(f"⏰ الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # تشغيل جميع الفحوصات
    check_environment_variables()
    check_network_configuration()
    check_firewall_and_iptables()
    check_process_and_ports()
    check_app_configuration()
    test_local_connectivity()
    test_external_connectivity()
    generate_fix_suggestions()
    
    print("\n" + "="*60)
    print("✅ انتهى التشخيص")
    print("="*60)

if __name__ == '__main__':
    main()
