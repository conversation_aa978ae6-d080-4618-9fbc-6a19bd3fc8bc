#!/usr/bin/env python3
"""
سكريپت تشغيل Ta9affi محلياً مع SQLite
"""

import os
import sys

def setup_local_environment():
    """إعداد البيئة المحلية"""
    print("🏠 إعداد البيئة المحلية...")
    
    # إعدادات محلية آمنة
    os.environ['PRODUCTION_MODE'] = 'true'
    os.environ['SERVER_IP'] = 'localhost'
    os.environ['SECRET_KEY'] = 'local-development-secret-key-2024'
    os.environ['DATABASE_URL'] = 'sqlite:///ta9affi_local.db'
    os.environ['PORT'] = '8000'
    
    # تعطيل Redis للاختبار المحلي
    if 'REDIS_URL' in os.environ:
        del os.environ['REDIS_URL']
    
    print("✅ تم إعداد البيئة المحلية:")
    print(f"   📍 الخادم: {os.environ['SERVER_IP']}")
    print(f"   🔌 المنفذ: {os.environ['PORT']}")
    print(f"   🗄️ قاعدة البيانات: SQLite محلية")
    print(f"   🔐 وضع Production: مفعل (للاختبار)")

def main():
    """الدالة الرئيسية"""
    print("🏠 تشغيل Ta9affi محلياً")
    print("=" * 40)
    
    # إعداد البيئة
    setup_local_environment()
    
    print("\n🚀 بدء تشغيل التطبيق...")
    print("=" * 40)
    
    try:
        # استيراد وتشغيل التطبيق
        from app import app
        
        print("✅ تم تحميل التطبيق بنجاح")
        print(f"🌐 الرابط: http://localhost:8000")
        print(f"🔐 تسجيل الدخول: http://localhost:8000/login")
        print(f"📝 التسجيل: http://localhost:8000/register")
        print(f"❤️ فحص الصحة: http://localhost:8000/health")
        print("\n⏹️ اضغط Ctrl+C لإيقاف التطبيق")
        print("=" * 40)
        
        # تشغيل التطبيق
        app.run(
            host='localhost',
            port=8000,
            debug=False,
            threaded=True
        )
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد التطبيق: {e}")
        print("💡 تأكد من تثبيت المتطلبات: pip install -r requirements.txt")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف التطبيق بواسطة المستخدم")
        sys.exit(0)
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
