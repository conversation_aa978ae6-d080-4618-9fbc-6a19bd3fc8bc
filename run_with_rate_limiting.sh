#!/bin/bash
# تشغيل Ta9affi مع نظام Rate Limiting

set -e

# ألوان للرسائل
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}$1${NC}"
}

# فحص المتطلبات
check_requirements() {
    log_header "🔍 فحص المتطلبات"
    
    # فحص Python
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 غير مثبت"
        exit 1
    fi
    
    # فحص ملفات Rate Limiting
    local required_files=("rate_limiter.py" "rate_limit_monitor.py" "app.py")
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            log_error "الملف المطلوب غير موجود: $file"
            exit 1
        fi
    done
    
    log_success "جميع المتطلبات متوفرة"
}

# فحص Redis
check_redis() {
    log_header "🔄 فحص Redis"
    
    # محاولة الاتصال بـ Redis
    if command -v redis-cli &> /dev/null; then
        if redis-cli ping &> /dev/null; then
            log_success "Redis يعمل بشكل صحيح"
            return 0
        else
            log_warning "Redis غير متاح - سيعمل النظام بدون Rate Limiting"
            return 1
        fi
    else
        log_warning "Redis غير مثبت - سيعمل النظام بدون Rate Limiting"
        return 1
    fi
}

# تثبيت المتطلبات
install_requirements() {
    log_header "📦 تثبيت المتطلبات"
    
    # تثبيت مكتبة Redis إذا لم تكن موجودة
    if ! python3 -c "import redis" &> /dev/null; then
        log_info "تثبيت مكتبة Redis..."
        pip3 install redis
    fi
    
    log_success "تم تثبيت المتطلبات"
}

# اختبار نظام Rate Limiting
test_rate_limiting() {
    log_header "🧪 اختبار نظام Rate Limiting"
    
    if [ -f "test_rate_limiting.py" ]; then
        python3 test_rate_limiting.py
    else
        log_warning "ملف الاختبار غير موجود"
    fi
}

# تشغيل التطبيق
run_application() {
    log_header "🚀 تشغيل Ta9affi مع Rate Limiting"
    
    # تعيين متغيرات البيئة
    export FLASK_ENV=development
    export FLASK_DEBUG=1
    
    # تشغيل التطبيق
    log_info "بدء تشغيل التطبيق..."
    log_info "يمكنك الوصول للتطبيق على: http://localhost:5000"
    log_info "لوحة تحكم Rate Limiting: http://localhost:5000/admin/rate-limits/"
    log_info "للإيقاف: اضغط Ctrl+C"
    
    echo ""
    python3 app.py
}

# عرض معلومات النظام
show_system_info() {
    log_header "📊 معلومات نظام Rate Limiting"
    
    echo "=================================="
    echo "🛡️ نظام Rate Limiting - Ta9affi"
    echo "=================================="
    echo "📋 القواعد المطبقة:"
    echo "   - إضافة التقدمات: 10 كل 12 ساعة"
    echo "   - حذف التقدمات: 3 كل 12 ساعة"
    echo "   - التعديل: غير محدود"
    echo "   - تحضير خطة الدرس: غير محدود"
    echo ""
    echo "🔄 إعادة التعيين:"
    echo "   - كل 12 ساعة تلقائياً"
    echo "   - النوافذ الزمنية: 00:00-12:00 و 12:00-24:00"
    echo ""
    echo "🎛️ الإدارة:"
    echo "   - لوحة التحكم: /admin/rate-limits/"
    echo "   - API المستخدمين: /api/my-rate-limits"
    echo "   - إعادة تعيين: متاح للمديرين"
    echo ""
    echo "🔍 المراقبة:"
    echo "   - إحصائيات الوقت الفعلي"
    echo "   - تتبع المستخدمين المحظورين"
    echo "   - تقارير النشاط"
    echo "=================================="
}

# عرض المساعدة
show_help() {
    echo "استخدام: $0 [COMMAND]"
    echo ""
    echo "الأوامر:"
    echo "  start       - تشغيل التطبيق (افتراضي)"
    echo "  test        - اختبار نظام Rate Limiting"
    echo "  check       - فحص المتطلبات والإعدادات"
    echo "  install     - تثبيت المتطلبات"
    echo "  info        - عرض معلومات النظام"
    echo "  help        - عرض هذه المساعدة"
    echo ""
    echo "أمثلة:"
    echo "  $0 start    # تشغيل التطبيق"
    echo "  $0 test     # اختبار النظام"
    echo "  $0 check    # فحص الإعدادات"
}

# الدالة الرئيسية
main() {
    case "${1:-start}" in
        "start")
            show_system_info
            echo ""
            check_requirements
            check_redis
            install_requirements
            run_application
            ;;
            
        "test")
            log_header "🧪 اختبار نظام Rate Limiting"
            check_requirements
            test_rate_limiting
            ;;
            
        "check")
            log_header "🔍 فحص النظام"
            check_requirements
            check_redis
            log_success "جميع الفحوصات مكتملة"
            ;;
            
        "install")
            log_header "📦 تثبيت المتطلبات"
            install_requirements
            ;;
            
        "info")
            show_system_info
            ;;
            
        "help")
            show_help
            ;;
            
        *)
            log_error "أمر غير معروف: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# تشغيل السكريبت
main "$@"
