{% extends 'base.html' %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card shadow-lg border-0 rounded-lg">
            <div class="card-header bg-primary text-white">
                <h3 class="text-center font-weight-light my-2">
                    <i class="fas fa-user-circle animated-icon pulse-icon me-2"></i>
                    الملف الشخصي
                </h3>
            </div>
            <div class="card-body">
                <!-- معلومات المستخدم الحالية -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <i class="fas fa-info-circle animated-icon me-2"></i>
                                    المعلومات الحالية
                                </h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>اسم المستخدم:</strong> {{ user.username }}</p>
                                        <p><strong>البريد الإلكتروني:</strong> {{ user.email }}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>الدور:</strong>
                                            {% if user.role == 'admin' %}
                                            <span class="badge bg-danger">إدارة عامة</span>
                                            {% elif user.role == 'inspector' %}
                                            <span class="badge bg-warning">مفتش</span>
                                            {% else %}
                                            <span class="badge bg-success">أستاذ</span>
                                            {% endif %}
                                        </p>
                                        <p><strong>تاريخ التسجيل:</strong> {{ user.created_at.strftime('%Y-%m-%d') }}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معلومات الاشتراك -->
                {% if user.role in ['teacher', 'inspector'] %}
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div
                            class="card {% if user.has_active_subscription %}bg-success{% elif user.subscription_status == 'trial' %}bg-warning{% else %}bg-danger{% endif %} text-white">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <i class="fas fa-crown animated-icon me-2"></i>
                                    حالة الاشتراك
                                </h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        {% if user.subscription_status == 'active' %}
                                        {% set active_sub = user.subscriptions|selectattr('is_active', 'equalto',
                                        True)|first %}
                                        <p><strong>الحالة:</strong>
                                            <span class="badge bg-light text-success">
                                                <i class="fas fa-check-circle me-1"></i>
                                                اشتراك نشط
                                            </span>
                                        </p>
                                        {% if active_sub %}
                                        <p><strong>نوع الباقة:</strong> {{ active_sub.plan.name }}</p>
                                        <p><strong>تاريخ الانتهاء:</strong> {{ active_sub.end_date.strftime('%Y-%m-%d')
                                            }}</p>
                                        {% endif %}
                                        {% elif user.subscription_status == 'trial' %}
                                        <p><strong>الحالة:</strong>
                                            <span class="badge bg-light text-warning">
                                                <i class="fas fa-clock me-1"></i>
                                                فترة تجريبية
                                            </span>
                                        </p>
                                        {% if user.free_trial_end %}
                                        <p><strong>انتهاء الفترة التجريبية:</strong> {{
                                            user.free_trial_end.strftime('%Y-%m-%d') }}</p>
                                        {% endif %}
                                        {% else %}
                                        <p><strong>الحالة:</strong>
                                            <span class="badge bg-light text-danger">
                                                <i class="fas fa-times-circle me-1"></i>
                                                منتهي الصلاحية
                                            </span>
                                        </p>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-6">
                                        {% if user.subscription_status == 'active' %}
                                        {% set active_sub = user.subscriptions|selectattr('is_active', 'equalto',
                                        True)|first %}
                                        {% if active_sub %}
                                        <div class="text-center">
                                            <div class="display-6 fw-bold">{{ active_sub.days_remaining }}</div>
                                            <small>يوم متبقي</small>
                                        </div>
                                        {% if active_sub.is_expiring_soon %}
                                        <div class="alert alert-warning mt-2 mb-0" role="alert">
                                            <i class="fas fa-exclamation-triangle me-1"></i>
                                            ينتهي قريباً!
                                        </div>
                                        {% endif %}
                                        {% endif %}
                                        {% elif user.subscription_status == 'trial' and user.free_trial_end %}
                                        <div class="text-center">
                                            <div class="display-6 fw-bold">{{ trial_days_left }}</div>
                                            <small>يوم متبقي في الفترة التجريبية</small>
                                        </div>
                                        {% if trial_days_left <= 7 %} <div class="alert alert-warning mt-2 mb-0"
                                            role="alert">
                                            <i class="fas fa-exclamation-triangle me-1"></i>
                                            تنتهي الفترة التجريبية قريباً!
                                    </div>
                                    {% endif %}
                                    {% else %}
                                    <div class="text-center">
                                        <div class="display-6 fw-bold text-danger">0</div>
                                        <small>يوم متبقي</small>
                                    </div>
                                    <div class="mt-2">
                                        <a href="{{ url_for('subscription_plans') }}" class="btn btn-light btn-sm">
                                            <i class="fas fa-shopping-cart me-1"></i>
                                            تجديد الاشتراك
                                        </a>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- نموذج تحديث البيانات -->
            <form action="{{ url_for('update_profile') }}" method="post">
                <h5 class="mb-3">
                    <i class="fas fa-edit animated-icon bounce-icon me-2"></i>
                    تحديث البيانات
                </h5>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-floating mb-3">
                            <input type="text" class="form-control" id="username" name="username"
                                value="{{ user.username }}" required>
                            <label for="username">
                                <i class="fas fa-user me-1"></i>
                                اسم المستخدم
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-floating mb-3">
                            <input type="email" class="form-control" id="email" name="email" value="{{ user.email }}"
                                required>
                            <label for="email">
                                <i class="fas fa-envelope me-1"></i>
                                البريد الإلكتروني
                            </label>
                        </div>
                    </div>
                </div>

                <hr class="my-4">

                <h5 class="mb-3">
                    <i class="fas fa-key animated-icon shake-icon me-2"></i>
                    تغيير كلمة المرور (اختياري)
                </h5>

                <div class="row">
                    <div class="col-md-12">
                        <div class="form-floating mb-3">
                            <input type="password" class="form-control" id="current_password" name="current_password">
                            <label for="current_password">
                                <i class="fas fa-lock me-1"></i>
                                كلمة المرور الحالية
                            </label>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-floating mb-3">
                            <input type="password" class="form-control" id="new_password" name="new_password">
                            <label for="new_password">
                                <i class="fas fa-key me-1"></i>
                                كلمة المرور الجديدة
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-floating mb-3">
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                            <label for="confirm_password">
                                <i class="fas fa-check-circle me-1"></i>
                                تأكيد كلمة المرور
                            </label>
                        </div>
                    </div>
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <a href="{{ url_for('dashboard') }}" class="btn btn-secondary btn-lg px-4 me-md-2 btn-hover-effect">
                        <i class="fas fa-arrow-right me-1"></i>
                        العودة
                    </a>
                    <button type="submit" class="btn btn-primary btn-lg px-4 btn-hover-effect">
                        <i class="fas fa-save me-1"></i>
                        حفظ التغييرات
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
</div>

<!-- إضافة بعض الأيقونات المتحركة الإضافية -->
<style>
    .profile-icon {
        color: #1976d2;
        transition: all 0.3s ease;
    }

    .profile-icon:hover {
        transform: scale(1.2);
        color: #0d47a1;
    }

    .card {
        transition: all 0.3s ease;
    }

    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
</style>

<script>
    // إضافة تأثيرات تفاعلية
    document.addEventListener('DOMContentLoaded', function () {
        // التحقق من تطابق كلمات المرور
        const newPassword = document.getElementById('new_password');
        const confirmPassword = document.getElementById('confirm_password');

        function checkPasswordMatch() {
            if (newPassword.value && confirmPassword.value) {
                if (newPassword.value === confirmPassword.value) {
                    confirmPassword.classList.remove('is-invalid');
                    confirmPassword.classList.add('is-valid');
                } else {
                    confirmPassword.classList.remove('is-valid');
                    confirmPassword.classList.add('is-invalid');
                }
            } else {
                confirmPassword.classList.remove('is-valid', 'is-invalid');
            }
        }

        newPassword.addEventListener('input', checkPasswordMatch);
        confirmPassword.addEventListener('input', checkPasswordMatch);

        // إضافة تأثيرات للأيقونات
        const icons = document.querySelectorAll('.animated-icon');
        icons.forEach(icon => {
            icon.addEventListener('mouseenter', function () {
                this.style.transform = 'scale(1.2) rotate(10deg)';
            });

            icon.addEventListener('mouseleave', function () {
                this.style.transform = 'scale(1) rotate(0deg)';
            });
        });
    });
</script>
{% endblock %}