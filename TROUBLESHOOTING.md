# دليل استكشاف الأخطاء - Ta9affi Production

## المشاكل الشائعة وحلولها

### 1. خطأ الاتصال بقاعدة البيانات MySQL

#### المشكلة:
```
pymysql.err.OperationalError: (2003, "Can't connect to MySQL server on '*************' ([<PERSON>rrno 111] Connection refused)")
```

#### الأسباب المحتملة:
- خادم MySQL غير مُشغل على العنوان المحدد
- المنفذ 3306 مغلق أو محجوب بواسطة Firewall
- إعدادات الشبكة غير صحيحة
- اسم المستخدم أو كلمة المرور غير صحيحة

#### الحلول:

##### أ. التحقق من حالة خادم MySQL
```bash
# التحقق من تشغيل MySQL
sudo systemctl status mysql

# تشغيل MySQL إذا كان متوقف
sudo systemctl start mysql

# التحقق من المنفذ
netstat -tlnp | grep :3306
```

##### ب. التحقق من إعدادات الشبكة
```bash
# اختبار الاتصال بالخادم
ping *************

# اختبار المنفذ
telnet 82.25.************
```

##### ج. إعداد MySQL للاتصالات الخارجية
```sql
-- السماح بالاتصالات من جميع العناوين
GRANT ALL PRIVILEGES ON ta9affi_db.* TO 'ta9affi_user'@'%' IDENTIFIED BY 'password';
FLUSH PRIVILEGES;
```

##### د. تعديل إعدادات MySQL
```ini
# في ملف /etc/mysql/mysql.conf.d/mysqld.cnf
[mysqld]
bind-address = 0.0.0.0
```

##### هـ. استخدام SQLite كبديل مؤقت
```bash
# تعديل متغير البيئة
export DATABASE_URL=sqlite:///ta9affi.db

# أو في ملف .env
DATABASE_URL=sqlite:///ta9affi.db
```

### 2. خطأ Redis غير متاح

#### المشكلة:
```
⚠️ Redis غير متاح: Error -2 connecting to redis:6379. Name or service not known.
```

#### الحل:
Redis اختياري للتطبيق. يمكن تشغيل التطبيق بدونه:

```bash
# حذف متغير Redis من البيئة
unset REDIS_URL

# أو تعطيله في ملف .env
# REDIS_URL=redis://localhost:6379/0
```

### 3. مشاكل المتطلبات والمكتبات

#### المشكلة:
```
ModuleNotFoundError: No module named 'flask'
```

#### الحل:
```bash
# تثبيت المتطلبات
pip install -r requirements.txt

# أو تثبيت المكتبات الأساسية يدوياً
pip install flask flask-sqlalchemy flask-login werkzeug pandas openpyxl pymysql
```

### 4. مشاكل الصلاحيات

#### المشكلة:
```
PermissionError: [Errno 13] Permission denied
```

#### الحل:
```bash
# إعطاء صلاحيات للملفات
chmod +x start_production.sh
chmod +x run_production.py

# تشغيل بصلاحيات مناسبة
sudo python app.py
```

### 5. مشكلة المنفذ مُستخدم

#### المشكلة:
```
OSError: [Errno 98] Address already in use
```

#### الحل:
```bash
# العثور على العملية التي تستخدم المنفذ
sudo lsof -i :8000

# إنهاء العملية
sudo kill -9 <PID>

# أو استخدام منفذ آخر
export PORT=8001
```

## الاختبار المحلي

### 1. اختبار سريع
```bash
# تشغيل الاختبار المحلي
python test_local.py
```

### 2. تشغيل محلي مع SQLite
```bash
# تشغيل محلي آمن
python run_local.py
```

### 3. فحص الحالة
```bash
# فحص حالة التطبيق
python check_status.py localhost 8000
```

## إعدادات الطوارئ

### 1. إعدادات SQLite للطوارئ
```bash
export PRODUCTION_MODE=true
export DATABASE_URL=sqlite:///ta9affi_emergency.db
export SECRET_KEY=emergency-secret-key
export SERVER_IP=localhost
export PORT=8000
```

### 2. تشغيل بأقل إعدادات
```python
# ملف run_emergency.py
import os
os.environ['PRODUCTION_MODE'] = 'false'
os.environ['DATABASE_URL'] = 'sqlite:///emergency.db'

from app import app
app.run(host='0.0.0.0', port=8000, debug=True)
```

## فحص السجلات

### 1. سجلات التطبيق
```bash
# عرض السجلات المباشرة
tail -f logs/gunicorn_error.log
tail -f logs/gunicorn_access.log
```

### 2. سجلات النظام
```bash
# سجلات MySQL
sudo tail -f /var/log/mysql/error.log

# سجلات النظام
sudo journalctl -u mysql -f
```

## أدوات التشخيص

### 1. فحص الاتصالات
```bash
# فحص المنافذ المفتوحة
sudo netstat -tlnp

# فحص الاتصالات النشطة
sudo ss -tlnp
```

### 2. فحص الموارد
```bash
# استخدام الذاكرة
free -h

# استخدام المعالج
top

# مساحة القرص
df -h
```

## جهات الاتصال للدعم

### 1. معلومات النظام
```bash
# معلومات النظام
uname -a
python --version
mysql --version
```

### 2. معلومات التطبيق
```bash
# إصدار التطبيق
curl http://localhost:8000/health

# حالة قاعدة البيانات
curl http://localhost:8000/db-status
```

## نصائح الأداء

### 1. تحسين MySQL
```sql
-- زيادة حجم الذاكرة المؤقتة
SET GLOBAL innodb_buffer_pool_size = **********; -- 1GB

-- تحسين الاتصالات
SET GLOBAL max_connections = 200;
```

### 2. تحسين التطبيق
```bash
# استخدام Gunicorn مع عدة workers
gunicorn --workers 4 --bind 0.0.0.0:8000 app:app

# تفعيل التخزين المؤقت
export FLASK_ENV=production
```

## الخطوات الوقائية

1. **النسخ الاحتياطية**: قم بعمل نسخ احتياطية دورية
2. **المراقبة**: استخدم أدوات المراقبة للتحقق من الحالة
3. **التحديثات**: حافظ على تحديث النظام والمكتبات
4. **الأمان**: استخدم كلمات مرور قوية وشهادات SSL
5. **التوثيق**: احتفظ بسجل للتغييرات والإعدادات
