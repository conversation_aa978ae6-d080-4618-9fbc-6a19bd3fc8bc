#!/usr/bin/env python3
"""
سكريپت إصلاح سريع لمشكلة عدم الاستجابة الخارجية للخادم
"""

import os
import sys
import subprocess

def setup_environment():
    """إعداد متغيرات البيئة الصحيحة"""
    print("🔧 إعداد متغيرات البيئة...")
    
    # تعيين متغيرات البيئة
    env_vars = {
        'PRODUCTION_MODE': 'true',
        'SERVER_IP': '*************',
        'PORT': '8000',
        'SECRET_KEY': 'ta9affi-production-secret-key-2024'
    }
    
    for key, value in env_vars.items():
        os.environ[key] = value
        print(f"✅ {key} = {value}")
    
    # إنشاء ملف .env للاستخدام المستقبلي
    with open('.env', 'w', encoding='utf-8') as f:
        f.write("# إعدادات Ta9affi Production\n")
        for key, value in env_vars.items():
            f.write(f"{key}={value}\n")
    
    print("✅ تم إنشاء ملف .env")

def check_and_fix_firewall():
    """فحص وإصلاح إعدادات Firewall"""
    print("\n🛡️ فحص وإصلاح إعدادات Firewall...")

    try:
        # فحص حالة UFW
        result = subprocess.run(['ufw', 'status'], capture_output=True, text=True)
        print(f"حالة UFW: {result.stdout.strip()}")

        # فتح المنافذ المطلوبة (إضافة 443 للـ HTTPS)
        ports = ['8000', '80', '443']
        for port in ports:
            try:
                subprocess.run(['sudo', 'ufw', 'allow', port], check=True)
                print(f"✅ تم فتح المنفذ {port}")
            except subprocess.CalledProcessError:
                print(f"⚠️ فشل في فتح المنفذ {port} (قد يكون مفتوح مسبقاً)")

        # إعادة تحميل UFW
        try:
            subprocess.run(['sudo', 'ufw', 'reload'], check=True)
            print("✅ تم إعادة تحميل UFW")
        except:
            print("⚠️ فشل في إعادة تحميل UFW")

    except FileNotFoundError:
        print("⚠️ UFW غير مثبت، جاري فحص iptables...")

        # إضافة قواعد iptables (إضافة 443 للـ HTTPS)
        iptables_rules = [
            ['sudo', 'iptables', '-A', 'INPUT', '-p', 'tcp', '--dport', '8000', '-j', 'ACCEPT'],
            ['sudo', 'iptables', '-A', 'INPUT', '-p', 'tcp', '--dport', '80', '-j', 'ACCEPT'],
            ['sudo', 'iptables', '-A', 'INPUT', '-p', 'tcp', '--dport', '443', '-j', 'ACCEPT']
        ]

        for rule in iptables_rules:
            try:
                subprocess.run(rule, check=True)
                print(f"✅ تم إضافة قاعدة iptables: {' '.join(rule[2:])}")
            except:
                print(f"⚠️ فشل في إضافة قاعدة iptables")

def create_systemd_service():
    """إنشاء خدمة systemd للتطبيق"""
    print("\n⚙️ إنشاء خدمة systemd...")
    
    service_content = f"""[Unit]
Description=Ta9affi Flask Application
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory={os.getcwd()}
Environment=PRODUCTION_MODE=true
Environment=SERVER_IP=*************
Environment=PORT=8000
Environment=SECRET_KEY=ta9affi-production-secret-key-2024
ExecStart=/usr/bin/python3 {os.getcwd()}/app.py
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
"""
    
    try:
        with open('/tmp/ta9affi.service', 'w') as f:
            f.write(service_content)
        
        # نسخ الملف إلى مجلد systemd
        subprocess.run(['sudo', 'cp', '/tmp/ta9affi.service', '/etc/systemd/system/'], check=True)
        
        # إعادة تحميل systemd
        subprocess.run(['sudo', 'systemctl', 'daemon-reload'], check=True)
        
        print("✅ تم إنشاء خدمة systemd")
        print("💡 لتشغيل الخدمة: sudo systemctl start ta9affi")
        print("💡 لتفعيل التشغيل التلقائي: sudo systemctl enable ta9affi")
        
    except Exception as e:
        print(f"⚠️ فشل في إنشاء خدمة systemd: {str(e)}")

def create_nginx_config():
    """إنشاء إعدادات Nginx مع دعم SSL"""
    print("\n🌐 إنشاء إعدادات Nginx مع SSL...")

    nginx_config = """# إعدادات Nginx لـ ta9affi.com مع SSL
server {
    listen 80;
    server_name ta9affi.com www.ta9affi.com *************;

    # إعادة توجيه HTTP إلى HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name ta9affi.com www.ta9affi.com;

    # إعدادات SSL
    ssl_certificate /etc/letsencrypt/live/ta9affi.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/ta9affi.com/privkey.pem;

    # إعدادات SSL محسنة
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Headers أمنية
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;

    location / {
        proxy_pass http://127.0.0.1:80;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $server_name;

        # إعدادات إضافية للـ proxy
        proxy_buffering off;
        proxy_redirect off;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    location /static {
        alias /app/static;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}

# إعدادات للوصول المباشر بـ IP
server {
    listen 443 ssl http2;
    server_name *************;

    # استخدام نفس شهادة SSL
    ssl_certificate /etc/letsencrypt/live/ta9affi.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/ta9affi.com/privkey.pem;

    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;

    location / {
        proxy_pass http://127.0.0.1:80;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
"""

    try:
        with open('/tmp/ta9affi_nginx_ssl.conf', 'w') as f:
            f.write(nginx_config)

        print("✅ تم إنشاء ملف إعدادات Nginx مع SSL في /tmp/ta9affi_nginx_ssl.conf")
        print("💡 لتفعيل Nginx مع SSL:")
        print("   sudo cp /tmp/ta9affi_nginx_ssl.conf /etc/nginx/sites-available/ta9affi")
        print("   sudo ln -s /etc/nginx/sites-available/ta9affi /etc/nginx/sites-enabled/")
        print("   sudo nginx -t")
        print("   sudo systemctl reload nginx")
        print("\n🔒 ملاحظة: تأكد من وجود شهادات SSL في:")
        print("   /etc/letsencrypt/live/ta9affi.com/")

    except Exception as e:
        print(f"⚠️ فشل في إنشاء إعدادات Nginx: {str(e)}")

def check_ssl_setup():
    """فحص إعدادات SSL"""
    print("\n🔒 فحص إعدادات SSL...")

    try:
        import ssl
        import socket

        # فحص الاتصال بـ HTTPS
        context = ssl.create_default_context()

        try:
            with socket.create_connection(('ta9affi.com', 443), timeout=10) as sock:
                with context.wrap_socket(sock, server_hostname='ta9affi.com') as ssock:
                    cert = ssock.getpeercert()
                    print("✅ شهادة SSL موجودة وصالحة")
                    print(f"   📋 المُصدر: {cert.get('issuer', {}).get('organizationName', 'غير محدد')}")
                    print(f"   📅 صالحة حتى: {cert.get('notAfter', 'غير محدد')}")
                    return True
        except Exception as e:
            print(f"❌ مشكلة في شهادة SSL: {str(e)}")
            return False

    except ImportError:
        print("⚠️ مكتبة SSL غير متاحة")
        return False

def test_application():
    """اختبار التطبيق"""
    print("\n🧪 اختبار التطبيق...")
    
    try:
        # محاولة استيراد التطبيق
        sys.path.insert(0, '.')
        from app import app
        
        print("✅ تم استيراد التطبيق بنجاح")
        
        # اختبار التطبيق مع test client
        with app.test_client() as client:
            response = client.get('/health')
            if response.status_code == 200:
                print("✅ اختبار /health نجح")
            else:
                print(f"⚠️ اختبار /health فشل: {response.status_code}")
                
    except Exception as e:
        print(f"❌ فشل في اختبار التطبيق: {str(e)}")

def create_startup_script():
    """إنشاء سكريپت بدء التشغيل"""
    print("\n📜 إنشاء سكريپت بدء التشغيل...")
    
    startup_script = f"""#!/bin/bash

# سكريپت بدء تشغيل Ta9affi
echo "🚀 بدء تشغيل Ta9affi..."

# تعيين متغيرات البيئة
export PRODUCTION_MODE=true
export SERVER_IP=*************
export PORT=8000
export SECRET_KEY=ta9affi-production-secret-key-2024

# الانتقال إلى مجلد التطبيق
cd {os.getcwd()}

# تشغيل التطبيق
echo "📍 تشغيل التطبيق على http://*************:8000"
python3 app.py
"""
    
    try:
        with open('start_ta9affi.sh', 'w') as f:
            f.write(startup_script)
        
        # إعطاء صلاحيات التنفيذ
        os.chmod('start_ta9affi.sh', 0o755)
        
        print("✅ تم إنشاء سكريپت start_ta9affi.sh")
        print("💡 لتشغيل التطبيق: ./start_ta9affi.sh")
        
    except Exception as e:
        print(f"⚠️ فشل في إنشاء سكريپت البدء: {str(e)}")

def main():
    """الدالة الرئيسية"""
    print("🔧 إصلاح سريع لمشكلة عدم الاستجابة الخارجية للخادم")
    print("="*60)
    
    # تنفيذ خطوات الإصلاح
    setup_environment()
    check_and_fix_firewall()
    create_systemd_service()
    create_nginx_config()
    test_application()
    create_startup_script()
    
    print("\n" + "="*60)
    print("✅ انتهى الإصلاح")
    print("\n🚀 خطوات التشغيل:")
    print("1. ./start_ta9affi.sh")
    print("2. أو: python3 app.py")
    print("3. أو: sudo systemctl start ta9affi")
    print("\n🌐 اختبار الوصول:")
    print("   http://*************:8000")
    print("   http://*************:8000/health")
    print("="*60)

if __name__ == '__main__':
    main()
