{% extends 'base.html' %}

{% block extra_css %}
<style>
    /* تحسينات CSS لصفحة تسجيل الدخول */
    .password-toggle-btn {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: #6c757d;
        cursor: pointer;
        z-index: 10;
        padding: 5px;
        border-radius: 3px;
        transition: color 0.3s ease;
    }

    .password-toggle-btn:hover {
        color: #495057;
        background-color: rgba(0,0,0,0.05);
    }

    .btn-hover-effect {
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .btn-hover-effect:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }

    .card {
        border-radius: 15px;
        overflow: hidden;
        animation: fadeInUp 0.6s ease-out;
    }

    .card-header {
        border-radius: 15px 15px 0 0 !important;
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .alert {
        border-radius: 10px;
        border: none;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        animation: slideDown 0.3s ease-out;
    }

    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .spinner-border-sm {
        width: 1rem;
        height: 1rem;
    }

    /* تحسين الاستجابة للشاشات الصغيرة */
    @media (max-width: 768px) {
        .col-lg-5 {
            margin: 0 15px;
        }

        .card {
            margin-top: 20px !important;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-5">
        <div class="card shadow-lg border-0 rounded-lg mt-5">
            <div class="card-header bg-primary text-white">
                <h3 class="text-center font-weight-light my-2">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    تسجيل الدخول
                </h3>
            </div>
            <div class="card-body">
                <!-- رسائل الخطأ والنجاح -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            {% if category == 'danger' %}
                                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>خطأ:</strong> {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            {% elif category == 'success' %}
                                <div class="alert alert-success alert-dismissible fade show" role="alert">
                                    <i class="fas fa-check-circle me-2"></i>
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            {% elif category == 'warning' %}
                                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                                    <i class="fas fa-exclamation-circle me-2"></i>
                                    <strong>تنبيه:</strong> {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            {% elif category == 'info' %}
                                <div class="alert alert-info alert-dismissible fade show" role="alert">
                                    <i class="fas fa-info-circle me-2"></i>
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            {% endif %}
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <form method="POST" action="{{ url_for('login') }}" id="loginForm">
                    <div class="form-floating mb-3">
                        <input class="form-control" id="username" name="username" type="text" placeholder="اسم المستخدم"
                            required />
                        <label for="username">اسم المستخدم</label>
                    </div>
                    <div class="form-floating mb-3 position-relative">
                        <input class="form-control" id="password" name="password" type="password"
                            placeholder="كلمة المرور" required />
                        <label for="password">كلمة المرور</label>
                        <button type="button" class="password-toggle-btn" id="togglePassword">
                            <i class="fas fa-eye" id="togglePasswordIcon"></i>
                        </button>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" id="remember" name="remember" type="checkbox" />
                        <label class="form-check-label" for="remember">تذكرني</label>
                    </div>
                    <div class="d-grid">
                        <button class="btn btn-primary btn-lg btn-hover-effect" type="submit" id="loginBtn">
                            <span id="loginBtnText">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                تسجيل الدخول
                            </span>
                            <span id="loginBtnLoading" class="d-none">
                                <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                جاري تسجيل الدخول...
                            </span>
                        </button>
                    </div>
                </form>
            </div>
            <div class="card-footer text-center py-3">
                <div class="small">
                    <a href="{{ url_for('register') }}">ليس لديك حساب؟ سجل الآن!</a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const passwordField = document.getElementById('password');
        const togglePasswordBtn = document.getElementById('togglePassword');
        const togglePasswordIcon = document.getElementById('togglePasswordIcon');

        togglePasswordBtn.addEventListener('click', function () {
            // تبديل نوع الحقل بين password و text
            const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordField.setAttribute('type', type);

            // تبديل الأيقونة
            if (type === 'text') {
                togglePasswordIcon.classList.remove('fa-eye');
                togglePasswordIcon.classList.add('fa-eye-slash');
                togglePasswordBtn.setAttribute('title', 'إخفاء كلمة المرور');
            } else {
                togglePasswordIcon.classList.remove('fa-eye-slash');
                togglePasswordIcon.classList.add('fa-eye');
                togglePasswordBtn.setAttribute('title', 'إظهار كلمة المرور');
            }
        });

        // إضافة تلميح للزر
        togglePasswordBtn.setAttribute('title', 'إظهار كلمة المرور');

        // إضافة وظائف التحميل والتحقق
        const loginForm = document.getElementById('loginForm');
        const loginBtn = document.getElementById('loginBtn');
        const loginBtnText = document.getElementById('loginBtnText');
        const loginBtnLoading = document.getElementById('loginBtnLoading');

        // دالة لإظهار حالة التحميل
        function showLoadingState() {
            if (loginBtn && loginBtnText && loginBtnLoading) {
                loginBtn.disabled = true;
                loginBtnText.classList.add('d-none');
                loginBtnLoading.classList.remove('d-none');
            }
        }

        // دالة لإخفاء حالة التحميل
        function hideLoadingState() {
            if (loginBtn && loginBtnText && loginBtnLoading) {
                loginBtn.disabled = false;
                loginBtnText.classList.remove('d-none');
                loginBtnLoading.classList.add('d-none');
            }
        }

        // معالجة إرسال النموذج
        if (loginForm) {
            loginForm.addEventListener('submit', function(e) {
                // التحقق من الحقول المطلوبة
                const username = document.getElementById('username').value.trim();
                const password = document.getElementById('password').value;

                if (!username || !password) {
                    e.preventDefault();

                    // إظهار رسالة خطأ
                    const alertDiv = document.createElement('div');
                    alertDiv.className = 'alert alert-danger alert-dismissible fade show';
                    alertDiv.innerHTML = `
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>خطأ:</strong> يرجى إدخال اسم المستخدم وكلمة المرور.
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    `;

                    // إدراج الرسالة قبل النموذج
                    loginForm.parentNode.insertBefore(alertDiv, loginForm);

                    // إزالة الرسالة بعد 5 ثوان
                    setTimeout(() => {
                        if (alertDiv.parentElement) {
                            alertDiv.remove();
                        }
                    }, 5000);

                    return false;
                }

                // إظهار حالة التحميل
                showLoadingState();

                // إضافة timeout للحماية من التعليق
                setTimeout(function() {
                    hideLoadingState();
                }, 15000); // 15 ثانية
            });
        }

        // إخفاء حالة التحميل عند تحميل الصفحة
        window.addEventListener('pageshow', function() {
            hideLoadingState();
        });

        // تحسينات إضافية للتجربة
        const alerts = document.querySelectorAll('.alert-dismissible');
        alerts.forEach(alert => {
            // إزالة تلقائية بعد 8 ثوان
            setTimeout(() => {
                if (alert.parentElement) {
                    alert.style.transition = 'all 0.3s ease';
                    alert.style.transform = 'translateY(-20px)';
                    alert.style.opacity = '0';
                    setTimeout(() => {
                        if (alert.parentElement) {
                            alert.remove();
                        }
                    }, 300);
                }
            }, 8000);
        });
    });
</script>

{% endblock %}