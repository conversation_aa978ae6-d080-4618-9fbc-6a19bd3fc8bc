#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
استيراد البيانات من SQLite إلى PostgreSQL
مع الحفاظ على جميع العلاقات والخصائص
"""

import os
import sqlite3
import pandas as pd
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from datetime import datetime
import json

class DataImporter:
    """فئة استيراد البيانات مع الحفاظ على التكامل"""
    
    def __init__(self, sqlite_path, postgres_uri):
        self.sqlite_path = sqlite_path
        self.postgres_uri = postgres_uri
        self.import_log = []
        
        # إنشاء محركات قاعدة البيانات
        self.sqlite_engine = create_engine(f'sqlite:///{sqlite_path}')
        self.postgres_engine = create_engine(postgres_uri)
        
    def log_message(self, message):
        """تسجيل رسائل الاستيراد"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.import_log.append(log_entry)
        print(log_entry)
    
    def get_table_order(self):
        """ترتيب الجداول حسب التبعيات لتجنب مشاكل المفاتيح الخارجية"""
        return [
            'role_settings',
            'user',
            'educational_level',
            'level_database',
            'level_data_entry',
            'schedule',
            'progress_entry',
            'admin_inspector_notification',
            'inspector_teacher_notification',
            'general_notification',
            'general_notification_read',
            'subscription_plan',
            'subscription',
            'payment',
            'subscription_notification',
            'subscription_extension_log',
            'user_session',
            'inspector_teacher',
            'news_update'
        ]
    
    def check_table_exists(self, table_name, engine):
        """التحقق من وجود الجدول"""
        try:
            with engine.connect() as conn:
                result = conn.execute(text(f"SELECT 1 FROM {table_name} LIMIT 1"))
                return True
        except:
            return False
    
    def get_sqlite_data(self, table_name):
        """استخراج البيانات من SQLite"""
        try:
            if not self.check_table_exists(table_name, self.sqlite_engine):
                self.log_message(f"⚠️ الجدول {table_name} غير موجود في SQLite")
                return pd.DataFrame()
            
            df = pd.read_sql_table(table_name, self.sqlite_engine)
            self.log_message(f"📤 تم استخراج {len(df)} سجل من جدول {table_name}")
            return df
        except Exception as e:
            self.log_message(f"❌ خطأ في استخراج البيانات من {table_name}: {str(e)}")
            return pd.DataFrame()
    
    def clean_data_for_postgres(self, df, table_name):
        """تنظيف البيانات للتوافق مع PostgreSQL"""
        if df.empty:
            return df
        
        # تحويل القيم الفارغة
        df = df.where(pd.notnull(df), None)
        
        # معالجة خاصة لجداول معينة
        if table_name == 'user':
            # التأكد من تنسيق التواريخ
            date_columns = ['created_at', 'updated_at', 'last_login']
            for col in date_columns:
                if col in df.columns:
                    df[col] = pd.to_datetime(df[col], errors='coerce')
        
        elif table_name == 'subscription':
            # معالجة تواريخ الاشتراكات
            date_columns = ['start_date', 'end_date', 'created_at', 'updated_at']
            for col in date_columns:
                if col in df.columns:
                    df[col] = pd.to_datetime(df[col], errors='coerce')
        
        elif table_name == 'payment':
            # معالجة تواريخ المدفوعات
            date_columns = ['created_at', 'updated_at']
            for col in date_columns:
                if col in df.columns:
                    df[col] = pd.to_datetime(df[col], errors='coerce')
        
        # معالجة الأعمدة النصية الطويلة
        for col in df.columns:
            if df[col].dtype == 'object':
                # قطع النصوص الطويلة جداً
                df[col] = df[col].astype(str).apply(
                    lambda x: x[:1000] if len(str(x)) > 1000 else x
                )
        
        return df
    
    def import_table_data(self, table_name, df):
        """استيراد البيانات إلى PostgreSQL"""
        if df.empty:
            self.log_message(f"⚠️ لا توجد بيانات لاستيراد في جدول {table_name}")
            return True
        
        try:
            # تنظيف البيانات
            df_clean = self.clean_data_for_postgres(df, table_name)
            
            # استيراد البيانات
            df_clean.to_sql(
                table_name, 
                self.postgres_engine, 
                if_exists='append', 
                index=False,
                method='multi'
            )
            
            self.log_message(f"✅ تم استيراد {len(df_clean)} سجل إلى جدول {table_name}")
            return True
            
        except Exception as e:
            self.log_message(f"❌ خطأ في استيراد البيانات إلى {table_name}: {str(e)}")
            return False
    
    def verify_data_integrity(self):
        """التحقق من سلامة البيانات بعد الاستيراد"""
        self.log_message("🔍 بدء التحقق من سلامة البيانات...")
        
        verification_results = {}
        
        for table_name in self.get_table_order():
            try:
                # عدد السجلات في SQLite
                sqlite_count = 0
                if self.check_table_exists(table_name, self.sqlite_engine):
                    with self.sqlite_engine.connect() as conn:
                        result = conn.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
                        sqlite_count = result.scalar()
                
                # عدد السجلات في PostgreSQL
                postgres_count = 0
                if self.check_table_exists(table_name, self.postgres_engine):
                    with self.postgres_engine.connect() as conn:
                        result = conn.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
                        postgres_count = result.scalar()
                
                verification_results[table_name] = {
                    'sqlite_count': sqlite_count,
                    'postgres_count': postgres_count,
                    'match': sqlite_count == postgres_count
                }
                
                status = "✅" if sqlite_count == postgres_count else "❌"
                self.log_message(f"{status} {table_name}: SQLite={sqlite_count}, PostgreSQL={postgres_count}")
                
            except Exception as e:
                self.log_message(f"❌ خطأ في التحقق من جدول {table_name}: {str(e)}")
                verification_results[table_name] = {
                    'sqlite_count': 0,
                    'postgres_count': 0,
                    'match': False,
                    'error': str(e)
                }
        
        return verification_results
    
    def run_import(self):
        """تشغيل عملية الاستيراد الكاملة"""
        self.log_message("🚀 بدء عملية استيراد البيانات...")
        
        success_count = 0
        total_tables = 0
        
        for table_name in self.get_table_order():
            total_tables += 1
            self.log_message(f"📋 معالجة جدول: {table_name}")
            
            # استخراج البيانات من SQLite
            df = self.get_sqlite_data(table_name)
            
            # استيراد البيانات إلى PostgreSQL
            if self.import_table_data(table_name, df):
                success_count += 1
        
        # التحقق من سلامة البيانات
        verification_results = self.verify_data_integrity()
        
        # تقرير النتائج
        self.log_message(f"📊 تم استيراد {success_count}/{total_tables} جدول بنجاح")
        
        # حفظ تقرير التحقق
        self.save_verification_report(verification_results)
        
        return success_count == total_tables
    
    def save_verification_report(self, verification_results):
        """حفظ تقرير التحقق من البيانات"""
        try:
            report_file = f"data_verification_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(verification_results, f, ensure_ascii=False, indent=2)
            
            self.log_message(f"📄 تم حفظ تقرير التحقق: {report_file}")
        except Exception as e:
            self.log_message(f"❌ خطأ في حفظ تقرير التحقق: {str(e)}")

def main():
    """الدالة الرئيسية للاستيراد"""
    print("📥 بدء عملية استيراد البيانات من SQLite إلى PostgreSQL")
    print("=" * 60)
    
    # إعدادات قواعد البيانات
    sqlite_path = "instance/ta9affi.db"
    postgres_uri = "postgresql://ta9affi_user:ta9affi_password@localhost:5432/ta9affi"
    
    # التحقق من وجود قاعدة البيانات المصدر
    if not os.path.exists(sqlite_path):
        print(f"❌ لم يتم العثور على قاعدة البيانات المصدر: {sqlite_path}")
        return False
    
    # إنشاء مثيل المستورد
    importer = DataImporter(sqlite_path, postgres_uri)
    
    try:
        # تشغيل عملية الاستيراد
        success = importer.run_import()
        
        if success:
            print("\n✅ تم الانتهاء من استيراد البيانات بنجاح!")
            print("🎉 يمكنك الآن استخدام PostgreSQL مع التطبيق")
        else:
            print("\n❌ حدثت مشاكل أثناء الاستيراد. راجع السجلات للتفاصيل.")
        
        return success
        
    except Exception as e:
        print(f"❌ خطأ عام في عملية الاستيراد: {str(e)}")
        return False

if __name__ == "__main__":
    main()
