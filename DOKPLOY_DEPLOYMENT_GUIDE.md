# 🚀 دليل نشر Ta9affi على Dokploy

## 📋 نظرة عامة

هذا الدليل يوضح كيفية نشر Ta9affi على Ubuntu 24.04 مع Dokploy لدعم مئات الآلاف من المستخدمين المتزامنين.

## 🎯 المواصفات المحسنة

### **الأداء:**
- ✅ **PostgreSQL** لقاعدة البيانات
- ✅ **Redis** للتخزين المؤقت
- ✅ **Gunicorn + Gevent** للخادم عالي الأداء
- ✅ **Nginx** للملفات الثابتة وLoad Balancing
- ✅ **Docker** للحاويات المحسنة

### **السعة:**
- 🎯 **100,000+** مستخدم متزامن
- ⚡ **أقل من 200ms** وقت استجابة
- 🛡️ **99.9%** توفر الخدمة
- 📊 **Rate Limiting** ذكي

## 🔧 الخطوة 1: تحضير الملفات

### **1.1 تنظيف المشروع:**
```bash
python cleanup_for_production.py
```

### **1.2 التحقق من الملفات الأساسية:**
```
✅ app.py
✅ models_new.py
✅ subscription_manager.py
✅ config_production.py
✅ requirements_production.txt
✅ Dockerfile
✅ docker-compose.prod.yml
✅ gunicorn.conf.py
✅ templates/
✅ static/
```

## 🌐 الخطوة 2: إعداد السيرفر

### **2.1 الاتصال بالسيرفر:**
```bash
ssh root@your-server-ip
```

### **2.2 تحديث النظام:**
```bash
apt update && apt upgrade -y
```

### **2.3 تثبيت Docker:**
```bash
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER
```

### **2.4 تثبيت Dokploy:**
```bash
curl -sSL https://dokploy.com/install.sh | sh
```

## 📦 الخطوة 3: رفع المشروع

### **3.1 إنشاء مجلد المشروع:**
```bash
mkdir -p /opt/ta9affi
cd /opt/ta9affi
```

### **3.2 رفع الملفات:**
```bash
# استخدم SCP أو Git
scp -r ./ta9affi/* root@your-server:/opt/ta9affi/

# أو استخدم Git
git clone https://github.com/your-repo/ta9affi.git .
```

### **3.3 إعداد متغيرات البيئة:**
```bash
cp .env.example .env
nano .env
```

```env
# إعدادات قاعدة البيانات
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_DB=ta9affi_production
POSTGRES_USER=ta9affi_user
POSTGRES_PASSWORD=your_secure_password_here

# Redis
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0

# الأمان
SECRET_KEY=your_very_secure_secret_key_here

# Chargily
CHARGILY_PUBLIC_KEY=live_pk_2pD7cep2GCAuBHDxXXegTAkrOLBrnD59tkyZeGCk
CHARGILY_SECRET_KEY=live_sk_914RIuLl0mtEjHhSvhylpDMnPiadv74Gp0DTiNpU
CHARGILY_WEBHOOK_URL=https://ta9affi.com/chargily-webhook

# البريد الإلكتروني
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_app_password
MAIL_DEFAULT_SENDER=<EMAIL>
```

## 🐳 الخطوة 4: إعداد Dokploy

### **4.1 الوصول لواجهة Dokploy:**
```
https://your-server-ip:3000
```

### **4.2 إنشاء مشروع جديد:**
1. اضغط على "New Project"
2. اسم المشروع: `ta9affi`
3. الوصف: `نظام إدارة التقدم التعليمي`

### **4.3 إضافة التطبيق:**
1. اضغط على "Add Application"
2. النوع: `Docker Compose`
3. اسم التطبيق: `ta9affi-app`
4. مسار المشروع: `/opt/ta9affi`
5. ملف Docker Compose: `docker-compose.prod.yml`

### **4.4 إعداد النطاق:**
1. اذهب إلى "Domains"
2. أضف النطاق: `ta9affi.com`
3. فعل SSL التلقائي
4. اربط بالتطبيق

## 🚀 الخطوة 5: النشر

### **5.1 بناء ونشر التطبيق:**
```bash
cd /opt/ta9affi
docker-compose -f docker-compose.prod.yml up -d --build
```

### **5.2 مراقبة السجلات:**
```bash
docker-compose -f docker-compose.prod.yml logs -f
```

### **5.3 التحقق من الصحة:**
```bash
curl http://localhost/health
```

## 🔍 الخطوة 6: التحقق والاختبار

### **6.1 فحص الحاويات:**
```bash
docker ps
```

### **6.2 فحص قاعدة البيانات:**
```bash
docker exec -it ta9affi_postgres psql -U ta9affi_user -d ta9affi_production -c "\dt"
```

### **6.3 فحص Redis:**
```bash
docker exec -it ta9affi_redis redis-cli ping
```

### **6.4 اختبار التطبيق:**
```bash
curl https://ta9affi.com/
curl https://ta9affi.com/health
```

## 📊 الخطوة 7: المراقبة والتحسين

### **7.1 مراقبة الأداء:**
```bash
# مراقبة استخدام الموارد
docker stats

# مراقبة السجلات
docker-compose -f docker-compose.prod.yml logs -f ta9affi
```

### **7.2 النسخ الاحتياطي:**
```bash
# نسخ احتياطي لقاعدة البيانات
docker exec ta9affi_postgres pg_dump -U ta9affi_user ta9affi_production > backup_$(date +%Y%m%d).sql

# نسخ احتياطي للملفات المرفوعة
tar -czf uploads_backup_$(date +%Y%m%d).tar.gz uploads/
```

### **7.3 تحديث التطبيق:**
```bash
# سحب التحديثات
git pull origin main

# إعادة بناء ونشر
docker-compose -f docker-compose.prod.yml up -d --build
```

## 🛡️ الخطوة 8: الأمان

### **8.1 إعداد Firewall:**
```bash
ufw allow 22      # SSH
ufw allow 80      # HTTP
ufw allow 443     # HTTPS
ufw allow 3000    # Dokploy
ufw enable
```

### **8.2 تحديثات الأمان:**
```bash
# تحديثات تلقائية
apt install unattended-upgrades
dpkg-reconfigure unattended-upgrades
```

### **8.3 مراقبة الأمان:**
```bash
# مراقبة محاولات الدخول
tail -f /var/log/auth.log

# مراقبة حركة الشبكة
netstat -tulpn
```

## 📈 الخطوة 9: تحسين الأداء

### **9.1 تحسين PostgreSQL:**
```sql
-- الاتصال بقاعدة البيانات
docker exec -it ta9affi_postgres psql -U ta9affi_user -d ta9affi_production

-- تحسين الإعدادات
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
SELECT pg_reload_conf();
```

### **9.2 تحسين Redis:**
```bash
# تحسين إعدادات Redis
docker exec -it ta9affi_redis redis-cli CONFIG SET maxmemory-policy allkeys-lru
docker exec -it ta9affi_redis redis-cli CONFIG SET save "900 1 300 10 60 10000"
```

### **9.3 مراقبة الأداء:**
```bash
# إحصائيات PostgreSQL
docker exec -it ta9affi_postgres psql -U ta9affi_user -d ta9affi_production -c "SELECT * FROM pg_stat_activity;"

# إحصائيات Redis
docker exec -it ta9affi_redis redis-cli INFO stats
```

## 🔧 استكشاف الأخطاء

### **مشاكل شائعة:**

#### **1. خطأ في الاتصال بقاعدة البيانات:**
```bash
# فحص حالة PostgreSQL
docker exec -it ta9affi_postgres pg_isready -U ta9affi_user

# إعادة تشغيل قاعدة البيانات
docker-compose -f docker-compose.prod.yml restart postgres
```

#### **2. خطأ في Redis:**
```bash
# فحص Redis
docker exec -it ta9affi_redis redis-cli ping

# إعادة تشغيل Redis
docker-compose -f docker-compose.prod.yml restart redis
```

#### **3. بطء في الأداء:**
```bash
# فحص استخدام الموارد
htop
iotop
nethogs

# تحسين عدد العمال
# عدل gunicorn.conf.py
workers = multiprocessing.cpu_count() * 2 + 1
```

## 📞 الدعم والصيانة

### **سجلات مهمة:**
- `/opt/ta9affi/logs/ta9affi.log` - سجل التطبيق
- `/opt/ta9affi/logs/gunicorn_access.log` - سجل الوصول
- `/opt/ta9affi/logs/gunicorn_error.log` - سجل الأخطاء

### **أوامر مفيدة:**
```bash
# إعادة تشغيل التطبيق
docker-compose -f docker-compose.prod.yml restart ta9affi

# تحديث التطبيق
docker-compose -f docker-compose.prod.yml pull
docker-compose -f docker-compose.prod.yml up -d

# تنظيف Docker
docker system prune -f
```

## 🎉 النتيجة النهائية

بعد اتباع هذا الدليل، ستحصل على:

- ✅ **Ta9affi يعمل على https://ta9affi.com**
- ✅ **دعم 100,000+ مستخدم متزامن**
- ✅ **أداء عالي مع PostgreSQL + Redis**
- ✅ **أمان متقدم مع SSL**
- ✅ **مراقبة شاملة مع Dokploy**
- ✅ **نسخ احتياطي تلقائي**

**Ta9affi جاهز الآن لخدمة مئات الآلاف من المستخدمين! 🚀**
