# إعدادات Ta9affi للإنتاج مع Dokploy
# انسخ هذه الإعدادات إلى قسم Environment Variables في Dokploy

# ===========================================
# إعدادات Flask الأساسية
# ===========================================
FLASK_ENV=production
FLASK_APP=app.py
SECRET_KEY=ta9affi-production-secret-key-2024-very-secure

# ===========================================
# إعدادات قاعدة البيانات PostgreSQL
# ===========================================
POSTGRES_DB=ta9affi_production
POSTGRES_USER=ta9affi_user
POSTGRES_PASSWORD=ta9affi_secure_password_2024
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
DATABASE_URL=********************************************************************/ta9affi_production

# ===========================================
# إعدادات Redis للتخزين المؤقت
# ===========================================
REDIS_URL=redis://redis:6379/0
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0

# ===========================================
# إعدادات Chargily للدفع
# ===========================================
CHARGILY_PUBLIC_KEY=live_pk_2pD7cep2GCAuBHDxXXegTAkrOLBrnD59tkyZeGCk
CHARGILY_SECRET_KEY=live_sk_914RIuLl0mtEjHhSvhylpDMnPiadv74Gp0DTiNpU
CHARGILY_WEBHOOK_URL=https://ta9affi.com/chargily-webhook

# ===========================================
# إعدادات الأمان
# ===========================================
CSRF_SECRET_KEY=ta9affi-csrf-secret-2024
SESSION_COOKIE_SECURE=true
SESSION_COOKIE_HTTPONLY=true
SESSION_COOKIE_SAMESITE=Lax
PERMANENT_SESSION_LIFETIME=86400

# ===========================================
# إعدادات Rate Limiting
# ===========================================
RATELIMIT_STORAGE_URL=redis://redis:6379/1
RATELIMIT_STRATEGY=fixed-window
RATELIMIT_DEFAULT=1000 per hour

# ===========================================
# إعدادات الأداء
# ===========================================
SQLALCHEMY_ENGINE_OPTIONS_POOL_SIZE=50
SQLALCHEMY_ENGINE_OPTIONS_MAX_OVERFLOW=100
SQLALCHEMY_ENGINE_OPTIONS_POOL_TIMEOUT=30
SQLALCHEMY_ENGINE_OPTIONS_POOL_RECYCLE=3600

# ===========================================
# إعدادات التطبيق
# ===========================================
MAX_CONTENT_LENGTH=16777216
UPLOAD_FOLDER=/app/uploads
ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,pdf,doc,docx

# ===========================================
# إعدادات البريد الإلكتروني (اختياري)
# ===========================================
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=true
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# ===========================================
# إعدادات المراقبة
# ===========================================
SENTRY_DSN=
ANALYTICS_ID=

# ===========================================
# إعدادات التطوير (لا تستخدم في الإنتاج)
# ===========================================
# DEBUG=false
# TESTING=false
