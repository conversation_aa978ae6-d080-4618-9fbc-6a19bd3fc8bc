# Ta9affi Production Requirements - High Performance

# Flask والمكونات الأساسية
Flask==3.0.0
Flask-SQLAlchemy==3.1.1
Flask-Login==0.6.3
Flask-WTF==1.2.1
WTForms==3.1.1
Flask-Mail==0.9.1

# قاعدة البيانات
psycopg2-binary==2.9.9        # PostgreSQL adapter
SQLAlchemy==2.0.23
alembic==1.13.1               # Database migrations

# التخزين المؤقت والجلسات
redis==5.0.1                  # Redis client
Flask-Caching==2.1.0          # Caching support
Flask-Session==0.5.0          # Server-side sessions

# خادم الويب عالي الأداء
gunicorn==21.2.0              # WSGI HTTP Server
gevent==23.9.1                # Async networking library

# الأمان
Flask-Limiter==3.5.0          # Rate limiting
Flask-Talisman==1.1.0         # Security headers
cryptography==41.0.7          # Encryption

# ضغط الاستجابات
Flask-Compress==1.14          # Response compression

# معالجة التواريخ والوقت
python-dateutil==2.8.2
pytz==2023.3

# معالجة JSON والبيانات
ujson==5.8.0                  # Fast JSON parser
msgpack==1.0.7                # Binary serialization

# مراقبة الأداء
psutil==5.9.6                 # System monitoring
prometheus-flask-exporter==0.23.0  # Metrics export

# معالجة الملفات
Pillow==10.1.0                # Image processing
python-magic==0.4.27          # File type detection

# HTTP requests
requests==2.31.0
urllib3==2.1.0

# Chargily Payment
chargily-pay==1.0.0

# أدوات التطوير والإنتاج
python-dotenv==1.0.0          # Environment variables
click==8.1.7                  # CLI commands

# تسجيل محسن
structlog==23.2.0             # Structured logging

# معالجة النصوص العربية
python-bidi==0.4.2            # Arabic text support

# أدوات إضافية للأداء
eventlet==0.33.3              # Concurrent networking
greenlet==3.0.1               # Lightweight threads

# مكتبات النظام
six==1.16.0
setuptools==69.0.2
wheel==0.42.0

# أمان إضافي
bcrypt==4.1.2                 # Password hashing
PyJWT==2.8.0                  # JSON Web Tokens

# معالجة CSV والبيانات
pandas==2.1.4                 # Data analysis (للتقارير)
openpyxl==3.1.2              # Excel files

# إدارة المهام في الخلفية
celery==5.3.4                 # Task queue
kombu==5.3.4                  # Message transport

# مكتبات الشبكة
dnspython==2.4.2              # DNS toolkit
idna==3.6                     # Internationalized domain names

# أدوات التحقق والتطوير
marshmallow==3.20.1           # Serialization
webargs==8.4.0                # Request parsing

# مكتبات إضافية للاستقرار
packaging==23.2
pyparsing==3.1.1
certifi==2023.11.17
charset-normalizer==3.3.2

# أدوات المراقبة
sentry-sdk[flask]==1.38.0     # Error tracking

# إدارة الذاكرة
memory-profiler==0.61.0       # Memory usage profiling

# أدوات الشبكة المتقدمة
httpx==0.25.2                 # Modern HTTP client
anyio==4.1.0                  # Async I/O

# مكتبات الأمان المتقدمة
argon2-cffi==23.1.0           # Password hashing
passlib==1.7.4                # Password utilities

# أدوات التحليل
user-agents==2.2.0            # User agent parsing

# مكتبات الوقت المتقدمة
arrow==1.3.0                  # Better datetime handling

# أدوات النشر
supervisor==4.2.5             # Process control

# مكتبات إضافية للاستقرار في الإنتاج
typing-extensions==4.8.0
zipp==3.17.0
importlib-metadata==6.8.0
