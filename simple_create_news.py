#!/usr/bin/env python3
"""
إنشاء جدول الأخبار بطريقة بسيطة
"""

import sys
import os
import requests

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_news_via_web():
    """إنشاء الأخبار عبر الواجهة"""
    print("🚀 إنشاء أخبار تجريبية عبر الواجهة")
    print("=" * 50)
    
    # بيانات تسجيل الدخول للأدمن
    login_data = {
        'username': 'admin_thr',
        'password': 'Pugpotf9*'
    }
    
    session = requests.Session()
    
    try:
        # تسجيل الدخول
        print("🔐 تسجيل الدخول كأدمن...")
        login_response = session.post('http://127.0.0.1:5000/login', data=login_data)
        
        if login_response.status_code != 200:
            print("❌ فشل في تسجيل الدخول")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")
        
        # إضافة أخبار تجريبية
        sample_news = [
            {
                'title': 'مرحباً بكم في نظام Ta9affi',
                'content': 'نرحب بجميع المستخدمين في النظام الجديد لإدارة البرنامج السنوي للتدريس',
                'priority': '10',
                'is_active': 'on'
            },
            {
                'title': 'تحديث جديد للنظام',
                'content': 'تم إضافة ميزات جديدة لتحسين تجربة المستخدم وسهولة الاستخدام',
                'priority': '8',
                'is_active': 'on'
            },
            {
                'title': 'نصائح للاستخدام الأمثل',
                'content': 'ننصح بمراجعة دليل المستخدم للاستفادة من جميع ميزات النظام',
                'priority': '5',
                'is_active': 'on'
            }
        ]
        
        for i, news_data in enumerate(sample_news, 1):
            print(f"\n📰 إضافة الخبر {i}: {news_data['title']}")
            
            response = session.post(
                'http://127.0.0.1:5000/admin/news/add',
                data=news_data
            )
            
            if response.status_code == 200:
                if 'تم إضافة الخبر بنجاح' in response.text:
                    print(f"✅ تم إضافة الخبر بنجاح")
                elif 'موجود بالفعل' in response.text:
                    print(f"⚠️ الخبر موجود بالفعل")
                else:
                    print(f"❓ استجابة غير متوقعة")
            else:
                print(f"❌ فشل في إضافة الخبر: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {str(e)}")
        return False

def test_news_display():
    """اختبار عرض الأخبار في الصفحة الرئيسية"""
    print(f"\n🔍 اختبار عرض الأخبار في الصفحة الرئيسية")
    print("=" * 50)
    
    try:
        response = requests.get('http://127.0.0.1:5000/')
        
        if response.status_code == 200:
            if 'news-ticker-container' in response.text:
                print("✅ شريط الأخبار موجود في الصفحة الرئيسية")
                
                if 'مرحباً بكم في نظام Ta9affi' in response.text:
                    print("✅ الأخبار تظهر بشكل صحيح")
                else:
                    print("⚠️ الأخبار لا تظهر أو فارغة")
            else:
                print("❌ شريط الأخبار غير موجود في الصفحة")
        else:
            print(f"❌ فشل في الوصول للصفحة الرئيسية: {response.status_code}")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار العرض: {str(e)}")

def main():
    """تشغيل العمليات"""
    print("🚀 إعداد شريط الأخبار والتحديثات")
    print("=" * 60)
    
    # إنشاء الأخبار
    success = create_news_via_web()
    
    if success:
        # اختبار العرض
        test_news_display()
        
        print(f"\n" + "=" * 60)
        print(f"📋 ملخص العمليات:")
        print(f"   ✅ تم إضافة أخبار تجريبية")
        print(f"   ✅ شريط الأخبار جاهز للعمل")
        
        print(f"\n🎯 للاختبار:")
        print(f"   1. افتح الصفحة الرئيسية: http://127.0.0.1:5000/")
        print(f"   2. تحقق من ظهور شريط الأخبار")
        print(f"   3. سجل دخول كأدمن: admin_thr")
        print(f"   4. اذهب إلى: http://127.0.0.1:5000/admin/news")
        print(f"   5. أضف/عدل/احذف الأخبار")
        
        print(f"\n🎨 الميزات المطبقة:")
        print(f"   ✅ شريط أخبار متحرك مع تأثير flip-up")
        print(f"   ✅ لوحة تحكم كاملة للأدمن")
        print(f"   ✅ حد أقصى 10 أخبار نشطة")
        print(f"   ✅ ترتيب حسب الأولوية")
        print(f"   ✅ تصميم متجاوب")
    else:
        print(f"\n❌ فشل في إعداد الأخبار")
        print(f"🔧 تحقق من تشغيل الخادم وصحة البيانات")

if __name__ == "__main__":
    main()
