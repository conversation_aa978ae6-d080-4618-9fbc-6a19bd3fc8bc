#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير Redis للجلسات والتخزين المؤقت
يوفر إدارة محسنة للجلسات والبيانات المؤقتة
"""

import redis
import json
import pickle
from datetime import datetime, timedelta
from functools import wraps
import hashlib
import logging

class RedisManager:
    """مدير Redis للجلسات والتخزين المؤقت"""
    
    def __init__(self, redis_url='redis://localhost:6379/0'):
        """تهيئة مدير Redis"""
        try:
            self.redis_client = redis.from_url(redis_url, decode_responses=True)
            self.redis_binary = redis.from_url(redis_url, decode_responses=False)
            
            # اختبار الاتصال
            self.redis_client.ping()
            self.connected = True
            logging.info("✅ تم الاتصال بـ Redis بنجاح")
            
        except Exception as e:
            self.connected = False
            self.redis_client = None
            self.redis_binary = None
            logging.warning(f"⚠️ فشل الاتصال بـ Redis: {str(e)}")
    
    def is_available(self):
        """التحقق من توفر Redis"""
        return self.connected and self.redis_client is not None
    
    # ===== إدارة الجلسات =====
    
    def create_session(self, user_id, session_data, expire_hours=24):
        """إنشاء جلسة جديدة"""
        if not self.is_available():
            return None
        
        try:
            session_id = f"session:{user_id}:{datetime.now().timestamp()}"
            session_key = f"user_session:{session_id}"
            
            # بيانات الجلسة
            session_info = {
                'user_id': user_id,
                'created_at': datetime.now().isoformat(),
                'last_activity': datetime.now().isoformat(),
                'ip_address': session_data.get('ip_address', ''),
                'user_agent': session_data.get('user_agent', ''),
                'is_active': True
            }
            
            # حفظ الجلسة مع انتهاء صلاحية
            self.redis_client.setex(
                session_key, 
                timedelta(hours=expire_hours), 
                json.dumps(session_info)
            )
            
            # إضافة الجلسة لقائمة جلسات المستخدم
            user_sessions_key = f"user_sessions:{user_id}"
            self.redis_client.sadd(user_sessions_key, session_id)
            self.redis_client.expire(user_sessions_key, timedelta(hours=expire_hours))
            
            logging.info(f"✅ تم إنشاء جلسة جديدة للمستخدم {user_id}")
            return session_id
            
        except Exception as e:
            logging.error(f"❌ خطأ في إنشاء الجلسة: {str(e)}")
            return None
    
    def get_session(self, session_id):
        """الحصول على بيانات الجلسة"""
        if not self.is_available():
            return None
        
        try:
            session_key = f"user_session:{session_id}"
            session_data = self.redis_client.get(session_key)
            
            if session_data:
                return json.loads(session_data)
            return None
            
        except Exception as e:
            logging.error(f"❌ خطأ في الحصول على الجلسة: {str(e)}")
            return None
    
    def update_session_activity(self, session_id):
        """تحديث آخر نشاط للجلسة"""
        if not self.is_available():
            return False
        
        try:
            session_key = f"user_session:{session_id}"
            session_data = self.get_session(session_id)
            
            if session_data:
                session_data['last_activity'] = datetime.now().isoformat()
                
                # تحديث البيانات مع تمديد انتهاء الصلاحية
                self.redis_client.setex(
                    session_key, 
                    timedelta(hours=24), 
                    json.dumps(session_data)
                )
                return True
            return False
            
        except Exception as e:
            logging.error(f"❌ خطأ في تحديث نشاط الجلسة: {str(e)}")
            return False
    
    def end_session(self, session_id):
        """إنهاء جلسة محددة"""
        if not self.is_available():
            return False
        
        try:
            session_key = f"user_session:{session_id}"
            session_data = self.get_session(session_id)
            
            if session_data:
                user_id = session_data['user_id']
                
                # حذف الجلسة
                self.redis_client.delete(session_key)
                
                # إزالة من قائمة جلسات المستخدم
                user_sessions_key = f"user_sessions:{user_id}"
                self.redis_client.srem(user_sessions_key, session_id)
                
                logging.info(f"✅ تم إنهاء الجلسة {session_id}")
                return True
            return False
            
        except Exception as e:
            logging.error(f"❌ خطأ في إنهاء الجلسة: {str(e)}")
            return False
    
    def end_all_user_sessions(self, user_id):
        """إنهاء جميع جلسات المستخدم"""
        if not self.is_available():
            return False
        
        try:
            user_sessions_key = f"user_sessions:{user_id}"
            session_ids = self.redis_client.smembers(user_sessions_key)
            
            ended_count = 0
            for session_id in session_ids:
                if self.end_session(session_id):
                    ended_count += 1
            
            # حذف قائمة الجلسات
            self.redis_client.delete(user_sessions_key)
            
            logging.info(f"✅ تم إنهاء {ended_count} جلسة للمستخدم {user_id}")
            return True
            
        except Exception as e:
            logging.error(f"❌ خطأ في إنهاء جلسات المستخدم: {str(e)}")
            return False
    
    def get_online_users_count(self):
        """حساب عدد المستخدمين المتصلين"""
        if not self.is_available():
            return 0
        
        try:
            # البحث عن جميع مفاتيح الجلسات النشطة
            session_keys = self.redis_client.keys("user_session:*")
            
            active_users = set()
            for key in session_keys:
                session_data = self.redis_client.get(key)
                if session_data:
                    session_info = json.loads(session_data)
                    if session_info.get('is_active', False):
                        active_users.add(session_info['user_id'])
            
            return len(active_users)
            
        except Exception as e:
            logging.error(f"❌ خطأ في حساب المستخدمين المتصلين: {str(e)}")
            return 0
    
    # ===== التخزين المؤقت =====
    
    def cache_set(self, key, value, expire_seconds=3600):
        """حفظ قيمة في التخزين المؤقت"""
        if not self.is_available():
            return False
        
        try:
            cache_key = f"cache:{key}"
            
            # تحويل القيمة إلى JSON إذا لزم الأمر
            if isinstance(value, (dict, list)):
                value = json.dumps(value)
            
            self.redis_client.setex(cache_key, expire_seconds, value)
            return True
            
        except Exception as e:
            logging.error(f"❌ خطأ في حفظ التخزين المؤقت: {str(e)}")
            return False
    
    def cache_get(self, key):
        """الحصول على قيمة من التخزين المؤقت"""
        if not self.is_available():
            return None
        
        try:
            cache_key = f"cache:{key}"
            value = self.redis_client.get(cache_key)
            
            if value:
                # محاولة تحويل من JSON
                try:
                    return json.loads(value)
                except:
                    return value
            return None
            
        except Exception as e:
            logging.error(f"❌ خطأ في الحصول على التخزين المؤقت: {str(e)}")
            return None
    
    def cache_delete(self, key):
        """حذف قيمة من التخزين المؤقت"""
        if not self.is_available():
            return False
        
        try:
            cache_key = f"cache:{key}"
            return self.redis_client.delete(cache_key) > 0
            
        except Exception as e:
            logging.error(f"❌ خطأ في حذف التخزين المؤقت: {str(e)}")
            return False
    
    def cache_clear_pattern(self, pattern):
        """حذف جميع المفاتيح التي تطابق النمط"""
        if not self.is_available():
            return 0
        
        try:
            cache_pattern = f"cache:{pattern}"
            keys = self.redis_client.keys(cache_pattern)
            
            if keys:
                return self.redis_client.delete(*keys)
            return 0
            
        except Exception as e:
            logging.error(f"❌ خطأ في حذف التخزين المؤقت بالنمط: {str(e)}")
            return 0
    
    # ===== ديكوريتر التخزين المؤقت =====
    
    def cached(self, expire_seconds=3600, key_prefix=""):
        """ديكوريتر للتخزين المؤقت للدوال"""
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                if not self.is_available():
                    return func(*args, **kwargs)
                
                # إنشاء مفتاح فريد للتخزين المؤقت
                cache_key = f"{key_prefix}{func.__name__}:{hashlib.md5(str(args + tuple(kwargs.items())).encode()).hexdigest()}"
                
                # محاولة الحصول على النتيجة من التخزين المؤقت
                cached_result = self.cache_get(cache_key)
                if cached_result is not None:
                    return cached_result
                
                # تنفيذ الدالة وحفظ النتيجة
                result = func(*args, **kwargs)
                self.cache_set(cache_key, result, expire_seconds)
                
                return result
            return wrapper
        return decorator
    
    # ===== إحصائيات وصيانة =====
    
    def get_stats(self):
        """الحصول على إحصائيات Redis"""
        if not self.is_available():
            return {}
        
        try:
            info = self.redis_client.info()
            
            # إحصائيات مخصصة
            session_count = len(self.redis_client.keys("user_session:*"))
            cache_count = len(self.redis_client.keys("cache:*"))
            
            return {
                'connected_clients': info.get('connected_clients', 0),
                'used_memory_human': info.get('used_memory_human', '0B'),
                'total_commands_processed': info.get('total_commands_processed', 0),
                'keyspace_hits': info.get('keyspace_hits', 0),
                'keyspace_misses': info.get('keyspace_misses', 0),
                'active_sessions': session_count,
                'cached_items': cache_count
            }
            
        except Exception as e:
            logging.error(f"❌ خطأ في الحصول على إحصائيات Redis: {str(e)}")
            return {}
    
    def cleanup_expired_sessions(self):
        """تنظيف الجلسات المنتهية الصلاحية"""
        if not self.is_available():
            return 0
        
        try:
            cleaned_count = 0
            session_keys = self.redis_client.keys("user_session:*")
            
            for key in session_keys:
                # التحقق من انتهاء الصلاحية
                ttl = self.redis_client.ttl(key)
                if ttl == -2:  # المفتاح غير موجود
                    cleaned_count += 1
                elif ttl == -1:  # المفتاح بدون انتهاء صلاحية
                    # حذف الجلسات القديمة جداً (أكثر من 7 أيام)
                    session_data = self.redis_client.get(key)
                    if session_data:
                        session_info = json.loads(session_data)
                        created_at = datetime.fromisoformat(session_info['created_at'])
                        if datetime.now() - created_at > timedelta(days=7):
                            self.redis_client.delete(key)
                            cleaned_count += 1
            
            logging.info(f"🧹 تم تنظيف {cleaned_count} جلسة منتهية الصلاحية")
            return cleaned_count
            
        except Exception as e:
            logging.error(f"❌ خطأ في تنظيف الجلسات: {str(e)}")
            return 0

# إنشاء مثيل عام لمدير Redis
redis_manager = RedisManager()
