#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير التخزين المؤقت للاستعلامات والبيانات الثقيلة
يحسن الأداء بشكل كبير للمستخدمين المتزامنين
"""

from functools import wraps
import hashlib
import json
import logging
from datetime import datetime, timedelta
from redis_manager import redis_manager

class CacheManager:
    """مدير التخزين المؤقت المتقدم"""
    
    def __init__(self):
        self.default_timeout = 3600  # ساعة واحدة
        self.cache_stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0
        }
    
    def _generate_cache_key(self, prefix, *args, **kwargs):
        """إنشاء مفتاح فريد للتخزين المؤقت"""
        # تحويل المعاملات إلى نص قابل للتجمع
        key_data = f"{prefix}:{str(args)}:{str(sorted(kwargs.items()))}"
        
        # إنشاء hash للمفتاح الطويل
        key_hash = hashlib.md5(key_data.encode('utf-8')).hexdigest()
        
        return f"cache:{prefix}:{key_hash}"
    
    def get(self, key, default=None):
        """الحصول على قيمة من التخزين المؤقت"""
        try:
            if redis_manager.is_available():
                value = redis_manager.cache_get(key)
                if value is not None:
                    self.cache_stats['hits'] += 1
                    return value
                else:
                    self.cache_stats['misses'] += 1
                    return default
            return default
            
        except Exception as e:
            logging.error(f"❌ خطأ في الحصول على التخزين المؤقت: {str(e)}")
            self.cache_stats['misses'] += 1
            return default
    
    def set(self, key, value, timeout=None):
        """حفظ قيمة في التخزين المؤقت"""
        try:
            if redis_manager.is_available():
                timeout = timeout or self.default_timeout
                success = redis_manager.cache_set(key, value, timeout)
                if success:
                    self.cache_stats['sets'] += 1
                return success
            return False
            
        except Exception as e:
            logging.error(f"❌ خطأ في حفظ التخزين المؤقت: {str(e)}")
            return False
    
    def delete(self, key):
        """حذف قيمة من التخزين المؤقت"""
        try:
            if redis_manager.is_available():
                success = redis_manager.cache_delete(key)
                if success:
                    self.cache_stats['deletes'] += 1
                return success
            return False
            
        except Exception as e:
            logging.error(f"❌ خطأ في حذف التخزين المؤقت: {str(e)}")
            return False
    
    def clear_pattern(self, pattern):
        """حذف جميع المفاتيح التي تطابق النمط"""
        try:
            if redis_manager.is_available():
                count = redis_manager.cache_clear_pattern(pattern)
                self.cache_stats['deletes'] += count
                return count
            return 0
            
        except Exception as e:
            logging.error(f"❌ خطأ في حذف التخزين المؤقت بالنمط: {str(e)}")
            return 0
    
    # ===== ديكوريتر التخزين المؤقت =====
    
    def cached(self, timeout=None, key_prefix="", invalidate_on=None):
        """ديكوريتر للتخزين المؤقت للدوال"""
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                # إنشاء مفتاح التخزين المؤقت
                cache_key = self._generate_cache_key(
                    key_prefix or func.__name__, 
                    *args, 
                    **kwargs
                )
                
                # محاولة الحصول على النتيجة من التخزين المؤقت
                cached_result = self.get(cache_key)
                if cached_result is not None:
                    return cached_result
                
                # تنفيذ الدالة وحفظ النتيجة
                result = func(*args, **kwargs)
                self.set(cache_key, result, timeout or self.default_timeout)
                
                return result
            
            # إضافة دالة لحذف التخزين المؤقت
            wrapper.cache_clear = lambda *args, **kwargs: self.delete(
                self._generate_cache_key(key_prefix or func.__name__, *args, **kwargs)
            )
            
            return wrapper
        return decorator
    
    # ===== تخزين مؤقت للاستعلامات الشائعة =====
    
    def cache_user_data(self, user_id, data, timeout=1800):
        """تخزين مؤقت لبيانات المستخدم (30 دقيقة)"""
        key = f"user_data:{user_id}"
        return self.set(key, data, timeout)
    
    def get_cached_user_data(self, user_id):
        """الحصول على بيانات المستخدم المخزنة مؤقتاً"""
        key = f"user_data:{user_id}"
        return self.get(key)
    
    def cache_educational_data(self, level_id, data, timeout=7200):
        """تخزين مؤقت للبيانات التعليمية (ساعتان)"""
        key = f"educational_data:{level_id}"
        return self.set(key, data, timeout)
    
    def get_cached_educational_data(self, level_id):
        """الحصول على البيانات التعليمية المخزنة مؤقتاً"""
        key = f"educational_data:{level_id}"
        return self.get(key)
    
    def cache_statistics(self, stat_type, data, timeout=3600):
        """تخزين مؤقت للإحصائيات (ساعة واحدة)"""
        key = f"statistics:{stat_type}"
        return self.set(key, data, timeout)
    
    def get_cached_statistics(self, stat_type):
        """الحصول على الإحصائيات المخزنة مؤقتاً"""
        key = f"statistics:{stat_type}"
        return self.get(key)
    
    def cache_search_results(self, query, results, timeout=1800):
        """تخزين مؤقت لنتائج البحث (30 دقيقة)"""
        query_hash = hashlib.md5(query.encode('utf-8')).hexdigest()
        key = f"search_results:{query_hash}"
        return self.set(key, results, timeout)
    
    def get_cached_search_results(self, query):
        """الحصول على نتائج البحث المخزنة مؤقتاً"""
        query_hash = hashlib.md5(query.encode('utf-8')).hexdigest()
        key = f"search_results:{query_hash}"
        return self.get(key)
    
    # ===== إدارة التخزين المؤقت =====
    
    def invalidate_user_cache(self, user_id):
        """إلغاء التخزين المؤقت لمستخدم معين"""
        patterns = [
            f"user_data:{user_id}",
            f"user_*:{user_id}",
            f"*:user:{user_id}:*"
        ]
        
        total_deleted = 0
        for pattern in patterns:
            total_deleted += self.clear_pattern(pattern)
        
        logging.info(f"🗑️ تم حذف {total_deleted} عنصر من التخزين المؤقت للمستخدم {user_id}")
        return total_deleted
    
    def invalidate_educational_cache(self, level_id=None):
        """إلغاء التخزين المؤقت للبيانات التعليمية"""
        if level_id:
            pattern = f"educational_data:{level_id}"
        else:
            pattern = "educational_data:*"
        
        deleted_count = self.clear_pattern(pattern)
        logging.info(f"🗑️ تم حذف {deleted_count} عنصر من التخزين المؤقت التعليمي")
        return deleted_count
    
    def invalidate_statistics_cache(self):
        """إلغاء التخزين المؤقت للإحصائيات"""
        deleted_count = self.clear_pattern("statistics:*")
        logging.info(f"🗑️ تم حذف {deleted_count} عنصر من تخزين الإحصائيات المؤقت")
        return deleted_count
    
    def warm_up_cache(self):
        """تسخين التخزين المؤقت بالبيانات الأساسية"""
        try:
            from models_new import EducationalLevel, User
            
            # تخزين مؤقت للمستويات التعليمية
            levels = EducationalLevel.query.filter_by(is_active=True).all()
            for level in levels:
                level_data = {
                    'id': level.id,
                    'name': level.name,
                    'is_active': level.is_active
                }
                self.cache_educational_data(level.id, level_data, 7200)
            
            # تخزين مؤقت للإحصائيات الأساسية
            basic_stats = {
                'total_users': User.query.count(),
                'active_users': User.query.filter_by(is_active=True).count(),
                'total_levels': len(levels),
                'last_updated': datetime.now().isoformat()
            }
            self.cache_statistics('basic', basic_stats, 3600)
            
            logging.info("🔥 تم تسخين التخزين المؤقت بالبيانات الأساسية")
            
        except Exception as e:
            logging.error(f"❌ خطأ في تسخين التخزين المؤقت: {str(e)}")
    
    def get_cache_stats(self):
        """الحصول على إحصائيات التخزين المؤقت"""
        stats = self.cache_stats.copy()
        
        # إضافة إحصائيات Redis إذا كان متاحاً
        if redis_manager.is_available():
            redis_stats = redis_manager.get_stats()
            stats.update({
                'redis_memory': redis_stats.get('used_memory_human', '0B'),
                'redis_hits': redis_stats.get('keyspace_hits', 0),
                'redis_misses': redis_stats.get('keyspace_misses', 0),
                'cached_items': redis_stats.get('cached_items', 0)
            })
        
        # حساب معدل النجاح
        total_requests = stats['hits'] + stats['misses']
        if total_requests > 0:
            stats['hit_rate'] = round((stats['hits'] / total_requests) * 100, 2)
        else:
            stats['hit_rate'] = 0
        
        return stats
    
    def reset_stats(self):
        """إعادة تعيين إحصائيات التخزين المؤقت"""
        self.cache_stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0
        }

# إنشاء مثيل عام لمدير التخزين المؤقت
cache_manager = CacheManager()
