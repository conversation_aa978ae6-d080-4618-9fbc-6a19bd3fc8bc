#!/usr/bin/env python3
"""
عرض قائمة المستخدمين باستخدام Flask Shell
"""

import os
import sys
from app import app, db

def list_all_users():
    """عرض جميع المستخدمين في قاعدة البيانات"""
    
    with app.app_context():
        try:
            # محاولة استيراد نموذج User
            try:
                from models_new import User
            except ImportError:
                try:
                    from models import User
                except ImportError:
                    print("❌ لا يمكن العثور على نموذج User")
                    return
            
            # الحصول على جميع المستخدمين
            users = User.query.all()
            
            if not users:
                print("❌ لا توجد مستخدمين في قاعدة البيانات")
                return
            
            print(f"📊 إجمالي المستخدمين: {len(users)}")
            print("=" * 80)
            
            for user in users:
                # تحويل الدور إلى عربي
                role_ar = {
                    'admin': 'أدمن 👑',
                    'teacher': 'أستاذ 👨‍🏫', 
                    'inspector': 'مفتش 🔍',
                    'user_manager': 'مدير مستخدمين 👥'
                }.get(user.role, user.role)
                
                # حالة النشاط
                status = "نشط ✅" if getattr(user, 'is_active', True) else "معطل ❌"
                
                print(f"🆔 المعرف: {user.id}")
                print(f"👤 اسم المستخدم: {user.username}")
                print(f"📧 البريد: {getattr(user, 'email', 'غير محدد')}")
                print(f"🎭 الدور: {role_ar}")
                print(f"📱 الهاتف: {getattr(user, 'phone_number', 'غير محدد') or 'غير محدد'}")
                print(f"🏛️ الولاية: {getattr(user, 'wilaya_code', 'غير محدد') or 'غير محدد'}")
                print(f"🔄 الحالة: {status}")
                print(f"📅 تاريخ الإنشاء: {getattr(user, 'created_at', 'غير محدد') or 'غير محدد'}")
                print("-" * 50)
            
            # إحصائيات حسب الدور
            from sqlalchemy import func
            stats = db.session.query(User.role, func.count(User.id)).group_by(User.role).all()
            
            print("\n📈 الإحصائيات حسب الدور:")
            for role, count in stats:
                role_ar = {
                    'admin': 'أدمن',
                    'teacher': 'أستاذ',
                    'inspector': 'مفتش', 
                    'user_manager': 'مدير مستخدمين'
                }.get(role, role)
                print(f"   {role_ar}: {count}")
            
        except Exception as e:
            print(f"❌ خطأ في قراءة قاعدة البيانات: {str(e)}")
            print(f"تفاصيل الخطأ: {type(e).__name__}")

if __name__ == '__main__':
    list_all_users()