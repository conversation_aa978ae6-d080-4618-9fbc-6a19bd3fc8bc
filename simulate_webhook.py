"""
محاكاة webhook من Chargily لتفعيل الاشتراك
"""

from app import app
from models_new import Payment, User
from subscription_manager import subscription_manager
import json

def simulate_successful_payment(payment_id):
    """محاكاة دفع ناجح لعملية دفع معينة"""
    
    with app.app_context():
        # البحث عن عملية الدفع
        payment = Payment.query.get(payment_id)
        if not payment:
            print(f"عملية الدفع {payment_id} غير موجودة")
            return False
            
        user = User.query.get(payment.user_id)
        print(f"محاكاة دفع ناجح للمستخدم: {user.username}")
        print(f"المبلغ: {payment.amount} دج")
        print(f"Checkout ID: {payment.chargily_checkout_id}")
        
        # إنشاء بيانات webhook مزيفة
        webhook_data = {
            'checkout_id': payment.chargily_checkout_id,
            'status': 'paid',
            'amount': payment.amount,
            'currency': payment.currency
        }
        
        # معالجة webhook
        success = subscription_manager.process_payment_webhook(webhook_data)
        
        if success:
            print("✅ تم تفعيل الاشتراك بنجاح!")
            
            # عرض معلومات الاشتراك الجديد
            user = User.query.get(payment.user_id)  # إعادة تحميل المستخدم
            print(f"حالة الاشتراك: {user.subscription_status}")
            if user.has_active_subscription:
                print("✅ المستخدم لديه اشتراك نشط")
                active_sub = user.active_subscription
                if active_sub:
                    print(f"تاريخ انتهاء الاشتراك: {active_sub.end_date}")
                    print(f"الأيام المتبقية: {active_sub.days_remaining}")
            else:
                print("❌ المستخدم ليس لديه اشتراك نشط")
                
        else:
            print("❌ فشل في تفعيل الاشتراك")
            
        return success

def list_pending_payments():
    """عرض عمليات الدفع المعلقة"""
    
    with app.app_context():
        payments = Payment.query.filter_by(status='pending').order_by(Payment.created_at.desc()).all()
        
        if not payments:
            print("لا توجد عمليات دفع معلقة")
            return
            
        print("عمليات الدفع المعلقة:")
        print("-" * 50)
        
        for payment in payments:
            user = User.query.get(payment.user_id)
            print(f"ID: {payment.id}")
            print(f"المستخدم: {user.username}")
            print(f"المبلغ: {payment.amount} دج")
            print(f"التاريخ: {payment.created_at}")
            print(f"Checkout ID: {payment.chargily_checkout_id}")
            print("-" * 30)

if __name__ == "__main__":
    print("=== محاكاة webhook Chargily ===")
    print()
    
    # عرض عمليات الدفع المعلقة
    list_pending_payments()
    
    print()
    payment_id = input("أدخل ID عملية الدفع التي تريد تفعيلها: ")
    
    try:
        payment_id = int(payment_id)
        simulate_successful_payment(payment_id)
    except ValueError:
        print("يرجى إدخال رقم صحيح")
    except Exception as e:
        print(f"خطأ: {str(e)}")
