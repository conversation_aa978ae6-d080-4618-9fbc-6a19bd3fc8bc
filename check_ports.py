#!/usr/bin/env python3
"""
فحص سريع للمنافذ والخدمات
"""

import subprocess
import socket

def check_port_open(host, port, timeout=5):
    """فحص إذا كان المنفذ مفتوح"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except:
        return False

def check_listening_ports():
    """فحص المنافذ التي يستمع عليها النظام"""
    print("🔍 فحص المنافذ المفتوحة:")
    
    try:
        result = subprocess.run(['netstat', '-tlnp'], capture_output=True, text=True)
        lines = result.stdout.split('\n')
        
        important_ports = ['80', '443', '8000', '8001']
        found_ports = []
        
        for line in lines:
            if 'LISTEN' in line:
                for port in important_ports:
                    if f':{port}' in line:
                        parts = line.split()
                        if len(parts) > 6:
                            process = parts[6] if parts[6] != '-' else 'غير محدد'
                        else:
                            process = 'غير محدد'
                        print(f"   ✅ المنفذ {port}: {process}")
                        found_ports.append(port)
        
        for port in important_ports:
            if port not in found_ports:
                print(f"   ❌ المنفذ {port}: مغلق")
                
    except FileNotFoundError:
        print("⚠️ netstat غير متاح، محاولة باستخدام ss...")
        try:
            result = subprocess.run(['ss', '-tlnp'], capture_output=True, text=True)
            print(result.stdout)
        except:
            print("❌ ss غير متاح أيضاً")

def check_external_connectivity():
    """فحص الاتصال الخارجي"""
    print("\n🌐 فحص الاتصال الخارجي:")
    
    hosts_ports = [
        ('ta9affi.com', 80),
        ('ta9affi.com', 443),
        ('*************', 80),
        ('*************', 443)
    ]
    
    for host, port in hosts_ports:
        if check_port_open(host, port):
            print(f"   ✅ {host}:{port} - متاح")
        else:
            print(f"   ❌ {host}:{port} - غير متاح")

def check_dokploy_status():
    """فحص حالة Dokploy"""
    print("\n🐳 فحص حالة Dokploy:")
    
    try:
        # فحص Docker containers
        result = subprocess.run(['docker', 'ps'], capture_output=True, text=True)
        if 'dokploy' in result.stdout.lower():
            print("   ✅ Dokploy يعمل")
        else:
            print("   ❌ Dokploy لا يعمل أو غير موجود")
            
        # فحص خدمة Dokploy
        result = subprocess.run(['systemctl', 'status', 'dokploy'], capture_output=True, text=True)
        if 'active (running)' in result.stdout:
            print("   ✅ خدمة Dokploy نشطة")
        else:
            print("   ⚠️ خدمة Dokploy غير نشطة")
            
    except FileNotFoundError:
        print("   ⚠️ Docker أو systemctl غير متاح")

def check_nginx_status():
    """فحص حالة Nginx"""
    print("\n🌐 فحص حالة Nginx:")
    
    try:
        result = subprocess.run(['systemctl', 'status', 'nginx'], capture_output=True, text=True)
        if 'active (running)' in result.stdout:
            print("   ✅ Nginx يعمل")
            
            # فحص إعدادات Nginx
            try:
                result = subprocess.run(['nginx', '-t'], capture_output=True, text=True)
                if result.returncode == 0:
                    print("   ✅ إعدادات Nginx صحيحة")
                else:
                    print(f"   ❌ خطأ في إعدادات Nginx: {result.stderr}")
            except:
                print("   ⚠️ لا يمكن فحص إعدادات Nginx")
        else:
            print("   ❌ Nginx لا يعمل")
            
    except FileNotFoundError:
        print("   ❌ Nginx غير مثبت")

def check_firewall():
    """فحص إعدادات Firewall"""
    print("\n🛡️ فحص إعدادات Firewall:")
    
    try:
        # فحص UFW
        result = subprocess.run(['ufw', 'status'], capture_output=True, text=True)
        print(f"   UFW: {result.stdout.strip()}")
        
        if '443' in result.stdout:
            print("   ✅ المنفذ 443 مفتوح في UFW")
        else:
            print("   ❌ المنفذ 443 مغلق في UFW")
            
    except FileNotFoundError:
        print("   ⚠️ UFW غير متاح")
        
        # فحص iptables
        try:
            result = subprocess.run(['iptables', '-L'], capture_output=True, text=True)
            if '443' in result.stdout:
                print("   ✅ المنفذ 443 موجود في iptables")
            else:
                print("   ❌ المنفذ 443 غير موجود في iptables")
        except:
            print("   ⚠️ iptables غير متاح")

def suggest_solutions():
    """اقتراح حلول"""
    print("\n💡 اقتراحات الحلول:")
    print("="*40)
    
    print("1. 🔧 إعادة إعداد Dokploy:")
    print("   - اذهب إلى Dokploy Dashboard")
    print("   - احذف إعدادات Domain الحالية")
    print("   - أضف ta9affi.com مرة أخرى مع SSL")
    
    print("\n2. 🛡️ فتح المنفذ 443:")
    print("   sudo ufw allow 443")
    print("   sudo ufw reload")
    
    print("\n3. 🌐 إعداد Nginx يدوياً:")
    print("   sudo python setup_nginx_ssl.py")
    
    print("\n4. 🔍 فحص DNS:")
    print("   nslookup ta9affi.com")
    print("   dig ta9affi.com")
    
    print("\n5. 📋 فحص سجلات النظام:")
    print("   sudo journalctl -f | grep ssl")
    print("   sudo tail -f /var/log/nginx/error.log")

def main():
    """الدالة الرئيسية"""
    print("🔍 فحص سريع للمنافذ والخدمات")
    print("="*40)
    
    check_listening_ports()
    check_external_connectivity()
    check_dokploy_status()
    check_nginx_status()
    check_firewall()
    suggest_solutions()
    
    print("\n" + "="*40)
    print("✅ انتهى الفحص")

if __name__ == '__main__':
    main()
