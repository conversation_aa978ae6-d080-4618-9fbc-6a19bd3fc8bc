#!/usr/bin/env python3
"""
سكريبت لتوزيع التقدمات الموجودة على المستويات المختلفة لاختبار الدالة
"""

from models_new import db, ProgressEntry, LevelDataEntry, LevelDatabase, EducationalLevel
from app import app
from datetime import datetime, date
import random

def distribute_existing_progress():
    """
    توزيع التقدمات الموجودة على المستويات المختلفة
    """
    with app.app_context():
        try:
            print("=== بدء توزيع التقدمات على المستويات ===\n")
            
            # الحصول على جميع التقدمات الموجودة
            all_progress = ProgressEntry.query.all()
            print(f"إجمالي التقدمات الموجودة: {len(all_progress)}")
            
            if not all_progress:
                print("لا توجد تقدمات لتوزيعها")
                return
            
            # الحصول على جميع المستويات النشطة
            levels = EducationalLevel.query.filter_by(is_active=True).all()
            print(f"المستويات المتاحة: {[level.name for level in levels]}")
            
            # تجميع المواد المعرفية حسب المستوى
            materials_by_level = {}
            for level in levels:
                level_db = LevelDatabase.query.filter_by(level_id=level.id, is_active=True).first()
                if level_db:
                    materials = LevelDataEntry.query.filter_by(
                        database_id=level_db.id,
                        entry_type='material',
                        is_active=True
                    ).all()
                    materials_by_level[level.id] = {
                        'name': level.name,
                        'materials': materials
                    }
                    print(f"  - {level.name}: {len(materials)} مادة معرفية")
            
            print("\n=== بدء التوزيع ===")
            
            # توزيع التقدمات
            updated_count = 0
            for i, progress in enumerate(all_progress):
                # اختيار مستوى عشوائي
                level_id = random.choice(list(materials_by_level.keys()))
                level_info = materials_by_level[level_id]
                
                if level_info['materials']:
                    # اختيار مادة معرفية عشوائية من المستوى
                    material = random.choice(level_info['materials'])
                    
                    # تحديث التقدم
                    progress.material_id = material.id
                    progress.level_id = level_id
                    
                    # الحصول على المادة الدراسية والميدان/النشاط
                    if material.parent_id:
                        domain = LevelDataEntry.query.filter_by(
                            id=material.parent_id,
                            entry_type='domain'
                        ).first()
                        if domain:
                            progress.domain_id = domain.id
                            
                            if domain.parent_id:
                                subject = LevelDataEntry.query.filter_by(
                                    id=domain.parent_id,
                                    entry_type='subject'
                                ).first()
                                if subject:
                                    progress.subject_id = subject.id
                    
                    updated_count += 1
                    print(f"  {i+1}. تم تحديث التقدم {progress.id} -> {level_info['name']} - {material.name}")
            
            # حفظ التغييرات
            db.session.commit()
            print(f"\n✅ تم تحديث {updated_count} تقدم بنجاح")
            
            # عرض التوزيع النهائي
            print("\n=== التوزيع النهائي ===")
            for level_id, level_info in materials_by_level.items():
                count = ProgressEntry.query.filter_by(level_id=level_id).count()
                print(f"  - {level_info['name']}: {count} تقدم")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ خطأ في التوزيع: {str(e)}")

def create_sample_progress():
    """
    إنشاء تقدمات تجريبية إضافية لاختبار أفضل
    """
    with app.app_context():
        try:
            print("\n=== إنشاء تقدمات تجريبية إضافية ===")
            
            # الحصول على المستخدم الحالي (افتراض أن المعلم له ID = 1)
            user_id = 1  # يمكنك تغيير هذا حسب المعلم المطلوب
            
            # الحصول على المستويات والمواد
            levels = EducationalLevel.query.filter_by(is_active=True).all()
            
            statuses = ['completed', 'in_progress', 'planned']
            
            created_count = 0
            for level in levels:
                level_db = LevelDatabase.query.filter_by(level_id=level.id, is_active=True).first()
                if level_db:
                    materials = LevelDataEntry.query.filter_by(
                        database_id=level_db.id,
                        entry_type='material',
                        is_active=True
                    ).limit(5).all()  # أخذ 5 مواد من كل مستوى
                    
                    for material in materials:
                        # التحقق من عدم وجود تقدم مسبق لهذه المادة
                        existing = ProgressEntry.query.filter_by(
                            user_id=user_id,
                            material_id=material.id
                        ).first()
                        
                        if not existing:
                            # إنشاء تقدم جديد
                            new_progress = ProgressEntry(
                                user_id=user_id,
                                material_id=material.id,
                                level_id=level.id,
                                status=random.choice(statuses),
                                date=date.today(),
                                notes=f"تقدم تجريبي في {material.name}"
                            )
                            
                            # إضافة معلومات المادة الدراسية والميدان/النشاط
                            if material.parent_id:
                                domain = LevelDataEntry.query.filter_by(
                                    id=material.parent_id,
                                    entry_type='domain'
                                ).first()
                                if domain:
                                    new_progress.domain_id = domain.id
                                    
                                    if domain.parent_id:
                                        subject = LevelDataEntry.query.filter_by(
                                            id=domain.parent_id,
                                            entry_type='subject'
                                        ).first()
                                        if subject:
                                            new_progress.subject_id = subject.id
                            
                            db.session.add(new_progress)
                            created_count += 1
                            print(f"  ✅ إنشاء تقدم: {level.name} - {material.name}")
            
            db.session.commit()
            print(f"\n✅ تم إنشاء {created_count} تقدم تجريبي جديد")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ خطأ في إنشاء التقدمات: {str(e)}")

if __name__ == "__main__":
    print("اختر العملية:")
    print("1. توزيع التقدمات الموجودة على المستويات")
    print("2. إنشاء تقدمات تجريبية جديدة")
    print("3. كلاهما")
    
    choice = input("أدخل اختيارك (1/2/3): ").strip()
    
    if choice == "1":
        distribute_existing_progress()
    elif choice == "2":
        create_sample_progress()
    elif choice == "3":
        distribute_existing_progress()
        create_sample_progress()
    else:
        print("اختيار غير صحيح")
