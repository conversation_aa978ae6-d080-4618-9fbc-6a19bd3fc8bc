#!/bin/bash
# سكريبت تشغيل Ta9affi في بيئة الإنتاج
# مع Gunicorn و Nginx

set -e  # إيقاف عند أي خطأ

# ألوان للرسائل
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# دوال المساعدة
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# متغيرات الإعداد
APP_DIR="/path/to/ta9affi"
APP_USER="ta9affi"
VENV_DIR="$APP_DIR/venv"
GUNICORN_PID="$APP_DIR/logs/gunicorn.pid"
NGINX_CONF="/etc/nginx/sites-available/ta9affi"
NGINX_ENABLED="/etc/nginx/sites-enabled/ta9affi"

# التحقق من المتطلبات
check_requirements() {
    log_info "التحقق من المتطلبات..."
    
    # التحقق من Python
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 غير مثبت"
        exit 1
    fi
    
    # التحقق من pip
    if ! command -v pip3 &> /dev/null; then
        log_error "pip3 غير مثبت"
        exit 1
    fi
    
    # التحقق من Nginx
    if ! command -v nginx &> /dev/null; then
        log_error "Nginx غير مثبت"
        exit 1
    fi
    
    # التحقق من PostgreSQL
    if ! command -v psql &> /dev/null; then
        log_warning "PostgreSQL غير مثبت - تأكد من تشغيله على خادم منفصل"
    fi
    
    # التحقق من Redis
    if ! command -v redis-cli &> /dev/null; then
        log_warning "Redis غير مثبت - تأكد من تشغيله على خادم منفصل"
    fi
    
    log_success "تم التحقق من المتطلبات"
}

# إعداد البيئة الافتراضية
setup_virtualenv() {
    log_info "إعداد البيئة الافتراضية..."
    
    if [ ! -d "$VENV_DIR" ]; then
        log_info "إنشاء بيئة افتراضية جديدة..."
        python3 -m venv "$VENV_DIR"
    fi
    
    # تفعيل البيئة الافتراضية
    source "$VENV_DIR/bin/activate"
    
    # تحديث pip
    pip install --upgrade pip
    
    # تثبيت المتطلبات
    log_info "تثبيت المتطلبات..."
    pip install -r requirements.txt
    
    log_success "تم إعداد البيئة الافتراضية"
}

# إعداد قاعدة البيانات
setup_database() {
    log_info "إعداد قاعدة البيانات..."
    
    # تفعيل البيئة الافتراضية
    source "$VENV_DIR/bin/activate"
    
    # تعيين متغيرات البيئة
    export FLASK_APP=app_postgresql.py
    export FLASK_ENV=production
    
    # تشغيل migrations
    if [ ! -d "migrations" ]; then
        log_info "تهيئة Flask-Migrate..."
        flask db init
    fi
    
    log_info "إنشاء migration جديد..."
    flask db migrate -m "Production deployment"
    
    log_info "تطبيق migrations..."
    flask db upgrade
    
    log_success "تم إعداد قاعدة البيانات"
}

# إنشاء مجلدات السجلات
create_log_dirs() {
    log_info "إنشاء مجلدات السجلات..."
    
    mkdir -p "$APP_DIR/logs"
    mkdir -p "/var/log/nginx"
    
    # تعيين الصلاحيات
    chown -R "$APP_USER:$APP_USER" "$APP_DIR/logs"
    chmod 755 "$APP_DIR/logs"
    
    log_success "تم إنشاء مجلدات السجلات"
}

# إعداد Nginx
setup_nginx() {
    log_info "إعداد Nginx..."
    
    # نسخ ملف الإعداد
    if [ -f "nginx_ta9affi.conf" ]; then
        cp nginx_ta9affi.conf "$NGINX_CONF"
        
        # تحديث المسارات في ملف الإعداد
        sed -i "s|/path/to/ta9affi|$APP_DIR|g" "$NGINX_CONF"
        
        # تفعيل الموقع
        if [ ! -L "$NGINX_ENABLED" ]; then
            ln -s "$NGINX_CONF" "$NGINX_ENABLED"
        fi
        
        # اختبار إعدادات Nginx
        nginx -t
        
        log_success "تم إعداد Nginx"
    else
        log_error "ملف nginx_ta9affi.conf غير موجود"
        exit 1
    fi
}

# بدء Gunicorn
start_gunicorn() {
    log_info "بدء Gunicorn..."
    
    # تفعيل البيئة الافتراضية
    source "$VENV_DIR/bin/activate"
    
    # تعيين متغيرات البيئة
    export FLASK_ENV=production
    export PYTHONPATH="$APP_DIR"
    
    # إيقاف Gunicorn إذا كان يعمل
    if [ -f "$GUNICORN_PID" ]; then
        log_info "إيقاف Gunicorn الحالي..."
        kill -TERM $(cat "$GUNICORN_PID") 2>/dev/null || true
        sleep 2
    fi
    
    # بدء Gunicorn
    cd "$APP_DIR"
    gunicorn --config gunicorn_config.py app_postgresql:app --daemon
    
    # التحقق من التشغيل
    sleep 3
    if [ -f "$GUNICORN_PID" ] && kill -0 $(cat "$GUNICORN_PID") 2>/dev/null; then
        log_success "تم بدء Gunicorn بنجاح (PID: $(cat $GUNICORN_PID))"
    else
        log_error "فشل في بدء Gunicorn"
        exit 1
    fi
}

# بدء Nginx
start_nginx() {
    log_info "بدء Nginx..."
    
    # إعادة تحميل إعدادات Nginx
    systemctl reload nginx
    
    # التحقق من حالة Nginx
    if systemctl is-active --quiet nginx; then
        log_success "Nginx يعمل بنجاح"
    else
        log_info "بدء Nginx..."
        systemctl start nginx
        
        if systemctl is-active --quiet nginx; then
            log_success "تم بدء Nginx بنجاح"
        else
            log_error "فشل في بدء Nginx"
            exit 1
        fi
    fi
}

# اختبار التطبيق
test_application() {
    log_info "اختبار التطبيق..."
    
    # انتظار قليل للتأكد من بدء الخدمات
    sleep 5
    
    # اختبار Gunicorn مباشرة
    if curl -s http://127.0.0.1:8000/health > /dev/null; then
        log_success "Gunicorn يستجيب بشكل صحيح"
    else
        log_warning "Gunicorn لا يستجيب على المنفذ 8000"
    fi
    
    # اختبار Nginx
    if curl -s http://localhost/ > /dev/null; then
        log_success "Nginx يستجيب بشكل صحيح"
    else
        log_warning "Nginx لا يستجيب على المنفذ 80"
    fi
    
    log_success "تم اختبار التطبيق"
}

# عرض معلومات النظام
show_status() {
    log_info "حالة النظام:"
    echo "=================================="
    
    # حالة Gunicorn
    if [ -f "$GUNICORN_PID" ] && kill -0 $(cat "$GUNICORN_PID") 2>/dev/null; then
        echo "✅ Gunicorn: يعمل (PID: $(cat $GUNICORN_PID))"
    else
        echo "❌ Gunicorn: متوقف"
    fi
    
    # حالة Nginx
    if systemctl is-active --quiet nginx; then
        echo "✅ Nginx: يعمل"
    else
        echo "❌ Nginx: متوقف"
    fi
    
    # حالة PostgreSQL
    if systemctl is-active --quiet postgresql 2>/dev/null; then
        echo "✅ PostgreSQL: يعمل"
    else
        echo "⚠️ PostgreSQL: غير معروف"
    fi
    
    # حالة Redis
    if redis-cli ping > /dev/null 2>&1; then
        echo "✅ Redis: يعمل"
    else
        echo "⚠️ Redis: غير متصل"
    fi
    
    echo "=================================="
    echo "🌐 التطبيق متاح على:"
    echo "   http://localhost/"
    echo "   https://localhost/ (إذا تم إعداد SSL)"
    echo "=================================="
}

# الدالة الرئيسية
main() {
    echo "🚀 بدء تشغيل Ta9affi في بيئة الإنتاج"
    echo "======================================"
    
    # التحقق من الصلاحيات
    if [ "$EUID" -ne 0 ]; then
        log_error "يجب تشغيل هذا السكريبت بصلاحيات root"
        exit 1
    fi
    
    # تغيير إلى مجلد التطبيق
    cd "$APP_DIR"
    
    # تنفيذ الخطوات
    check_requirements
    setup_virtualenv
    create_log_dirs
    setup_database
    setup_nginx
    start_gunicorn
    start_nginx
    test_application
    show_status
    
    log_success "تم تشغيل Ta9affi بنجاح في بيئة الإنتاج! 🎉"
}

# تشغيل الدالة الرئيسية
main "$@"
