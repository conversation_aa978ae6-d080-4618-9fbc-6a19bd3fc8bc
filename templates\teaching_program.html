{% extends 'base.html' %}

{% block content %}
<div class="row">
    <div class="col-md-12 d-flex justify-content-between align-items-center mb-4">
        <h2>البرنامج السنوي للتدريس</h2>
        {% if current_user.role == 'admin' %}
        <div>
            <a href="{{ url_for('cleanup_inactive_levels') }}" class="btn btn-danger"
                onclick="return confirm('هل أنت متأكد من حذف جميع المستويات التي ليس لديها قاعدة بيانات نشطة؟')">
                <i class="fas fa-trash me-1"></i> حذف المستويات غير النشطة
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- بيانات قواعد البيانات للمستويات -->
<script>
    const levelDatabases = {
        {% for level_id, db_id in level_databases.items() %}
    "{{ level_id }}": "{{ db_id }}",
        {% endfor %}
    };
</script>

<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-search me-1"></i>
                        اختيار الكفاءة المستهدفة
                    </div>
                    <div>
                        {% if current_user.has_active_subscription %}
                        <button type="button" id="addNewProgressBtn" class="btn btn-sm btn-success"
                            onclick="addNewProgress()">
                            <i class="fas fa-plus me-1"></i> إضافة تقدم جديد
                        </button>
                        {% else %}
                        <a href="{{ url_for('subscription_plans') }}" class="btn btn-sm btn-warning">
                            <i class="fas fa-lock me-1"></i> تجديد الاشتراك لإضافة التقدم
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="card-body">
                <form id="competencySelectionForm">
                    <div class="row mb-3">
                        <div class="col-md">
                            <label for="level" class="form-label">المستوى التعليمي</label>
                            <select class="form-select" id="level" required>
                                <option value="" selected disabled>اختر المستوى</option>
                                {% for level in levels %}
                                <option value="{{ level.id }}">{{ level.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md">
                            <label for="subject" class="form-label">المادة</label>
                            <select class="form-select" id="subject" disabled required>
                                <option value="" selected disabled>اختر المادة</option>
                            </select>
                        </div>
                        <div class="col-md">
                            <label for="domain" class="form-label">الميدان/النشاط</label>
                            <select class="form-select" id="domain" disabled required>
                                <option value="" selected disabled>اختر الميدان/النشاط</option>
                            </select>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md">
                            <label for="material" class="form-label">الموارد المعرفية</label>
                            <select class="form-select" id="material" disabled required>
                                <option value="" selected disabled>اختر الموارد المعرفية</option>
                            </select>
                        </div>
                        <div class="col-md">
                            <label for="competency" class="form-label">الكفاءة المستهدفة</label>
                            <select class="form-select" id="competency" disabled required>
                                <option value="" selected disabled>اختر الكفاءة</option>
                            </select>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row" id="competencyDetailsSection" style="display: none;">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header">
                <i class="fas fa-clipboard-list me-1"></i>
                تفاصيل الكفاءة المستهدفة
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <h5 id="competencyTitle">عنوان الكفاءة</h5>
                        <p id="competencyDescription" class="lead">وصف الكفاءة المستهدفة</p>

                        <div class="mt-4">
                            <h6>تسجيل التقدم</h6>
                            <form id="progressForm" method="POST" action="{{ url_for('add_progress') }}">
                                <input type="hidden" id="competency_id" name="competency_id">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="date" class="form-label">التاريخ</label>
                                        <input type="date" class="form-control" id="date" name="date" required
                                            value="{{ today }}">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="status" class="form-label">الحالة</label>
                                        <select class="form-select" id="status" name="status" required>
                                            <option value="" selected disabled>اختر الحالة</option>
                                            <option value="completed">مكتمل</option>
                                            <option value="in_progress">قيد التنفيذ</option>
                                            <option value="planned">مخطط</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="notes" class="form-label">ملاحظات</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                                </div>
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i> حفظ التقدم
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card h-100">
                            <div class="card-header">
                                <i class="fas fa-history me-1"></i>
                                سجل التقدم
                            </div>
                            <div class="card-body">
                                <div id="progressHistory">
                                    <p class="text-center text-muted">لا يوجد سجل تقدم لهذه الكفاءة</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Get form elements
        const levelSelect = document.getElementById('level');
        const subjectSelect = document.getElementById('subject');
        const domainSelect = document.getElementById('domain');
        const materialSelect = document.getElementById('material');
        const competencySelect = document.getElementById('competency');
        const competencyDetailsSection = document.getElementById('competencyDetailsSection');
        const competencyTitle = document.getElementById('competencyTitle');
        const competencyDescription = document.getElementById('competencyDescription');
        const competencyIdInput = document.getElementById('competency_id');

        // Function to reset dropdown options
        function resetDropdown(dropdown) {
            // Eliminar todas las opciones excepto la primera (placeholder)
            dropdown.innerHTML = '<option value="">اختر...</option>';
            dropdown.disabled = true;
        }

        // Set current date as default for progress form
        document.getElementById('date').valueAsDate = new Date();

        // Level change event
        levelSelect.addEventListener('change', function () {
            // Reset subsequent dropdowns
            resetDropdown(subjectSelect);
            resetDropdown(domainSelect);
            resetDropdown(materialSelect);
            resetDropdown(competencySelect);
            competencyDetailsSection.style.display = 'none';

            // Check if the level has a database
            const levelId = this.value;
            if (!levelDatabases[levelId]) {
                alert('لا توجد قاعدة بيانات نشطة لهذا المستوى');
                return;
            }

            // Enable subject dropdown and fetch subjects
            subjectSelect.disabled = false;

            // Fetch subjects for selected level
            fetch(`/api/subjects/${levelId}`)
                .then(response => response.json())
                .then(data => {
                    // Populate subject dropdown
                    if (data.length === 0) {
                        alert('لا توجد مواد دراسية لهذا المستوى');
                        return;
                    }

                    // Limpiar opciones existentes antes de agregar nuevas
                    resetDropdown(subjectSelect);
                    subjectSelect.disabled = false;

                    data.forEach(subject => {
                        const option = document.createElement('option');
                        option.value = subject.id;
                        option.textContent = subject.name;
                        subjectSelect.appendChild(option);
                    });
                })
                .catch(error => {
                    console.error('Error fetching subjects:', error);
                    alert('حدث خطأ أثناء جلب المواد الدراسية');
                });
        });

        // Subject change event
        subjectSelect.addEventListener('change', function () {
            // Reset subsequent dropdowns
            resetDropdown(domainSelect);
            resetDropdown(materialSelect);
            resetDropdown(competencySelect);
            competencyDetailsSection.style.display = 'none';

            // Enable domain dropdown and fetch domains
            domainSelect.disabled = false;

            // Fetch domains for selected subject
            fetch(`/api/domains/${this.value}`)
                .then(response => response.json())
                .then(data => {
                    // Populate domain dropdown
                    if (data.length === 0) {
                        alert('لا توجد ميادين لهذه المادة');
                        return;
                    }

                    // Limpiar opciones existentes antes de agregar nuevas
                    resetDropdown(domainSelect);
                    domainSelect.disabled = false;

                    data.forEach(domain => {
                        const option = document.createElement('option');
                        option.value = domain.id;
                        option.textContent = domain.name;
                        domainSelect.appendChild(option);
                    });
                })
                .catch(error => {
                    console.error('Error fetching domains:', error);
                    alert('حدث خطأ أثناء جلب الميادين');
                });
        });

        // Domain change event
        domainSelect.addEventListener('change', function () {
            // Reset subsequent dropdowns
            resetDropdown(materialSelect);
            resetDropdown(competencySelect);
            competencyDetailsSection.style.display = 'none';

            // Enable material dropdown and fetch materials
            materialSelect.disabled = false;

            // Fetch knowledge materials for selected domain
            fetch(`/api/knowledge-materials/${this.value}`)
                .then(response => response.json())
                .then(data => {
                    // Populate material dropdown
                    if (data.length === 0) {
                        alert('لا توجد موارد معرفية لهذا الميدان/النشاط');
                        return;
                    }

                    // Limpiar opciones existentes antes de agregar nuevas
                    resetDropdown(materialSelect);
                    materialSelect.disabled = false;

                    data.forEach(material => {
                        const option = document.createElement('option');
                        option.value = material.id;
                        option.textContent = material.name;
                        materialSelect.appendChild(option);
                    });
                })
                .catch(error => {
                    console.error('Error fetching materials:', error);
                    alert('حدث خطأ أثناء جلب الموارد المعرفية');
                });
        });

        // Material change event
        materialSelect.addEventListener('change', function () {
            // Reset competency dropdown
            resetDropdown(competencySelect);
            competencyDetailsSection.style.display = 'none';

            // Enable competency dropdown and fetch competencies
            competencySelect.disabled = false;

            // Fetch competencies for selected material
            fetch(`/api/competencies/${this.value}`)
                .then(response => response.json())
                .then(data => {
                    // Populate competency dropdown
                    if (data.length === 0) {
                        alert('لا توجد كفاءات مستهدفة لهذه الموارد المعرفية');
                        return;
                    }

                    // Limpiar opciones existentes antes de agregar nuevas
                    resetDropdown(competencySelect);
                    competencySelect.disabled = false;

                    data.forEach(competency => {
                        const option = document.createElement('option');
                        option.value = competency.id;

                        // استخدام الاسم إذا كان موجوداً، وإلا استخدام الوصف
                        const displayText = competency.name || competency.description;
                        option.textContent = displayText.substring(0, 50) + (displayText.length > 50 ? '...' : '');
                        option.setAttribute('data-description', competency.description || '');
                        competencySelect.appendChild(option);
                    });
                })
                .catch(error => {
                    console.error('Error fetching competencies:', error);
                    alert('حدث خطأ أثناء جلب الكفاءات المستهدفة');
                });
        });

        // Competency change event
        competencySelect.addEventListener('change', function () {
            // Show competency details section
            competencyDetailsSection.style.display = 'block';

            // Get selected option
            const selectedOption = this.options[this.selectedIndex];

            // Update competency details
            competencyTitle.textContent = `${levelSelect.options[levelSelect.selectedIndex].text} - ${subjectSelect.options[subjectSelect.selectedIndex].text}`;
            competencyDescription.textContent = selectedOption.getAttribute('data-description') || '';
            competencyIdInput.value = this.value;

            // Fetch progress history for this competency
            // This would be implemented in a real application
            // For now, we'll just show a placeholder
        });

        // Function to add new progress
        window.addNewProgress = function () {
            // Check if a competency is selected
            if (!competencySelect.value) {
                alert('يرجى اختيار الكفاءة المستهدفة أولاً');
                return;
            }

            // Get current form data
            const formData = new FormData(document.getElementById('progressForm'));

            // Validate required fields
            if (!formData.get('status') || !formData.get('date')) {
                alert('يرجى ملء جميع الحقول المطلوبة (التاريخ والحالة)');
                return;
            }

            // Submit the current progress
            fetch('/api/add_progress', {
                method: 'POST',
                body: formData
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Show success message
                        alert('تم إضافة التقدم بنجاح! يمكنك الآن إضافة تقدم جديد.');

                        // Reset the progress form but keep the competency selection
                        document.getElementById('progressForm').reset();
                        document.getElementById('competency_id').value = competencySelect.value;

                        // Set today's date again
                        const today = new Date().toISOString().split('T')[0];
                        document.getElementById('date').value = today;

                        // Update progress history if needed
                        // You can add code here to refresh the progress history section

                        // Focus on the status field for quick entry
                        document.getElementById('status').focus();
                    } else {
                        alert('حدث خطأ: ' + (data.message || 'فشل في إضافة التقدم'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ في الاتصال');
                });
        };

        // Helper function to reset dropdown
        function resetDropdown(dropdown) {
            dropdown.innerHTML = '<option value="" selected disabled>اختر</option>';
            dropdown.disabled = true;
        }
    });
</script>
{% endblock %}

<!-- قسم آخر التحديثات -->
{% if detailed_entries %}
<div class="row mt-5">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-tasks me-1"></i>
                        آخر التحديثات (آخر 10 تقدمات)
                    </div>
                    <div>
                        <a href="{{ url_for('teacher_dashboard') }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-eye me-1"></i> عرض جميع التقدمات
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                    <table class="table table-bordered table-hover table-sm">
                        <thead class="table-light">
                            <tr>
                                <th class="text-center" style="width: 100px;">
                                    <i class="fas fa-calendar-day me-1"></i> التاريخ
                                </th>
                                <th class="text-center" style="width: 120px;">
                                    <i class="fas fa-school me-1"></i> المستوى
                                </th>
                                <th class="text-center" style="width: 120px;">
                                    <i class="fas fa-book me-1"></i> المادة
                                </th>
                                <th class="text-center" style="width: 120px;">
                                    <i class="fas fa-sitemap me-1"></i> الميدان/النشاط
                                </th>
                                <th class="text-center" style="width: 150px;">
                                    <i class="fas fa-bookmark me-1"></i> الموارد المعرفية
                                </th>
                                <th class="text-center">
                                    <i class="fas fa-target me-1"></i> الكفاءة المستهدفة
                                </th>
                                <th class="text-center" style="width: 100px;">
                                    <i class="fas fa-flag me-1"></i> الحالة
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in detailed_entries %}
                            {% set entry = item.entry %}
                            <tr>
                                <td class="text-center">
                                    <small class="text-muted">
                                        {{ entry.date.strftime('%Y-%m-%d') if entry.date else 'غير محدد' }}
                                    </small>
                                </td>
                                <td class="text-center">
                                    <span class="badge bg-secondary">
                                        {{ entry.level.name if entry.level else 'غير محدد' }}
                                    </span>
                                </td>
                                <td class="text-center">
                                    <span class="badge bg-primary">
                                        {{ entry.subject.name if entry.subject else 'غير محدد' }}
                                    </span>
                                </td>
                                <td class="text-center">
                                    <span class="badge bg-info">
                                        {{ entry.domain.name if entry.domain else 'غير محدد' }}
                                    </span>
                                </td>
                                <td class="text-center">
                                    <span class="badge bg-warning text-dark">
                                        {{ entry.material.name if entry.material else 'غير محدد' }}
                                    </span>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        {{ item.competency_name[:50] + '...' if item.competency_name|length > 50 else
                                        item.competency_name }}
                                    </small>
                                </td>
                                <td class="text-center">
                                    {% if entry.status == 'completed' %}
                                    <span class="badge bg-success">مكتمل</span>
                                    {% elif entry.status == 'in_progress' %}
                                    <span class="badge bg-warning">قيد التنفيذ</span>
                                    {% elif entry.status == 'planned' %}
                                    <span class="badge bg-info">مخطط</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{{ entry.status }}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- قسم تصدير وطباعة التقدمات -->
<div class="row mt-5">
    <div class="col-md-12">
        <div class="card bg-light">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-download me-1"></i>
                    تصدير وطباعة التقدمات
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- تصدير إلى Excel -->
                    <div class="col-md-6">
                        <form method="POST" action="{{ url_for('export_progress_excel') }}" class="d-inline">
                            <div class="mb-3">
                                <label for="export_date_tp" class="form-label">
                                    <i class="fas fa-calendar me-1"></i>
                                    اختر التاريخ للتصدير إلى Excel:
                                </label>
                                <input type="date" class="form-control" id="export_date_tp" name="export_date" required>
                            </div>
                            <button type="submit" class="btn btn-success w-100">
                                <i class="fas fa-file-excel me-1"></i>
                                تصدير إلى Excel
                            </button>
                        </form>
                    </div>

                    <!-- طباعة -->
                    <div class="col-md-6">
                        <form method="POST" action="{{ url_for('print_progress') }}" class="d-inline" target="_blank">
                            <div class="mb-3">
                                <label for="print_date_tp" class="form-label">
                                    <i class="fas fa-calendar me-1"></i>
                                    اختر التاريخ للطباعة:
                                </label>
                                <input type="date" class="form-control" id="print_date_tp" name="print_date" required>
                            </div>
                            <button type="submit" class="btn btn-info w-100">
                                <i class="fas fa-print me-1"></i>
                                طباعة التقدمات
                            </button>
                        </form>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="alert alert-info mb-0">
                            <i class="fas fa-info-circle me-1"></i>
                            <strong>ملاحظة:</strong> سيتم تصدير/طباعة جميع التقدمات المسجلة في التاريخ المحدد فقط.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // تحسين تجربة المستخدم لأزرار التصدير والطباعة في صفحة البرنامج التعليمي
    document.addEventListener('DOMContentLoaded', function () {
        const exportForm = document.querySelector('form[action="{{ url_for("export_progress_excel") }}"]');
        const printForm = document.querySelector('form[action="{{ url_for("print_progress") }}"]');

        if (exportForm) {
            exportForm.addEventListener('submit', function (e) {
                const dateInput = this.querySelector('input[name="export_date"]');
                if (!dateInput.value) {
                    e.preventDefault();
                    alert('يرجى تحديد تاريخ للتصدير');
                    dateInput.focus();
                    return false;
                }

                // إظهار رسالة تحميل
                const submitBtn = this.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> جاري التصدير...';
                submitBtn.disabled = true;

                // إعادة تفعيل الزر بعد 3 ثوان
                setTimeout(function () {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }, 3000);
            });
        }

        if (printForm) {
            printForm.addEventListener('submit', function (e) {
                const dateInput = this.querySelector('input[name="print_date"]');
                if (!dateInput.value) {
                    e.preventDefault();
                    alert('يرجى تحديد تاريخ للطباعة');
                    dateInput.focus();
                    return false;
                }

                // منع الإرسال الافتراضي للتحقق أولاً
                e.preventDefault();
                const selectedDate = dateInput.value;
                const submitBtn = this.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;

                // إظهار رسالة تحقق
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> جاري التحقق...';
                submitBtn.disabled = true;

                // التحقق من وجود تقدمات عبر API
                fetch('/api/check_progress_date', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ date: selectedDate })
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.has_progress) {
                            // يوجد تقدمات، متابعة الطباعة
                            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> جاري التحضير للطباعة...';

                            // إرسال النموذج للطباعة
                            setTimeout(() => {
                                const form = document.createElement('form');
                                form.method = 'POST';
                                form.action = '{{ url_for("print_progress") }}';
                                form.target = '_blank';

                                const dateField = document.createElement('input');
                                dateField.type = 'hidden';
                                dateField.name = 'print_date';
                                dateField.value = selectedDate;
                                form.appendChild(dateField);

                                document.body.appendChild(form);
                                form.submit();
                                document.body.removeChild(form);

                                // إعادة تفعيل الزر
                                submitBtn.innerHTML = originalText;
                                submitBtn.disabled = false;
                            }, 500);
                        } else {
                            // لا توجد تقدمات
                            submitBtn.innerHTML = originalText;
                            submitBtn.disabled = false;

                            alert('لا توجد تقدمات في التاريخ المحدد (' + selectedDate + ').\n\nيرجى إضافة تقدمات جديدة أولاً أو اختيار تاريخ آخر.');
                            dateInput.focus();
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        // في حالة الخطأ، إعادة تفعيل الزر وإظهار رسالة
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                        alert('حدث خطأ في التحقق من التقدمات. يرجى المحاولة مرة أخرى.');
                    });
            });
        }
    });
</script>
{% endblock %}