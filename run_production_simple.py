#!/usr/bin/env python3
"""
سكريپت تشغيل Ta9affi في وضع Production - مبسط ومضمون
"""

import os
import sys
import socket
import subprocess

def find_available_port(start_port=8000):
    """البحث عن منفذ متاح"""
    for port in range(start_port, start_port + 100):
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('localhost', port))
            sock.close()
            if result != 0:  # المنفذ متاح
                return port
        except:
            continue
    return None

def kill_process_on_port(port):
    """إنهاء العملية التي تستخدم المنفذ"""
    try:
        # البحث عن العملية التي تستخدم المنفذ
        result = subprocess.run(['lsof', '-ti', f':{port}'], capture_output=True, text=True)
        if result.stdout.strip():
            pids = result.stdout.strip().split('\n')
            for pid in pids:
                try:
                    subprocess.run(['kill', '-9', pid], check=True)
                    print(f"✅ تم إنهاء العملية {pid} التي تستخدم المنفذ {port}")
                    return True
                except:
                    continue
    except:
        # محاولة باستخدام netstat
        try:
            result = subprocess.run(['netstat', '-tlnp'], capture_output=True, text=True)
            lines = result.stdout.split('\n')
            for line in lines:
                if f':{port}' in line and 'LISTEN' in line:
                    parts = line.split()
                    if len(parts) > 6:
                        pid_program = parts[6]
                        if '/' in pid_program:
                            pid = pid_program.split('/')[0]
                            try:
                                subprocess.run(['kill', '-9', pid], check=True)
                                print(f"✅ تم إنهاء العملية {pid} التي تستخدم المنفذ {port}")
                                return True
                            except:
                                continue
        except:
            pass
    return False

def main():
    """تشغيل التطبيق مع الإعدادات الصحيحة"""

    print("🚀 تشغيل Ta9affi في وضع Production")
    print("="*50)
    
    # فحص المنفذ 8000
    print("🔍 فحص توفر المنفذ 8000...")
    port = 8000

    # فحص إذا كان المنفذ مستخدم
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    sock.settimeout(1)
    port_in_use = sock.connect_ex(('localhost', port)) == 0
    sock.close()

    if port_in_use:
        print(f"⚠️ المنفذ {port} مستخدم من قبل تطبيق آخر")

        # محاولة إنهاء العملية
        print("🔄 محاولة إنهاء العملية التي تستخدم المنفذ...")
        if kill_process_on_port(port):
            print(f"✅ تم تحرير المنفذ {port}")
        else:
            # البحث عن منفذ بديل
            print("🔍 البحث عن منفذ بديل...")
            available_port = find_available_port(8001)
            if available_port:
                port = available_port
                print(f"✅ تم العثور على منفذ متاح: {port}")
            else:
                print("❌ لم يتم العثور على منفذ متاح")
                sys.exit(1)
    else:
        print(f"✅ المنفذ {port} متاح")

    # تعيين متغيرات البيئة بشكل مباشر
    os.environ['PRODUCTION_MODE'] = 'true'
    os.environ['SERVER_IP'] = '*************'
    os.environ['PORT'] = str(port)
    os.environ['SECRET_KEY'] = 'ta9affi-production-secret-key-2024'
    
    # استخدام SQLite كبديل آمن
    if not os.environ.get('DATABASE_URL'):
        os.environ['DATABASE_URL'] = 'sqlite:///ta9affi_production.db'
    
    print("✅ تم تعيين متغيرات البيئة:")
    print(f"   🏭 وضع Production: {os.environ['PRODUCTION_MODE']}")
    print(f"   🌐 عنوان الخادم: {os.environ['SERVER_IP']}")
    print(f"   🔌 المنفذ: {os.environ['PORT']}")
    print(f"   🗄️ قاعدة البيانات: {os.environ['DATABASE_URL']}")
    
    print("\n🔗 روابط الوصول:")
    print(f"   📱 التطبيق: http://*************:{port}")
    print(f"   🔐 تسجيل الدخول: http://*************:{port}/login")
    print(f"   📝 التسجيل: http://*************:{port}/register")
    print(f"   ❤️ فحص الصحة: http://*************:{port}/health")
    
    print("\n" + "="*50)
    print("🚀 بدء تشغيل التطبيق...")
    print("⏹️ اضغط Ctrl+C لإيقاف التطبيق")
    print("="*50)
    
    try:
        # استيراد وتشغيل التطبيق
        from app import app
        
        # تشغيل التطبيق مع الإعدادات المحددة
        app.run(
            host='0.0.0.0',        # الاستماع على جميع العناوين
            port=port,             # المنفذ المحدد
            debug=False,           # تعطيل debug في Production
            threaded=True,         # دعم multiple threads
            use_reloader=False     # تعطيل auto-reload
        )
        
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف التطبيق بواسطة المستخدم")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل التطبيق: {str(e)}")
        print("\n💡 نصائح لحل المشكلة:")
        print("1. تأكد من تثبيت المتطلبات: pip install -r requirements.txt")
        print("2. تأكد من صلاحيات الملفات")
        print("3. تأكد من فتح المنفذ 8000 في Firewall")
        print("4. جرب تشغيل: python diagnose_server.py")
        sys.exit(1)

if __name__ == '__main__':
    main()
