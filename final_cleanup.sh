#!/bin/bash
# تنظيف نهائي وشامل للمشروع - إزالة جميع الملفات غير الضرورية للإنتاج

set -e

# ألوان للرسائل
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}$1${NC}"
}

# إنشاء نسخة احتياطية نهائية
create_final_backup() {
    log_header "💾 إنشاء نسخة احتياطية نهائية"
    
    local backup_dir="final_backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    # نسخ كامل للمشروع قبل التنظيف النهائي
    cp -r . "$backup_dir/" 2>/dev/null || true
    tar -czf "${backup_dir}.tar.gz" "$backup_dir"
    rm -rf "$backup_dir"
    
    log_success "تم إنشاء نسخة احتياطية نهائية: ${backup_dir}.tar.gz"
}

# قائمة الملفات المطلوبة للإنتاج فقط
get_production_files() {
    cat << 'EOF'
# ملفات Python الأساسية
app_postgresql.py
models_optimized.py
redis_manager.py
cache_manager.py
database_optimizer.py
monitoring_system.py
alerting_system.py
security_manager.py
auth_security.py
file_security.py
backup_manager.py
frontend_optimizer.py

# ملفات الإعداد والنشر
requirements.txt
docker-compose.production.yml
Dockerfile.production
gunicorn_config.py
deploy.sh
.env.production.example

# ملفات التوثيق الأساسية
README.md
LICENSE
CHANGELOG.md

# مجلدات ضرورية
static/
templates/
nginx/
monitoring/
database/
EOF
}

# حذف جميع الملفات غير الضرورية
remove_unnecessary_files() {
    log_header "🧹 حذف الملفات غير الضرورية للإنتاج"
    
    # ملفات التطوير والاختبار
    log_info "حذف ملفات التطوير والاختبار..."
    rm -f run_local.sh
    rm -f docker-compose.local.yml
    rm -f Dockerfile.local
    rm -f .env.local
    rm -f README_LOCAL.md
    rm -f cleanup_for_production.sh
    rm -f prepare_for_production.sh
    rm -f production_quality_check.py
    rm -f final_cleanup.sh
    rm -f PRODUCTION_READY.md
    rm -f load_testing.py
    rm -f database_performance_test.py
    rm -f run_performance_tests.sh
    rm -f backup_script.sh
    rm -f backup_crontab.txt
    rm -f migrate_to_postgresql.py
    rm -f monitoring_dashboard.py
    
    # ملفات IDE والمحررات
    log_info "حذف ملفات IDE والمحررات..."
    rm -rf .vscode/
    rm -rf .idea/
    rm -f *.sublime-project
    rm -f *.sublime-workspace
    rm -f .*.swp
    rm -f .*.swo
    rm -f *~
    rm -f \#*\#
    rm -f .\#*
    
    # ملفات Python المؤقتة
    log_info "حذف ملفات Python المؤقتة..."
    find . -type f -name "*.pyc" -delete 2>/dev/null || true
    find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
    find . -type f -name "*.pyo" -delete 2>/dev/null || true
    find . -type f -name "*.pyd" -delete 2>/dev/null || true
    rm -rf build/
    rm -rf dist/
    rm -rf *.egg-info/
    
    # ملفات الاختبار
    log_info "حذف ملفات الاختبار..."
    rm -rf tests/
    rm -rf test_*.py
    rm -rf *_test.py
    rm -rf .pytest_cache/
    rm -f pytest.ini
    rm -f conftest.py
    rm -rf htmlcov/
    rm -f .coverage
    rm -f coverage.xml
    rm -f .tox/
    
    # ملفات النظام
    log_info "حذف ملفات النظام..."
    find . -name ".DS_Store" -delete 2>/dev/null || true
    find . -name "._*" -delete 2>/dev/null || true
    find . -name "Thumbs.db" -delete 2>/dev/null || true
    find . -name "Desktop.ini" -delete 2>/dev/null || true
    find . -name "*~" -delete 2>/dev/null || true
    
    # ملفات Git غير الضرورية
    log_info "تنظيف ملفات Git..."
    rm -rf .git/hooks/pre-commit.sample
    rm -rf .git/hooks/pre-push.sample
    rm -rf .git/hooks/pre-receive.sample
    
    # ملفات السجلات والبيانات المؤقتة
    log_info "حذف ملفات السجلات والبيانات المؤقتة..."
    rm -rf logs/*.log 2>/dev/null || true
    rm -f *.log
    rm -f *.db
    rm -f *.sqlite
    rm -f *.sqlite3
    rm -rf cache/
    rm -rf tmp/
    rm -rf temp/
    rm -rf sessions/
    rm -rf flask_session/
    
    # ملفات النسخ الاحتياطية القديمة
    log_info "حذف النسخ الاحتياطية القديمة..."
    rm -rf backup_*/
    rm -f backup_*.tar.gz
    rm -f *_backup_*.sql
    rm -f *_backup_*.tar.gz
    
    # ملفات التقارير والتحليل
    log_info "حذف ملفات التقارير..."
    rm -f production_quality_report.json
    rm -f preparation_report.md
    rm -f optimization_report.json
    rm -f load_test_*.json
    rm -f load_test_*.md
    rm -f load_test_*.csv
    rm -f db_performance_*.json
    rm -f db_performance_*.md
    
    # ملفات التحضير
    log_info "حذف ملفات التحضير..."
    rm -f .production_ready
    rm -f version.json
    rm -f DEPLOYMENT.md
    
    # مجلدات غير ضرورية
    log_info "حذف مجلدات غير ضرورية..."
    rm -rf performance_test_results/
    rm -rf node_modules/ 2>/dev/null || true
    rm -rf .npm/ 2>/dev/null || true
    
    log_success "تم حذف جميع الملفات غير الضرورية"
}

# تنظيف مجلد static
clean_static_folder() {
    log_header "📁 تنظيف مجلد static"
    
    if [ -d "static" ]; then
        # حذف ملفات التطوير
        rm -f static/js/lazy-loading.js 2>/dev/null || true
        rm -f static/js/performance.js 2>/dev/null || true
        rm -f static/css/performance.css 2>/dev/null || true
        rm -f static/sw.js 2>/dev/null || true
        rm -f static/manifest.json 2>/dev/null || true
        
        # حذف ملفات gzip المؤقتة
        find static -name "*.gz" -delete 2>/dev/null || true
        
        # الاحتفاظ فقط بالملفات الأساسية
        log_success "تم تنظيف مجلد static"
    fi
}

# تنظيف مجلد templates
clean_templates_folder() {
    log_header "📄 تنظيف مجلد templates"
    
    if [ -d "templates" ]; then
        # حذف القوالب المحسنة للتطوير
        rm -f templates/base_optimized.html 2>/dev/null || true
        
        log_success "تم تنظيف مجلد templates"
    fi
}

# تنظيف مجلد nginx
clean_nginx_folder() {
    log_header "🌐 تنظيف مجلد nginx"
    
    if [ -d "nginx" ]; then
        # حذف إعدادات التطوير المحلي
        rm -f nginx/nginx.local.conf 2>/dev/null || true
        
        log_success "تم تنظيف مجلد nginx"
    fi
}

# تنظيف مجلد monitoring
clean_monitoring_folder() {
    log_header "📊 تنظيف مجلد monitoring"
    
    if [ -d "monitoring" ]; then
        # حذف إعدادات التطوير المحلي
        rm -f monitoring/prometheus.local.yml 2>/dev/null || true
        
        log_success "تم تنظيف مجلد monitoring"
    fi
}

# إنشاء .gitignore نهائي للإنتاج
create_final_gitignore() {
    log_header "📝 إنشاء .gitignore نهائي للإنتاج"
    
    cat > .gitignore << 'EOF'
# ملفات البيئة والإعدادات الحساسة
.env
.env.production
.env.local
config.local.py

# ملفات Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# ملفات السجلات
logs/
*.log

# ملفات قاعدة البيانات المحلية
*.db
*.sqlite
*.sqlite3

# ملفات التحميل والتخزين المؤقت
uploads/
cache/
tmp/
temp/
sessions/

# ملفات النسخ الاحتياطي
backups/
backup_*/

# ملفات النظام
.DS_Store
Thumbs.db
Desktop.ini

# ملفات الأمان
*.pem
*.key
*.crt
*.p12
*.pfx
EOF

    log_success "تم إنشاء .gitignore نهائي للإنتاج"
}

# إنشاء requirements.txt نهائي ومبسط
create_final_requirements() {
    log_header "📦 إنشاء requirements.txt نهائي"
    
    cat > requirements.txt << 'EOF'
# متطلبات الإنتاج الأساسية - Ta9affi

# Flask Framework
Flask==2.3.3
Flask-SQLAlchemy==3.0.5
Flask-Login==0.6.3
Flask-WTF==1.1.1
Flask-Mail==0.9.1
Flask-Migrate==4.0.5
Flask-Limiter==3.5.0
Flask-Talisman==1.1.0

# قاعدة البيانات
psycopg2-binary==2.9.7
SQLAlchemy==2.0.21

# Redis والتخزين المؤقت
redis==5.0.0
Flask-Session==0.5.0

# خادم التطبيق
gunicorn==21.2.0
gevent==23.7.0

# الأمان والتشفير
cryptography==41.0.4
bcrypt==4.0.1

# معالجة الملفات
Pillow==10.0.0
python-magic==0.4.27

# المراقبة
prometheus-client==0.17.1
psutil==5.9.5

# المهام الخلفية
celery==5.3.1

# أدوات مساعدة
python-dotenv==1.0.0
click==8.1.7
Werkzeug==2.3.7
Jinja2==3.1.2
WTForms==3.0.1
requests==2.31.0
EOF

    log_success "تم إنشاء requirements.txt نهائي"
}

# عرض الملفات المتبقية
show_remaining_files() {
    log_header "📋 الملفات المتبقية للإنتاج"
    
    echo "الملفات Python:"
    ls -la *.py 2>/dev/null || echo "  لا توجد ملفات Python"
    
    echo ""
    echo "ملفات الإعداد:"
    ls -la *.txt *.yml *.sh *.md *.example 2>/dev/null || echo "  لا توجد ملفات إعداد"
    
    echo ""
    echo "المجلدات:"
    ls -la | grep "^d" | awk '{print "  " $9}' | grep -v "^\.$\|^\.\.$"
    
    echo ""
    echo "إجمالي الملفات: $(find . -type f | wc -l)"
    echo "إجمالي المجلدات: $(find . -type d | wc -l)"
    
    local total_size=$(du -sh . | cut -f1)
    echo "الحجم الإجمالي: $total_size"
}

# إنشاء حزمة الإنتاج النهائية
create_final_package() {
    log_header "📦 إنشاء حزمة الإنتاج النهائية"
    
    local package_name="ta9affi-production-final-$(date +%Y%m%d_%H%M%S)"
    
    # إنشاء أرشيف مضغوط للمشروع النظيف
    tar --exclude='.git' --exclude='final_backup_*' --exclude='*.tar.gz' \
        -czf "${package_name}.tar.gz" .
    
    local package_size=$(du -h "${package_name}.tar.gz" | cut -f1)
    local file_count=$(tar -tzf "${package_name}.tar.gz" | wc -l)
    
    log_success "تم إنشاء حزمة الإنتاج النهائية:"
    echo "  📦 الاسم: ${package_name}.tar.gz"
    echo "  📏 الحجم: $package_size"
    echo "  📄 عدد الملفات: $file_count"
}

# عرض ملخص التنظيف النهائي
show_final_summary() {
    log_header "📊 ملخص التنظيف النهائي"
    
    echo "=============================================="
    echo "🎉 تم تنظيف Ta9affi نهائياً للإنتاج!"
    echo "=============================================="
    echo ""
    echo "✅ العمليات المكتملة:"
    echo "   - حذف جميع ملفات التطوير والاختبار"
    echo "   - حذف ملفات IDE والمحررات"
    echo "   - حذف ملفات النظام المؤقتة"
    echo "   - تنظيف مجلدات static و templates"
    echo "   - إنشاء .gitignore نهائي"
    echo "   - إنشاء requirements.txt مبسط"
    echo "   - إنشاء حزمة الإنتاج النهائية"
    echo ""
    echo "🚀 المشروع الآن يحتوي فقط على:"
    echo "   - الملفات الأساسية للتطبيق"
    echo "   - ملفات الإعداد والنشر"
    echo "   - المجلدات الضرورية فقط"
    echo ""
    echo "📦 جاهز للرفع والنشر في الإنتاج!"
    echo "=============================================="
}

# الدالة الرئيسية
main() {
    log_header "🧹 التنظيف النهائي والشامل لـ Ta9affi"
    echo "=============================================="
    echo "⚠️  هذا سيحذف جميع الملفات غير الضرورية للإنتاج!"
    echo "سيتم الاحتفاظ فقط بالملفات الأساسية للتشغيل."
    echo "=============================================="
    echo ""
    
    # تأكيد من المستخدم
    read -p "هل تريد المتابعة؟ (y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "تم إلغاء التنظيف"
        exit 0
    fi
    
    # تنفيذ التنظيف النهائي
    create_final_backup
    remove_unnecessary_files
    clean_static_folder
    clean_templates_folder
    clean_nginx_folder
    clean_monitoring_folder
    create_final_gitignore
    create_final_requirements
    
    echo ""
    show_remaining_files
    echo ""
    
    create_final_package
    show_final_summary
    
    log_success "🎉 تم التنظيف النهائي بنجاح!"
}

# تشغيل السكريبت
main "$@"
