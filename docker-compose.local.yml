# Docker Compose للتشغيل المحلي - Ta9affi
version: '3.8'

services:
  # تطبيق Ta9affi الرئيسي
  ta9affi-app:
    build:
      context: .
      dockerfile: Dockerfile.local
    container_name: ta9affi-app-local
    restart: unless-stopped
    environment:
      - FLASK_ENV=development
      - DATABASE_URL=**************************************************************/ta9affi_local
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=local-development-secret-key
    volumes:
      - .:/app
      - ./uploads:/app/uploads
      - ./logs:/app/logs
      - ./backups:/app/backups
    ports:
      - "5000:5000"
    depends_on:
      - postgres
      - redis
    networks:
      - ta9affi-local-network
    command: python app_postgresql.py

  # قاعدة البيانات PostgreSQL
  postgres:
    image: postgres:15-alpine
    container_name: ta9affi-postgres-local
    restart: unless-stopped
    environment:
      - POSTGRES_DB=ta9affi_local
      - POSTGRES_USER=ta9affi_user
      - POSTGRES_PASSWORD=ta9affi_local_password
    volumes:
      - postgres_local_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d:ro
    ports:
      - "5433:5432"
    networks:
      - ta9affi-local-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ta9affi_user -d ta9affi_local"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis للتخزين المؤقت والجلسات
  redis:
    image: redis:7-alpine
    container_name: ta9affi-redis-local
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 128mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_local_data:/data
    ports:
      - "6380:6379"
    networks:
      - ta9affi-local-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # خادم Nginx (اختياري للتطوير)
  nginx:
    image: nginx:alpine
    container_name: ta9affi-nginx-local
    restart: unless-stopped
    ports:
      - "8080:80"
    volumes:
      - ./nginx/nginx.local.conf:/etc/nginx/nginx.conf:ro
      - ./static:/var/www/static:ro
      - ./uploads:/var/www/uploads:ro
    depends_on:
      - ta9affi-app
    networks:
      - ta9affi-local-network
    profiles:
      - nginx

  # مراقبة النظام مع Prometheus (اختياري)
  prometheus:
    image: prom/prometheus:latest
    container_name: ta9affi-prometheus-local
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.local.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_local_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--storage.tsdb.retention.time=24h'
      - '--web.enable-lifecycle'
    networks:
      - ta9affi-local-network
    profiles:
      - monitoring

  # Grafana للوحات المراقبة (اختياري)
  grafana:
    image: grafana/grafana:latest
    container_name: ta9affi-grafana-local
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_local_data:/var/lib/grafana
    depends_on:
      - prometheus
    networks:
      - ta9affi-local-network
    profiles:
      - monitoring

  # MailHog لاختبار البريد الإلكتروني
  mailhog:
    image: mailhog/mailhog:latest
    container_name: ta9affi-mailhog-local
    restart: unless-stopped
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    networks:
      - ta9affi-local-network
    profiles:
      - mail

  # pgAdmin لإدارة قاعدة البيانات (اختياري)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: ta9affi-pgadmin-local
    restart: unless-stopped
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin123
    ports:
      - "5050:80"
    volumes:
      - pgadmin_local_data:/var/lib/pgadmin
    depends_on:
      - postgres
    networks:
      - ta9affi-local-network
    profiles:
      - admin

  # Redis Commander لإدارة Redis (اختياري)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: ta9affi-redis-commander-local
    restart: unless-stopped
    environment:
      - REDIS_HOSTS=local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis
    networks:
      - ta9affi-local-network
    profiles:
      - admin

# الشبكات
networks:
  ta9affi-local-network:
    driver: bridge

# الأحجام المستمرة
volumes:
  postgres_local_data:
    driver: local
  redis_local_data:
    driver: local
  prometheus_local_data:
    driver: local
  grafana_local_data:
    driver: local
  pgadmin_local_data:
    driver: local
