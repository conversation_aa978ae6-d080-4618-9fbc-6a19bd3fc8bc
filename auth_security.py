#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام حماية المصادقة المتقدم
يوفر حماية شاملة لعمليات تسجيل الدخول والمصادقة
"""

import secrets
import time
import hashlib
import logging
from datetime import datetime, timedelta
from functools import wraps
from flask import request, session, current_app, abort, jsonify
from flask_login import current_user
from redis_manager import redis_manager
import re

class AuthSecurityManager:
    """مدير أمان المصادقة"""
    
    def __init__(self, app=None):
        self.app = app
        self.max_login_attempts = 5
        self.lockout_duration = 1800  # 30 دقيقة
        self.password_min_length = 8
        self.session_timeout = 3600  # ساعة واحدة
        
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """تهيئة مدير أمان المصادقة"""
        self.app = app
        
        # تحميل الإعدادات من التطبيق
        self.max_login_attempts = app.config.get('MAX_LOGIN_ATTEMPTS', 5)
        self.lockout_duration = app.config.get('LOCKOUT_DURATION', 1800)
        self.password_min_length = app.config.get('PASSWORD_MIN_LENGTH', 8)
        self.session_timeout = app.config.get('SESSION_TIMEOUT', 3600)
        
        logging.info("✅ تم تهيئة مدير أمان المصادقة")
    
    def validate_password_strength(self, password):
        """التحقق من قوة كلمة المرور"""
        errors = []
        
        # الطول الأدنى
        if len(password) < self.password_min_length:
            errors.append(f"كلمة المرور يجب أن تكون {self.password_min_length} أحرف على الأقل")
        
        # وجود أحرف كبيرة
        if not re.search(r'[A-Z]', password):
            errors.append("كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل")
        
        # وجود أحرف صغيرة
        if not re.search(r'[a-z]', password):
            errors.append("كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل")
        
        # وجود أرقام
        if not re.search(r'\d', password):
            errors.append("كلمة المرور يجب أن تحتوي على رقم واحد على الأقل")
        
        # وجود رموز خاصة
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            errors.append("كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل")
        
        # كلمات مرور شائعة
        common_passwords = [
            'password', '123456', '123456789', 'qwerty', 'abc123',
            'password123', 'admin', 'letmein', 'welcome', 'monkey'
        ]
        
        if password.lower() in common_passwords:
            errors.append("كلمة المرور شائعة جداً، يرجى اختيار كلمة مرور أقوى")
        
        return errors
    
    def check_login_attempts(self, identifier):
        """فحص عدد محاولات تسجيل الدخول"""
        if not redis_manager.is_available():
            return True  # السماح إذا لم يكن Redis متاحاً
        
        try:
            key = f"login_attempts:{identifier}"
            attempts_data = redis_manager.cache_get(key)
            
            if not attempts_data:
                return True
            
            attempts = attempts_data.get('count', 0)
            last_attempt = attempts_data.get('last_attempt', 0)
            
            # التحقق من انتهاء فترة الحظر
            if time.time() - last_attempt > self.lockout_duration:
                redis_manager.cache_delete(key)
                return True
            
            return attempts < self.max_login_attempts
            
        except Exception as e:
            logging.error(f"Error checking login attempts: {str(e)}")
            return True
    
    def record_failed_login(self, identifier, ip_address=None):
        """تسجيل محاولة تسجيل دخول فاشلة"""
        if not redis_manager.is_available():
            return
        
        try:
            key = f"login_attempts:{identifier}"
            current_time = time.time()
            
            # الحصول على البيانات الحالية
            attempts_data = redis_manager.cache_get(key) or {'count': 0, 'last_attempt': 0}
            
            # زيادة العدد
            attempts_data['count'] += 1
            attempts_data['last_attempt'] = current_time
            attempts_data['ip_address'] = ip_address or request.remote_addr
            
            # حفظ البيانات
            redis_manager.cache_set(key, attempts_data, self.lockout_duration)
            
            # تسجيل الحدث
            logging.getLogger('security').warning(
                f"FAILED_LOGIN - {identifier} - Attempt {attempts_data['count']} - IP: {ip_address}"
            )
            
            # حظر IP إذا تجاوز الحد
            if attempts_data['count'] >= self.max_login_attempts:
                self.block_ip_temporarily(ip_address or request.remote_addr)
            
        except Exception as e:
            logging.error(f"Error recording failed login: {str(e)}")
    
    def record_successful_login(self, identifier):
        """تسجيل تسجيل دخول ناجح"""
        if not redis_manager.is_available():
            return
        
        try:
            # مسح محاولات الفشل
            key = f"login_attempts:{identifier}"
            redis_manager.cache_delete(key)
            
            # تسجيل النجاح
            logging.getLogger('security').info(
                f"SUCCESSFUL_LOGIN - {identifier} - IP: {request.remote_addr}"
            )
            
        except Exception as e:
            logging.error(f"Error recording successful login: {str(e)}")
    
    def block_ip_temporarily(self, ip_address, duration=None):
        """حظر IP مؤقتاً"""
        if not redis_manager.is_available():
            return
        
        duration = duration or self.lockout_duration
        
        try:
            key = f"blocked_ip:{ip_address}"
            block_data = {
                'blocked_at': time.time(),
                'reason': 'Too many failed login attempts',
                'duration': duration
            }
            
            redis_manager.cache_set(key, block_data, duration)
            
            logging.getLogger('security').warning(
                f"IP_BLOCKED_TEMPORARILY - {ip_address} - Duration: {duration}s"
            )
            
        except Exception as e:
            logging.error(f"Error blocking IP: {str(e)}")
    
    def is_ip_blocked(self, ip_address):
        """فحص ما إذا كان IP محظور"""
        if not redis_manager.is_available():
            return False
        
        try:
            key = f"blocked_ip:{ip_address}"
            block_data = redis_manager.cache_get(key)
            return block_data is not None
            
        except Exception as e:
            logging.error(f"Error checking IP block: {str(e)}")
            return False
    
    def generate_secure_token(self, length=32):
        """إنشاء رمز آمن"""
        return secrets.token_urlsafe(length)
    
    def create_password_reset_token(self, user_id):
        """إنشاء رمز إعادة تعيين كلمة المرور"""
        token = self.generate_secure_token()
        
        if redis_manager.is_available():
            try:
                key = f"password_reset:{token}"
                data = {
                    'user_id': user_id,
                    'created_at': time.time(),
                    'expires_at': time.time() + 3600  # ساعة واحدة
                }
                
                redis_manager.cache_set(key, data, 3600)
                return token
                
            except Exception as e:
                logging.error(f"Error creating password reset token: {str(e)}")
        
        return None
    
    def validate_password_reset_token(self, token):
        """التحقق من رمز إعادة تعيين كلمة المرور"""
        if not redis_manager.is_available():
            return None
        
        try:
            key = f"password_reset:{token}"
            data = redis_manager.cache_get(key)
            
            if not data:
                return None
            
            # التحقق من انتهاء الصلاحية
            if time.time() > data.get('expires_at', 0):
                redis_manager.cache_delete(key)
                return None
            
            return data.get('user_id')
            
        except Exception as e:
            logging.error(f"Error validating password reset token: {str(e)}")
            return None
    
    def invalidate_password_reset_token(self, token):
        """إلغاء رمز إعادة تعيين كلمة المرور"""
        if redis_manager.is_available():
            try:
                key = f"password_reset:{token}"
                redis_manager.cache_delete(key)
            except Exception as e:
                logging.error(f"Error invalidating password reset token: {str(e)}")
    
    def create_email_verification_token(self, user_id, email):
        """إنشاء رمز تأكيد البريد الإلكتروني"""
        token = self.generate_secure_token()
        
        if redis_manager.is_available():
            try:
                key = f"email_verification:{token}"
                data = {
                    'user_id': user_id,
                    'email': email,
                    'created_at': time.time(),
                    'expires_at': time.time() + 86400  # 24 ساعة
                }
                
                redis_manager.cache_set(key, data, 86400)
                return token
                
            except Exception as e:
                logging.error(f"Error creating email verification token: {str(e)}")
        
        return None
    
    def validate_email_verification_token(self, token):
        """التحقق من رمز تأكيد البريد الإلكتروني"""
        if not redis_manager.is_available():
            return None
        
        try:
            key = f"email_verification:{token}"
            data = redis_manager.cache_get(key)
            
            if not data:
                return None
            
            # التحقق من انتهاء الصلاحية
            if time.time() > data.get('expires_at', 0):
                redis_manager.cache_delete(key)
                return None
            
            return data
            
        except Exception as e:
            logging.error(f"Error validating email verification token: {str(e)}")
            return None
    
    def check_session_security(self):
        """فحص أمان الجلسة"""
        if not current_user.is_authenticated:
            return True
        
        # فحص انتهاء صلاحية الجلسة
        last_activity = session.get('last_activity', 0)
        if time.time() - last_activity > self.session_timeout:
            session.clear()
            return False
        
        # تحديث آخر نشاط
        session['last_activity'] = time.time()
        
        # فحص تغيير IP (اختياري)
        session_ip = session.get('ip_address')
        current_ip = request.remote_addr
        
        if session_ip and session_ip != current_ip:
            logging.getLogger('security').warning(
                f"SESSION_IP_CHANGE - User: {current_user.id} - Old: {session_ip} - New: {current_ip}"
            )
            # يمكن إضافة منطق إضافي هنا
        
        return True
    
    def get_login_statistics(self, hours=24):
        """الحصول على إحصائيات تسجيل الدخول"""
        if not redis_manager.is_available():
            return {}
        
        try:
            # البحث عن محاولات تسجيل الدخول
            pattern = "login_attempts:*"
            keys = redis_manager.redis_client.keys(pattern)
            
            stats = {
                'total_failed_attempts': 0,
                'blocked_accounts': 0,
                'blocked_ips': 0,
                'top_failed_accounts': {},
                'top_attacking_ips': {}
            }
            
            for key in keys:
                try:
                    data = redis_manager.cache_get(key.decode() if isinstance(key, bytes) else key)
                    if data:
                        stats['total_failed_attempts'] += data.get('count', 0)
                        
                        if data.get('count', 0) >= self.max_login_attempts:
                            stats['blocked_accounts'] += 1
                        
                        # استخراج المعرف من المفتاح
                        identifier = key.split(':')[-1] if isinstance(key, str) else key.decode().split(':')[-1]
                        stats['top_failed_accounts'][identifier] = data.get('count', 0)
                        
                        ip = data.get('ip_address')
                        if ip:
                            stats['top_attacking_ips'][ip] = stats['top_attacking_ips'].get(ip, 0) + data.get('count', 0)
                except:
                    continue
            
            # عدد IPs المحظورة
            blocked_ip_pattern = "blocked_ip:*"
            blocked_keys = redis_manager.redis_client.keys(blocked_ip_pattern)
            stats['blocked_ips'] = len(blocked_keys)
            
            return stats
            
        except Exception as e:
            logging.error(f"Error getting login statistics: {str(e)}")
            return {}

# إنشاء مثيل عام لمدير أمان المصادقة
auth_security = AuthSecurityManager()

# ديكوريتر للحماية من محاولات تسجيل الدخول المتكررة
def login_rate_limit(identifier_func=None):
    """ديكوريتر للحد من محاولات تسجيل الدخول"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # تحديد المعرف
            if identifier_func:
                identifier = identifier_func()
            else:
                identifier = request.form.get('username') or request.json.get('username') or request.remote_addr
            
            # فحص المحاولات
            if not auth_security.check_login_attempts(identifier):
                return jsonify({
                    'error': 'تم تجاوز الحد الأقصى لمحاولات تسجيل الدخول. يرجى المحاولة لاحقاً.'
                }), 429
            
            # فحص حظر IP
            if auth_security.is_ip_blocked(request.remote_addr):
                return jsonify({
                    'error': 'تم حظر عنوان IP هذا مؤقتاً.'
                }), 403
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

# ديكوريتر للتحقق من أمان الجلسة
def session_security_check(f):
    """ديكوريتر للتحقق من أمان الجلسة"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not auth_security.check_session_security():
            return jsonify({'error': 'انتهت صلاحية الجلسة'}), 401
        return f(*args, **kwargs)
    return decorated_function
