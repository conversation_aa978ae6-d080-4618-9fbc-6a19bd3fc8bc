{% extends 'base.html' %}

{% block content %}
<div class="row">
    <div class="col-md-12 mb-4">
        <h2>تعديل التقدم</h2>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <h5 class="mb-0">
                    <i class="fas fa-edit me-1"></i> تعديل حالة التقدم
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('edit_progress', entry_id=entry.id) }}">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="level" class="form-label">المستوى التعليمي</label>
                            <select class="form-select" id="level" name="level_id">
                                <option value="">اختر المستوى</option>
                                {% for level in levels %}
                                <option value="{{ level.id }}" {% if entry.level and entry.level.id==level.id
                                    %}selected{% endif %}>{{ level.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="subject" class="form-label">المادة</label>
                            <select class="form-select" id="subject" name="subject_id">
                                <option value="">اختر المادة</option>
                                {% for subject in subjects %}
                                <option value="{{ subject.id }}" {% if entry.subject and entry.subject.id==subject.id
                                    %}selected{% endif %}>{{ subject.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="domain" class="form-label">الميدان/النشاط</label>
                            <select class="form-select" id="domain" name="domain_id">
                                <option value="">اختر الميدان/النشاط</option>
                                {% for domain in domains %}
                                <option value="{{ domain.id }}" {% if entry.domain and entry.domain.id==domain.id
                                    %}selected{% endif %}>{{ domain.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="material" class="form-label">الموارد المعرفية</label>
                            <select class="form-select" id="material" name="material_id">
                                <option value="">اختر الموارد المعرفية</option>
                                {% for material in materials %}
                                <option value="{{ material.id }}" {% if entry.material and
                                    entry.material.id==material.id %}selected{% endif %}>{{ material.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="competency" class="form-label">الكفاءة المستهدفة</label>
                        <select class="form-select" id="competency" name="competency_id">
                            <option value="">اختر الكفاءة</option>
                            {% for competency in competencies %}
                            <option value="{{ competency.id }}" {% if entry.competency and
                                entry.competency.id==competency.id %}selected{% endif %}>{{ competency.description or
                                competency.name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="date" class="form-label">التاريخ</label>
                            <input type="date" class="form-control" id="date" name="date"
                                value="{{ entry.date.strftime('%Y-%m-%d') }}" required>
                        </div>
                        <div class="col-md-6">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="completed" {% if entry.status=='completed' %}selected{% endif %}>مكتمل
                                </option>
                                <option value="in_progress" {% if entry.status=='in_progress' %}selected{% endif %}>قيد
                                    التنفيذ</option>
                                <option value="planned" {% if entry.status=='planned' %}selected{% endif %}>مخطط
                                </option>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes"
                            rows="3">{{ entry.notes or '' }}</textarea>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('teacher_dashboard') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i> العودة
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // الحصول على العناصر
        const levelSelect = document.getElementById('level');
        const subjectSelect = document.getElementById('subject');
        const domainSelect = document.getElementById('domain');
        const materialSelect = document.getElementById('material');
        const competencySelect = document.getElementById('competency');

        // تحديث المواد بناءً على المستوى
        levelSelect.addEventListener('change', function () {
            const levelId = this.value;

            // إعادة تعيين القوائم المنسدلة التابعة
            subjectSelect.innerHTML = '<option value="">اختر المادة</option>';
            domainSelect.innerHTML = '<option value="">اختر الميدان/النشاط</option>';
            materialSelect.innerHTML = '<option value="">اختر الموارد المعرفية</option>';
            competencySelect.innerHTML = '<option value="">اختر الكفاءة</option>';

            // إذا تم اختيار مستوى، قم بتحميل المواد المناسبة
            if (levelId) {
                // يمكن إضافة طلب AJAX للحصول على المواد المرتبطة بالمستوى
                // للتبسيط، سنعرض جميع المواد المتاحة
            }
        });

        // تحديث الميادين/الأنشطة بناءً على المادة
        subjectSelect.addEventListener('change', function () {
            const subjectId = this.value;

            // إعادة تعيين القوائم المنسدلة التابعة
            domainSelect.innerHTML = '<option value="">اختر الميدان/النشاط</option>';
            materialSelect.innerHTML = '<option value="">اختر الموارد المعرفية</option>';
            competencySelect.innerHTML = '<option value="">اختر الكفاءة</option>';

            // إذا تم اختيار مادة، قم بتحميل الميادين/الأنشطة المناسبة
            if (subjectId) {
                // يمكن إضافة طلب AJAX للحصول على الميادين/الأنشطة المرتبطة بالمادة
                // للتبسيط، سنعرض جميع الميادين/الأنشطة المتاحة
            }
        });

        // تحديث المواد المعرفية بناءً على الميدان
        domainSelect.addEventListener('change', function () {
            const domainId = this.value;

            // إعادة تعيين القوائم المنسدلة التابعة
            materialSelect.innerHTML = '<option value="">اختر الموارد المعرفية</option>';
            competencySelect.innerHTML = '<option value="">اختر الكفاءة</option>';

            // إذا تم اختيار ميدان، قم بتحميل المواد المعرفية المناسبة
            if (domainId) {
                // يمكن إضافة طلب AJAX للحصول على المواد المعرفية المرتبطة بالميدان
                // للتبسيط، سنعرض جميع المواد المعرفية المتاحة
            }
        });

        // تحديث الكفاءات بناءً على الموارد المعرفية
        materialSelect.addEventListener('change', function () {
            const materialId = this.value;

            // إعادة تعيين قائمة الكفاءات
            competencySelect.innerHTML = '<option value="">اختر الكفاءة</option>';

            // إذا تم اختيار موارد معرفية، قم بتحميل الكفاءات المناسبة
            if (materialId) {
                // يمكن إضافة طلب AJAX للحصول على الكفاءات المرتبطة بالموارد المعرفية
                // للتبسيط، سنعرض جميع الكفاءات المتاحة
            }
        });
    });
</script>
{% endblock %}