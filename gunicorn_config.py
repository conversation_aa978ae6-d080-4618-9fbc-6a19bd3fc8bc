#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعدادات Gunicorn لتطبيق Ta9affi
محسن للأداء العالي مع آلاف المستخدمين المتزامنين
"""

import os
import multiprocessing

# ===== إعدادات الخادم الأساسية =====

# عنوان الربط
bind = "0.0.0.0:8000"  # تغيير من 127.0.0.1:8000 إلى 0.0.0.0:8000

# عدد العمليات (workers)
# القاعدة: (2 x CPU cores) + 1
workers = multiprocessing.cpu_count() * 2 + 1

# نوع العامل - gevent للأداء العالي مع الاتصالات المتزامنة
worker_class = "gevent"

# عدد الاتصالات المتزامنة لكل عامل
worker_connections = 1000

# الحد الأقصى للطلبات لكل عامل قبل إعادة التشغيل
max_requests = 1000
max_requests_jitter = 50

# مهلة العامل (ثانية)
timeout = 30
keepalive = 2

# ===== إعدادات الذاكرة والموارد =====

# الحد الأقصى لحجم الطلب (16MB)
limit_request_line = 4094
limit_request_fields = 100
limit_request_field_size = 8190

# إعادة تشغيل العمال عند استهلاك ذاكرة عالي
max_worker_memory = 200  # MB

# ===== إعدادات الأمان =====

# تشغيل كمستخدم محدد (في الإنتاج)
# user = "ta9affi"
# group = "ta9affi"

# إخفاء معلومات الخادم
proc_name = "ta9affi"

# ===== إعدادات السجلات =====

# مستوى السجل
loglevel = "info"

# ملفات السجل
accesslog = "logs/gunicorn_access.log"
errorlog = "logs/gunicorn_error.log"

# تنسيق سجل الوصول
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# تدوير السجلات
logconfig_dict = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'generic': {
            'format': '%(asctime)s [%(process)d] [%(levelname)s] %(message)s',
            'datefmt': '%Y-%m-%d %H:%M:%S',
            'class': 'logging.Formatter'
        },
        'access': {
            'format': '%(message)s',
            'class': 'logging.Formatter'
        }
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'generic',
            'stream': 'ext://sys.stdout'
        },
        'error_file': {
            'class': 'logging.handlers.RotatingFileHandler',
            'formatter': 'generic',
            'filename': 'logs/gunicorn_error.log',
            'maxBytes': 10485760,  # 10MB
            'backupCount': 5
        },
        'access_file': {
            'class': 'logging.handlers.RotatingFileHandler',
            'formatter': 'access',
            'filename': 'logs/gunicorn_access.log',
            'maxBytes': 10485760,  # 10MB
            'backupCount': 5
        }
    },
    'loggers': {
        'gunicorn.error': {
            'handlers': ['console', 'error_file'],
            'level': 'INFO',
            'propagate': False
        },
        'gunicorn.access': {
            'handlers': ['access_file'],
            'level': 'INFO',
            'propagate': False
        }
    },
    'root': {
        'level': 'INFO',
        'handlers': ['console']
    }
}

# ===== إعدادات التطوير/الإنتاج =====

# تحديد البيئة
env = os.environ.get('FLASK_ENV', 'production')

if env == 'development':
    # إعدادات التطوير
    reload = True
    workers = 2
    loglevel = "debug"
    timeout = 120
else:
    # إعدادات الإنتاج
    reload = False
    preload_app = True
    
    # تحسينات الأداء
    worker_tmp_dir = "/dev/shm"  # استخدام الذاكرة المشتركة في Linux
    
    # إعدادات SSL (إذا لزم الأمر)
    # keyfile = "/path/to/ssl/key.pem"
    # certfile = "/path/to/ssl/cert.pem"

# ===== دوال الخطافات (Hooks) =====

def on_starting(server):
    """عند بدء الخادم"""
    server.log.info("🚀 بدء تشغيل خادم Ta9affi...")
    
    # إنشاء مجلدات السجلات
    os.makedirs("logs", exist_ok=True)

def on_reload(server):
    """عند إعادة تحميل الخادم"""
    server.log.info("🔄 إعادة تحميل خادم Ta9affi...")

def worker_int(worker):
    """عند مقاطعة العامل"""
    worker.log.info(f"⚠️ مقاطعة العامل {worker.pid}")

def pre_fork(server, worker):
    """قبل إنشاء عامل جديد"""
    server.log.info(f"👷 إنشاء عامل جديد {worker.pid}")

def post_fork(server, worker):
    """بعد إنشاء عامل جديد"""
    server.log.info(f"✅ تم إنشاء العامل {worker.pid}")

def post_worker_init(worker):
    """بعد تهيئة العامل"""
    worker.log.info(f"🔧 تم تهيئة العامل {worker.pid}")

def worker_abort(worker):
    """عند إنهاء العامل بشكل مفاجئ"""
    worker.log.error(f"💥 إنهاء مفاجئ للعامل {worker.pid}")

def pre_exec(server):
    """قبل تنفيذ الخادم"""
    server.log.info("⚙️ تحضير تنفيذ الخادم...")

def when_ready(server):
    """عندما يصبح الخادم جاهز"""
    server.log.info(f"✅ خادم Ta9affi جاهز على {bind}")
    server.log.info(f"👷 عدد العمال: {workers}")
    server.log.info(f"🔗 نوع العامل: {worker_class}")

def on_exit(server):
    """عند إغلاق الخادم"""
    server.log.info("👋 إغلاق خادم Ta9affi...")

# ===== إعدادات متقدمة =====

# تمكين إعادة استخدام المنفذ
reuse_port = True

# تحسين TCP
tcp_nodelay = True

# إعدادات الشبكة
backlog = 2048

# تحسين الأداء للملفات الثابتة (سيتم التعامل معها بواسطة Nginx)
sendfile = False

# إعدادات الذاكرة المشتركة
shared_memory_size = 64 * 1024 * 1024  # 64MB

# ===== متغيرات البيئة =====

# تعيين متغيرات البيئة للتطبيق
raw_env = [
    'FLASK_ENV=production',
    'PYTHONPATH=/path/to/ta9affi',
]

# إعدادات Python
pythonpath = '/path/to/ta9affi'
python_path = '/path/to/ta9affi'

# ===== إعدادات المراقبة =====

# تمكين إحصائيات العمال
enable_stdio_inheritance = True

# معلومات العملية
pidfile = "logs/gunicorn.pid"

# إعدادات الصحة
health_check_interval = 30


