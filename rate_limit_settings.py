#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نموذج إعدادات Rate Limiting القابلة للتخصيص
"""

from datetime import datetime
from models_new import db
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, Float

class RateLimitSettings(db.Model):
    """إعدادات Rate Limiting القابلة للتخصيص"""
    
    __tablename__ = 'rate_limit_settings'
    
    id = Column(Integer, primary_key=True)
    
    # إعدادات إضافة التقدمات
    add_progress_limit = Column(Integer, default=10, nullable=False)
    add_progress_window_hours = Column(Integer, default=12, nullable=False)
    
    # إعدادات حذف التقدمات
    delete_progress_limit = Column(Integer, default=3, nullable=False)
    delete_progress_window_hours = Column(Integer, default=12, nullable=False)
    
    # إعدادات إضافية
    login_attempts_limit = Column(Integer, default=5, nullable=False)
    login_attempts_window_hours = Column(Integer, default=1, nullable=False)
    
    file_upload_limit = Column(Integer, default=20, nullable=False)
    file_upload_window_hours = Column(Integer, default=1, nullable=False)
    
    # معلومات التحديث
    updated_by = Column(Integer, nullable=True)  # معرف المدير الذي قام بالتحديث
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # ملاحظات
    notes = Column(Text, nullable=True)
    
    # حالة التفعيل
    is_active = Column(Boolean, default=True, nullable=False)
    
    def __repr__(self):
        return f'<RateLimitSettings add:{self.add_progress_limit}/delete:{self.delete_progress_limit}>'
    
    def to_dict(self):
        """تحويل إلى قاموس"""
        return {
            'id': self.id,
            'add_progress_limit': self.add_progress_limit,
            'add_progress_window_hours': self.add_progress_window_hours,
            'delete_progress_limit': self.delete_progress_limit,
            'delete_progress_window_hours': self.delete_progress_window_hours,
            'login_attempts_limit': self.login_attempts_limit,
            'login_attempts_window_hours': self.login_attempts_window_hours,
            'file_upload_limit': self.file_upload_limit,
            'file_upload_window_hours': self.file_upload_window_hours,
            'updated_by': self.updated_by,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'notes': self.notes,
            'is_active': self.is_active
        }
    
    @classmethod
    def get_current_settings(cls):
        """الحصول على الإعدادات الحالية"""
        settings = cls.query.filter_by(is_active=True).first()
        if not settings:
            # إنشاء إعدادات افتراضية
            settings = cls()
            db.session.add(settings)
            db.session.commit()
        return settings
    
    @classmethod
    def update_settings(cls, admin_id, **kwargs):
        """تحديث الإعدادات"""
        current_settings = cls.get_current_settings()
        
        # تحديث القيم
        for key, value in kwargs.items():
            if hasattr(current_settings, key):
                setattr(current_settings, key, value)
        
        current_settings.updated_by = admin_id
        current_settings.updated_at = datetime.utcnow()
        
        db.session.commit()
        return current_settings

class RateLimitHistory(db.Model):
    """سجل تغييرات إعدادات Rate Limiting"""
    
    __tablename__ = 'rate_limit_history'
    
    id = Column(Integer, primary_key=True)
    
    # الإعدادات القديمة والجديدة
    old_settings = Column(Text, nullable=True)  # JSON
    new_settings = Column(Text, nullable=True)  # JSON
    
    # معلومات التغيير
    changed_by = Column(Integer, nullable=False)  # معرف المدير
    changed_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # سبب التغيير
    reason = Column(Text, nullable=True)
    
    # نوع التغيير
    change_type = Column(String(50), default='update', nullable=False)  # update, create, delete
    
    def __repr__(self):
        return f'<RateLimitHistory {self.change_type} by {self.changed_by}>'
    
    @classmethod
    def log_change(cls, admin_id, old_settings, new_settings, reason=None, change_type='update'):
        """تسجيل تغيير في السجل"""
        import json
        
        history = cls(
            old_settings=json.dumps(old_settings) if old_settings else None,
            new_settings=json.dumps(new_settings) if new_settings else None,
            changed_by=admin_id,
            reason=reason,
            change_type=change_type
        )
        
        db.session.add(history)
        db.session.commit()
        return history

class UserRateLimitOverride(db.Model):
    """تخصيص حدود Rate Limiting لمستخدمين محددين"""
    
    __tablename__ = 'user_rate_limit_override'
    
    id = Column(Integer, primary_key=True)
    
    # معرف المستخدم
    user_id = Column(Integer, nullable=False, unique=True)
    
    # حدود مخصصة (null = استخدام الإعدادات العامة)
    add_progress_limit = Column(Integer, nullable=True)
    add_progress_window_hours = Column(Integer, nullable=True)
    
    delete_progress_limit = Column(Integer, nullable=True)
    delete_progress_window_hours = Column(Integer, nullable=True)
    
    # معلومات التخصيص
    created_by = Column(Integer, nullable=False)  # معرف المدير
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    updated_by = Column(Integer, nullable=True)
    updated_at = Column(DateTime, onupdate=datetime.utcnow)
    
    # سبب التخصيص
    reason = Column(Text, nullable=True)
    
    # حالة التفعيل
    is_active = Column(Boolean, default=True, nullable=False)
    
    # تاريخ انتهاء التخصيص (اختياري)
    expires_at = Column(DateTime, nullable=True)
    
    def __repr__(self):
        return f'<UserRateLimitOverride user:{self.user_id}>'
    
    def to_dict(self):
        """تحويل إلى قاموس"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'add_progress_limit': self.add_progress_limit,
            'add_progress_window_hours': self.add_progress_window_hours,
            'delete_progress_limit': self.delete_progress_limit,
            'delete_progress_window_hours': self.delete_progress_window_hours,
            'created_by': self.created_by,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_by': self.updated_by,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'reason': self.reason,
            'is_active': self.is_active,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None
        }
    
    @classmethod
    def get_user_override(cls, user_id):
        """الحصول على تخصيص المستخدم"""
        override = cls.query.filter_by(
            user_id=user_id, 
            is_active=True
        ).first()
        
        # فحص انتهاء الصلاحية
        if override and override.expires_at and override.expires_at < datetime.utcnow():
            override.is_active = False
            db.session.commit()
            return None
        
        return override
    
    @classmethod
    def set_user_override(cls, admin_id, user_id, **kwargs):
        """تعيين تخصيص للمستخدم"""
        # البحث عن تخصيص موجود
        override = cls.query.filter_by(user_id=user_id).first()
        
        if override:
            # تحديث التخصيص الموجود
            for key, value in kwargs.items():
                if hasattr(override, key):
                    setattr(override, key, value)
            override.updated_by = admin_id
            override.updated_at = datetime.utcnow()
            override.is_active = True
        else:
            # إنشاء تخصيص جديد
            override = cls(
                user_id=user_id,
                created_by=admin_id,
                **kwargs
            )
            db.session.add(override)
        
        db.session.commit()
        return override

# دوال مساعدة للتكامل مع نظام Rate Limiting

def get_user_rate_limits(user_id):
    """الحصول على حدود المستخدم (مع التخصيصات)"""
    # فحص التخصيص الشخصي أولاً
    override = UserRateLimitOverride.get_user_override(user_id)
    if override:
        return {
            'add_progress': {
                'max_requests': override.add_progress_limit or RateLimitSettings.get_current_settings().add_progress_limit,
                'window_hours': override.add_progress_window_hours or RateLimitSettings.get_current_settings().add_progress_window_hours
            },
            'delete_progress': {
                'max_requests': override.delete_progress_limit or RateLimitSettings.get_current_settings().delete_progress_limit,
                'window_hours': override.delete_progress_window_hours or RateLimitSettings.get_current_settings().delete_progress_window_hours
            }
        }
    
    # استخدام الإعدادات العامة
    settings = RateLimitSettings.get_current_settings()
    return {
        'add_progress': {
            'max_requests': settings.add_progress_limit,
            'window_hours': settings.add_progress_window_hours
        },
        'delete_progress': {
            'max_requests': settings.delete_progress_limit,
            'window_hours': settings.delete_progress_window_hours
        }
    }

def update_rate_limiter_settings():
    """تحديث إعدادات Rate Limiter من قاعدة البيانات"""
    try:
        from rate_limiter import rate_limiter
        settings = RateLimitSettings.get_current_settings()
        
        # تحديث الإعدادات في Rate Limiter
        rate_limiter.rate_limits['add_progress'].max_requests = settings.add_progress_limit
        rate_limiter.rate_limits['add_progress'].window_hours = settings.add_progress_window_hours
        
        rate_limiter.rate_limits['delete_progress'].max_requests = settings.delete_progress_limit
        rate_limiter.rate_limits['delete_progress'].window_hours = settings.delete_progress_window_hours
        
        rate_limiter.rate_limits['login_attempts'].max_requests = settings.login_attempts_limit
        rate_limiter.rate_limits['login_attempts'].window_hours = settings.login_attempts_window_hours
        
        rate_limiter.rate_limits['file_upload'].max_requests = settings.file_upload_limit
        rate_limiter.rate_limits['file_upload'].window_hours = settings.file_upload_window_hours
        
        return True
    except Exception as e:
        print(f"خطأ في تحديث إعدادات Rate Limiter: {e}")
        return False
