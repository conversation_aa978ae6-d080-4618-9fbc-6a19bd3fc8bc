#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير الجلسات المحسن باستخدام Redis
يوفر إدارة متقدمة للجلسات مع دعم المستخدمين المتزامنين
"""

from flask import session, request, current_app
from flask_login import current_user
from datetime import datetime, timedelta
import uuid
import logging
from redis_manager import redis_manager

class SessionManager:
    """مدير الجلسات المحسن"""
    
    def __init__(self, app=None):
        self.app = app
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """تهيئة مدير الجلسات مع التطبيق"""
        self.app = app
        
        # إعدادات الجلسات
        app.config.setdefault('SESSION_TIMEOUT_HOURS', 24)
        app.config.setdefault('SESSION_CLEANUP_INTERVAL', 3600)  # ساعة واحدة
        
        # تسجيل معالجات الأحداث
        app.before_request(self.before_request)
        app.after_request(self.after_request)
        
        logging.info("✅ تم تهيئة مدير الجلسات")
    
    def before_request(self):
        """معالج ما قبل الطلب"""
        if current_user.is_authenticated:
            # تحديث آخر نشاط للمستخدم
            self.update_user_activity(current_user.id)
            
            # التحقق من صحة الجلسة
            if not self.validate_session():
                self.end_current_session()
    
    def after_request(self, response):
        """معالج ما بعد الطلب"""
        # تنظيف دوري للجلسات المنتهية الصلاحية
        import random
        if random.randint(1, 100) == 1:  # 1% من الطلبات
            self.cleanup_expired_sessions()
        
        return response
    
    def create_session(self, user_id, remember_me=False):
        """إنشاء جلسة جديدة للمستخدم"""
        try:
            # إنهاء الجلسات القديمة إذا لزم الأمر
            if not remember_me:
                self.end_all_user_sessions(user_id)
            
            # بيانات الجلسة
            session_data = {
                'ip_address': self.get_client_ip(),
                'user_agent': request.headers.get('User-Agent', '')[:500],
                'remember_me': remember_me
            }
            
            # مدة انتهاء الصلاحية
            expire_hours = 24 * 30 if remember_me else 24  # 30 يوم أو 24 ساعة
            
            # إنشاء الجلسة في Redis
            session_id = redis_manager.create_session(user_id, session_data, expire_hours)
            
            if session_id:
                # حفظ معرف الجلسة في جلسة Flask
                session['session_id'] = session_id
                session['user_id'] = user_id
                session.permanent = remember_me
                
                logging.info(f"✅ تم إنشاء جلسة جديدة للمستخدم {user_id}")
                return session_id
            else:
                # العودة للنظام التقليدي إذا فشل Redis
                session['user_id'] = user_id
                session.permanent = remember_me
                logging.warning("⚠️ تم استخدام النظام التقليدي للجلسات")
                return str(uuid.uuid4())
                
        except Exception as e:
            logging.error(f"❌ خطأ في إنشاء الجلسة: {str(e)}")
            return None
    
    def validate_session(self):
        """التحقق من صحة الجلسة الحالية"""
        try:
            session_id = session.get('session_id')
            user_id = session.get('user_id')
            
            if not session_id or not user_id:
                return True  # جلسة تقليدية
            
            # التحقق من الجلسة في Redis
            session_data = redis_manager.get_session(session_id)
            
            if not session_data:
                logging.warning(f"⚠️ جلسة غير صالحة: {session_id}")
                return False
            
            # التحقق من تطابق المستخدم
            if session_data['user_id'] != user_id:
                logging.warning(f"⚠️ عدم تطابق المستخدم في الجلسة: {session_id}")
                return False
            
            # التحقق من IP (اختياري)
            current_ip = self.get_client_ip()
            session_ip = session_data.get('ip_address', '')
            
            if session_ip and current_ip != session_ip:
                logging.warning(f"⚠️ تغيير IP في الجلسة: {session_id}")
                # يمكن إضافة منطق إضافي هنا حسب سياسة الأمان
            
            return True
            
        except Exception as e:
            logging.error(f"❌ خطأ في التحقق من الجلسة: {str(e)}")
            return True  # السماح بالمرور في حالة الخطأ
    
    def update_user_activity(self, user_id):
        """تحديث آخر نشاط للمستخدم"""
        try:
            session_id = session.get('session_id')
            
            if session_id and redis_manager.is_available():
                redis_manager.update_session_activity(session_id)
            
            # تحديث في قاعدة البيانات أيضاً (للنظام التقليدي)
            from models_new import User, db
            user = User.query.get(user_id)
            if user:
                user.last_activity = datetime.utcnow()
                db.session.commit()
                
        except Exception as e:
            logging.error(f"❌ خطأ في تحديث نشاط المستخدم: {str(e)}")
    
    def end_current_session(self):
        """إنهاء الجلسة الحالية"""
        try:
            session_id = session.get('session_id')
            
            if session_id and redis_manager.is_available():
                redis_manager.end_session(session_id)
            
            # مسح جلسة Flask
            session.clear()
            
            logging.info("✅ تم إنهاء الجلسة الحالية")
            
        except Exception as e:
            logging.error(f"❌ خطأ في إنهاء الجلسة: {str(e)}")
    
    def end_all_user_sessions(self, user_id):
        """إنهاء جميع جلسات المستخدم"""
        try:
            if redis_manager.is_available():
                redis_manager.end_all_user_sessions(user_id)
            
            logging.info(f"✅ تم إنهاء جميع جلسات المستخدم {user_id}")
            
        except Exception as e:
            logging.error(f"❌ خطأ في إنهاء جلسات المستخدم: {str(e)}")
    
    def get_online_users_count(self):
        """حساب عدد المستخدمين المتصلين"""
        try:
            if redis_manager.is_available():
                return redis_manager.get_online_users_count()
            else:
                # العودة للطريقة التقليدية
                from models_new import UserSession
                return UserSession.get_online_users_count()
                
        except Exception as e:
            logging.error(f"❌ خطأ في حساب المستخدمين المتصلين: {str(e)}")
            return 0
    
    def get_user_sessions(self, user_id):
        """الحصول على جلسات المستخدم النشطة"""
        try:
            if not redis_manager.is_available():
                return []
            
            user_sessions_key = f"user_sessions:{user_id}"
            session_ids = redis_manager.redis_client.smembers(user_sessions_key)
            
            sessions = []
            for session_id in session_ids:
                session_data = redis_manager.get_session(session_id)
                if session_data:
                    sessions.append({
                        'session_id': session_id,
                        'created_at': session_data['created_at'],
                        'last_activity': session_data['last_activity'],
                        'ip_address': session_data.get('ip_address', ''),
                        'user_agent': session_data.get('user_agent', '')[:100] + '...' if len(session_data.get('user_agent', '')) > 100 else session_data.get('user_agent', '')
                    })
            
            return sessions
            
        except Exception as e:
            logging.error(f"❌ خطأ في الحصول على جلسات المستخدم: {str(e)}")
            return []
    
    def cleanup_expired_sessions(self):
        """تنظيف الجلسات المنتهية الصلاحية"""
        try:
            if redis_manager.is_available():
                cleaned_count = redis_manager.cleanup_expired_sessions()
                if cleaned_count > 0:
                    logging.info(f"🧹 تم تنظيف {cleaned_count} جلسة منتهية الصلاحية")
            
        except Exception as e:
            logging.error(f"❌ خطأ في تنظيف الجلسات: {str(e)}")
    
    def get_client_ip(self):
        """الحصول على عنوان IP الحقيقي للعميل"""
        # التحقق من headers الخاصة بـ proxy
        if request.headers.get('X-Forwarded-For'):
            return request.headers.get('X-Forwarded-For').split(',')[0].strip()
        elif request.headers.get('X-Real-IP'):
            return request.headers.get('X-Real-IP')
        else:
            return request.remote_addr or 'unknown'
    
    def get_session_stats(self):
        """الحصول على إحصائيات الجلسات"""
        try:
            if redis_manager.is_available():
                redis_stats = redis_manager.get_stats()
                return {
                    'redis_available': True,
                    'active_sessions': redis_stats.get('active_sessions', 0),
                    'online_users': self.get_online_users_count(),
                    'redis_memory': redis_stats.get('used_memory_human', '0B'),
                    'cache_hits': redis_stats.get('keyspace_hits', 0),
                    'cache_misses': redis_stats.get('keyspace_misses', 0)
                }
            else:
                return {
                    'redis_available': False,
                    'active_sessions': 0,
                    'online_users': 0,
                    'redis_memory': '0B',
                    'cache_hits': 0,
                    'cache_misses': 0
                }
                
        except Exception as e:
            logging.error(f"❌ خطأ في الحصول على إحصائيات الجلسات: {str(e)}")
            return {}

# إنشاء مثيل عام لمدير الجلسات
session_manager = SessionManager()
