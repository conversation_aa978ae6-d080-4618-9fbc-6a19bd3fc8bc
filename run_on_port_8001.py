#!/usr/bin/env python3
"""
تشغيل Ta9affi على المنفذ 8001 (بديل للمنفذ 8000 المستخدم)
"""

import os
import sys

def main():
    """تشغيل التطبيق على المنفذ 8001"""
    
    print("🚀 تشغيل Ta9affi على المنفذ 8001")
    print("="*50)
    
    # تعيين متغيرات البيئة
    os.environ['PRODUCTION_MODE'] = 'true'
    os.environ['SERVER_IP'] = '*************'
    os.environ['PORT'] = '8001'  # استخدام المنفذ 8001 بدلاً من 8000
    os.environ['SECRET_KEY'] = 'ta9affi-production-secret-key-2024'
    os.environ['DATABASE_URL'] = 'sqlite:///ta9affi_production.db'
    
    print("✅ تم تعيين متغيرات البيئة:")
    print(f"   🏭 وضع Production: {os.environ['PRODUCTION_MODE']}")
    print(f"   🌐 عنوان الخادم: {os.environ['SERVER_IP']}")
    print(f"   🔌 المنفذ: {os.environ['PORT']}")
    print(f"   🗄️ قاعدة البيانات: {os.environ['DATABASE_URL']}")
    
    print("\n🔗 روابط الوصول:")
    print(f"   📱 التطبيق: http://*************:8001")
    print(f"   🔐 تسجيل الدخول: http://*************:8001/login")
    print(f"   📝 التسجيل: http://*************:8001/register")
    print(f"   ❤️ فحص الصحة: http://*************:8001/health")
    
    print("\n💡 ملاحظة: يتم استخدام المنفذ 8001 لأن المنفذ 8000 مستخدم")
    
    print("\n" + "="*50)
    print("🚀 بدء تشغيل التطبيق...")
    print("⏹️ اضغط Ctrl+C لإيقاف التطبيق")
    print("="*50)
    
    try:
        # استيراد وتشغيل التطبيق
        from app import app
        
        # تشغيل التطبيق على المنفذ 8001
        app.run(
            host='0.0.0.0',
            port=8001,
            debug=False,
            threaded=True,
            use_reloader=False
        )
        
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف التطبيق بواسطة المستخدم")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل التطبيق: {str(e)}")
        print("\n💡 نصائح لحل المشكلة:")
        print("1. تأكد من تثبيت المتطلبات: pip install -r requirements.txt")
        print("2. تأكد من فتح المنفذ 8001 في Firewall:")
        print("   sudo ufw allow 8001")
        print("3. جرب تشغيل: python diagnose_server.py")
        sys.exit(1)

if __name__ == '__main__':
    main()
