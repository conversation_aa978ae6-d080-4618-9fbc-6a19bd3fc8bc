#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام التنبيهات والإشعارات لمراقبة التطبيق
يرسل تنبيهات عند حدوث مشاكل في الأداء أو الأخطاء
"""

import smtplib
import json
import time
import logging
from datetime import datetime, timedelta
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from threading import Thread, Lock
from collections import defaultdict, deque
from redis_manager import redis_manager

class AlertManager:
    """مدير التنبيهات والإشعارات"""
    
    def __init__(self, app=None):
        self.app = app
        self.alert_rules = {}
        self.alert_history = defaultdict(deque)
        self.alert_lock = Lock()
        self.is_running = False
        
        # إعدادات التنبيهات الافتراضية
        self.default_rules = {
            'high_cpu': {
                'metric': 'cpu_percent',
                'threshold': 85,
                'duration': 300,  # 5 دقائق
                'severity': 'warning',
                'cooldown': 1800  # 30 دقيقة
            },
            'high_memory': {
                'metric': 'memory_percent',
                'threshold': 90,
                'duration': 300,
                'severity': 'critical',
                'cooldown': 1800
            },
            'high_disk': {
                'metric': 'disk_percent',
                'threshold': 95,
                'duration': 600,  # 10 دقائق
                'severity': 'critical',
                'cooldown': 3600  # ساعة واحدة
            },
            'slow_response': {
                'metric': 'avg_response_time',
                'threshold': 3.0,
                'duration': 600,
                'severity': 'warning',
                'cooldown': 1800
            },
            'high_error_rate': {
                'metric': 'error_rate',
                'threshold': 10,  # 10%
                'duration': 300,
                'severity': 'critical',
                'cooldown': 900  # 15 دقيقة
            },
            'database_connections': {
                'metric': 'active_connections',
                'threshold': 80,
                'duration': 300,
                'severity': 'warning',
                'cooldown': 1800
            },
            'low_online_users': {
                'metric': 'online_users',
                'threshold': 0,
                'duration': 1800,  # 30 دقيقة
                'severity': 'info',
                'cooldown': 3600,
                'condition': 'below'  # تحت العتبة
            }
        }
        
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """تهيئة نظام التنبيهات"""
        self.app = app
        
        # تحميل إعدادات البريد الإلكتروني
        self.email_config = {
            'smtp_server': app.config.get('SMTP_SERVER', 'localhost'),
            'smtp_port': app.config.get('SMTP_PORT', 587),
            'smtp_username': app.config.get('SMTP_USERNAME', ''),
            'smtp_password': app.config.get('SMTP_PASSWORD', ''),
            'from_email': app.config.get('ALERT_FROM_EMAIL', '<EMAIL>'),
            'to_emails': app.config.get('ALERT_TO_EMAILS', []).split(',') if app.config.get('ALERT_TO_EMAILS') else []
        }
        
        # تحميل قواعد التنبيهات
        self.load_alert_rules()
        
        # بدء خيط المراقبة
        self.start_monitoring()
        
        logging.info("✅ تم تهيئة نظام التنبيهات")
    
    def load_alert_rules(self):
        """تحميل قواعد التنبيهات"""
        # استخدام القواعد الافتراضية
        self.alert_rules = self.default_rules.copy()
        
        # محاولة تحميل قواعد مخصصة من Redis
        if redis_manager.is_available():
            try:
                custom_rules = redis_manager.cache_get('alert_rules')
                if custom_rules:
                    self.alert_rules.update(custom_rules)
            except Exception as e:
                logging.error(f"Error loading custom alert rules: {str(e)}")
    
    def save_alert_rules(self):
        """حفظ قواعد التنبيهات"""
        if redis_manager.is_available():
            try:
                redis_manager.cache_set('alert_rules', self.alert_rules, 86400)
            except Exception as e:
                logging.error(f"Error saving alert rules: {str(e)}")
    
    def add_alert_rule(self, name, rule):
        """إضافة قاعدة تنبيه جديدة"""
        with self.alert_lock:
            self.alert_rules[name] = rule
            self.save_alert_rules()
    
    def remove_alert_rule(self, name):
        """حذف قاعدة تنبيه"""
        with self.alert_lock:
            if name in self.alert_rules:
                del self.alert_rules[name]
                self.save_alert_rules()
    
    def start_monitoring(self):
        """بدء مراقبة التنبيهات"""
        if not self.is_running:
            self.is_running = True
            monitor_thread = Thread(target=self._monitoring_loop, daemon=True)
            monitor_thread.start()
    
    def _monitoring_loop(self):
        """حلقة مراقبة التنبيهات"""
        while self.is_running:
            try:
                self._check_alerts()
                time.sleep(60)  # فحص كل دقيقة
            except Exception as e:
                logging.error(f"Alert monitoring error: {str(e)}")
                time.sleep(300)  # انتظار أطول في حالة الخطأ
    
    def _check_alerts(self):
        """فحص التنبيهات"""
        current_metrics = self._get_current_metrics()
        
        for rule_name, rule in self.alert_rules.items():
            try:
                self._evaluate_rule(rule_name, rule, current_metrics)
            except Exception as e:
                logging.error(f"Error evaluating rule {rule_name}: {str(e)}")
    
    def _get_current_metrics(self):
        """الحصول على المقاييس الحالية"""
        if not redis_manager.is_available():
            return {}
        
        try:
            # البحث عن أحدث المقاييس
            current_time = int(time.time())
            
            # البحث في آخر 5 دقائق
            for i in range(5):
                timestamp = current_time - (i * 60)
                key = f"metrics:system:{timestamp}"
                
                metric_data = redis_manager.redis_client.get(key)
                if metric_data:
                    return json.loads(metric_data)
            
            return {}
            
        except Exception as e:
            logging.error(f"Error getting current metrics: {str(e)}")
            return {}
    
    def _evaluate_rule(self, rule_name, rule, metrics):
        """تقييم قاعدة تنبيه"""
        metric_name = rule['metric']
        threshold = rule['threshold']
        duration = rule['duration']
        severity = rule['severity']
        cooldown = rule['cooldown']
        condition = rule.get('condition', 'above')  # above أو below
        
        # الحصول على قيمة المقياس
        metric_value = self._extract_metric_value(metrics, metric_name)
        
        if metric_value is None:
            return
        
        # تحديد ما إذا كان التنبيه مُفعل
        is_triggered = False
        if condition == 'above':
            is_triggered = metric_value > threshold
        elif condition == 'below':
            is_triggered = metric_value < threshold
        
        current_time = time.time()
        
        # إدارة تاريخ التنبيهات
        alert_history = self.alert_history[rule_name]
        
        if is_triggered:
            # إضافة نقطة تفعيل
            alert_history.append(current_time)
            
            # إزالة النقاط القديمة
            while alert_history and alert_history[0] < current_time - duration:
                alert_history.popleft()
            
            # التحقق من استمرار التفعيل للمدة المطلوبة
            if len(alert_history) >= duration / 60:  # تقريباً دقيقة واحدة لكل نقطة
                self._trigger_alert(rule_name, rule, metric_value, metrics)
        else:
            # مسح تاريخ التنبيه إذا لم يعد مُفعلاً
            alert_history.clear()
    
    def _extract_metric_value(self, metrics, metric_name):
        """استخراج قيمة المقياس من البيانات"""
        if not metrics:
            return None
        
        # البحث في المستوى الأول
        if metric_name in metrics:
            return metrics[metric_name]
        
        # البحث في قسم قاعدة البيانات
        if 'database' in metrics and metric_name in metrics['database']:
            return metrics['database'][metric_name]
        
        # البحث في قسم التطبيق
        if 'application' in metrics and metric_name in metrics['application']:
            return metrics['application'][metric_name]
        
        # البحث في قسم Redis
        if 'redis' in metrics and metric_name in metrics['redis']:
            return metrics['redis'][metric_name]
        
        return None
    
    def _trigger_alert(self, rule_name, rule, metric_value, metrics):
        """تفعيل التنبيه"""
        current_time = time.time()
        cooldown = rule['cooldown']
        
        # التحقق من فترة التهدئة
        last_alert_key = f"last_alert:{rule_name}"
        if redis_manager.is_available():
            try:
                last_alert_time = redis_manager.cache_get(last_alert_key)
                if last_alert_time and current_time - last_alert_time < cooldown:
                    return  # لا نرسل تنبيه بعد
            except:
                pass
        
        # إنشاء التنبيه
        alert = {
            'rule_name': rule_name,
            'severity': rule['severity'],
            'metric': rule['metric'],
            'value': metric_value,
            'threshold': rule['threshold'],
            'timestamp': datetime.utcnow().isoformat(),
            'metrics': metrics
        }
        
        # إرسال التنبيه
        self._send_alert(alert)
        
        # تسجيل وقت آخر تنبيه
        if redis_manager.is_available():
            try:
                redis_manager.cache_set(last_alert_key, current_time, cooldown)
            except:
                pass
        
        # تسجيل التنبيه
        logging.getLogger('alerts').warning(
            f"ALERT - {rule_name} - {rule['severity']} - {rule['metric']}={metric_value} (threshold={rule['threshold']})"
        )
    
    def _send_alert(self, alert):
        """إرسال التنبيه"""
        try:
            # إرسال عبر البريد الإلكتروني
            if self.email_config['to_emails']:
                self._send_email_alert(alert)
            
            # حفظ في Redis للعرض في لوحة التحكم
            self._save_alert_to_redis(alert)
            
        except Exception as e:
            logging.error(f"Error sending alert: {str(e)}")
    
    def _send_email_alert(self, alert):
        """إرسال تنبيه عبر البريد الإلكتروني"""
        try:
            subject = f"[Ta9affi Alert] {alert['severity'].upper()} - {alert['rule_name']}"
            
            body = f"""
تنبيه من نظام Ta9affi

التفاصيل:
- القاعدة: {alert['rule_name']}
- الخطورة: {alert['severity']}
- المقياس: {alert['metric']}
- القيمة الحالية: {alert['value']}
- العتبة: {alert['threshold']}
- الوقت: {alert['timestamp']}

معلومات النظام:
- استخدام المعالج: {alert['metrics'].get('cpu_percent', 'غير متاح')}%
- استخدام الذاكرة: {alert['metrics'].get('memory_percent', 'غير متاح')}%
- المستخدمين المتصلين: {alert['metrics'].get('application', {}).get('online_users', 'غير متاح')}

يرجى التحقق من النظام.
            """
            
            msg = MIMEMultipart()
            msg['From'] = self.email_config['from_email']
            msg['To'] = ', '.join(self.email_config['to_emails'])
            msg['Subject'] = subject
            
            msg.attach(MIMEText(body, 'plain', 'utf-8'))
            
            # إرسال البريد
            if self.email_config['smtp_username']:
                server = smtplib.SMTP(self.email_config['smtp_server'], self.email_config['smtp_port'])
                server.starttls()
                server.login(self.email_config['smtp_username'], self.email_config['smtp_password'])
                server.send_message(msg)
                server.quit()
            
        except Exception as e:
            logging.error(f"Error sending email alert: {str(e)}")
    
    def _save_alert_to_redis(self, alert):
        """حفظ التنبيه في Redis"""
        if redis_manager.is_available():
            try:
                # حفظ التنبيه مع انتهاء صلاحية
                alert_key = f"alert:{int(time.time())}"
                redis_manager.redis_client.setex(alert_key, 86400, json.dumps(alert))
                
                # إضافة للقائمة الحديثة
                redis_manager.redis_client.lpush('recent_alerts', json.dumps(alert))
                redis_manager.redis_client.ltrim('recent_alerts', 0, 99)  # الاحتفاظ بآخر 100 تنبيه
                
            except Exception as e:
                logging.error(f"Error saving alert to Redis: {str(e)}")
    
    def get_recent_alerts(self, limit=20):
        """الحصول على التنبيهات الحديثة"""
        if not redis_manager.is_available():
            return []
        
        try:
            alerts_data = redis_manager.redis_client.lrange('recent_alerts', 0, limit - 1)
            alerts = []
            
            for alert_data in alerts_data:
                try:
                    alert = json.loads(alert_data)
                    alerts.append(alert)
                except:
                    continue
            
            return alerts
            
        except Exception as e:
            logging.error(f"Error getting recent alerts: {str(e)}")
            return []
    
    def get_alert_statistics(self, hours=24):
        """الحصول على إحصائيات التنبيهات"""
        alerts = self.get_recent_alerts(1000)  # جلب عدد كبير
        
        # فلترة التنبيهات حسب الوقت
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        recent_alerts = []
        
        for alert in alerts:
            try:
                alert_time = datetime.fromisoformat(alert['timestamp'])
                if alert_time > cutoff_time:
                    recent_alerts.append(alert)
            except:
                continue
        
        # حساب الإحصائيات
        stats = {
            'total_alerts': len(recent_alerts),
            'critical_alerts': len([a for a in recent_alerts if a.get('severity') == 'critical']),
            'warning_alerts': len([a for a in recent_alerts if a.get('severity') == 'warning']),
            'info_alerts': len([a for a in recent_alerts if a.get('severity') == 'info']),
            'alerts_by_rule': defaultdict(int)
        }
        
        for alert in recent_alerts:
            stats['alerts_by_rule'][alert.get('rule_name', 'unknown')] += 1
        
        return stats

# إنشاء مثيل عام لمدير التنبيهات
alert_manager = AlertManager()

# إعداد logger للتنبيهات
alerts_logger = logging.getLogger('alerts')
alerts_handler = logging.FileHandler('logs/alerts.log')
alerts_handler.setFormatter(logging.Formatter(
    '%(asctime)s - %(levelname)s - %(message)s'
))
alerts_logger.addHandler(alerts_handler)
alerts_logger.setLevel(logging.INFO)
