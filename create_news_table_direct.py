#!/usr/bin/env python3
"""
إنشاء جدول الأخبار مباشرة في قاعدة البيانات
"""

import sqlite3
import os
from datetime import datetime

def create_news_table_direct():
    """إنشاء جدول الأخبار مباشرة في SQLite"""
    
    # مسار قاعدة البيانات
    db_path = 'instance/ta9affi.db'
    
    if not os.path.exists(db_path):
        print("❌ ملف قاعدة البيانات غير موجود")
        return False
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # إنشاء جدول الأخبار
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS news_updates (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title VARCHAR(200) NOT NULL,
            content TEXT NOT NULL,
            is_active BOOLEAN DEFAULT 1,
            priority INTEGER DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            created_by INTEGER NOT NULL,
            FOREIGN KEY (created_by) REFERENCES user (id)
        )
        """
        
        cursor.execute(create_table_sql)
        print("✅ تم إنشاء جدول news_updates بنجاح")
        
        # التحقق من وجود مستخدم أدمن
        cursor.execute("SELECT id FROM user WHERE role = 'admin' LIMIT 1")
        admin_user = cursor.fetchone()
        
        if admin_user:
            admin_id = admin_user[0]
            
            # إضافة أخبار تجريبية
            sample_news = [
                {
                    'title': 'مرحباً بكم في نظام Ta9affi',
                    'content': 'نرحب بجميع المستخدمين في النظام الجديد لإدارة البرنامج السنوي للتدريس',
                    'priority': 10,
                    'is_active': 1,
                    'created_by': admin_id
                },
                {
                    'title': 'تحديث جديد للنظام',
                    'content': 'تم إضافة ميزات جديدة لتحسين تجربة المستخدم وسهولة الاستخدام',
                    'priority': 8,
                    'is_active': 1,
                    'created_by': admin_id
                },
                {
                    'title': 'نصائح للاستخدام الأمثل',
                    'content': 'ننصح بمراجعة دليل المستخدم للاستفادة من جميع ميزات النظام',
                    'priority': 5,
                    'is_active': 1,
                    'created_by': admin_id
                }
            ]
            
            # إدراج الأخبار التجريبية
            for news in sample_news:
                # التحقق من عدم وجود الخبر
                cursor.execute("SELECT id FROM news_updates WHERE title = ?", (news['title'],))
                existing = cursor.fetchone()
                
                if not existing:
                    insert_sql = """
                    INSERT INTO news_updates (title, content, priority, is_active, created_by)
                    VALUES (?, ?, ?, ?, ?)
                    """
                    cursor.execute(insert_sql, (
                        news['title'],
                        news['content'],
                        news['priority'],
                        news['is_active'],
                        news['created_by']
                    ))
                    print(f"✅ تم إضافة الخبر: {news['title']}")
                else:
                    print(f"⚠️ الخبر موجود بالفعل: {news['title']}")
        else:
            print("⚠️ لم يتم العثور على مستخدم أدمن")
        
        # حفظ التغييرات
        conn.commit()
        
        # عرض الأخبار المضافة
        cursor.execute("SELECT id, title, is_active, priority FROM news_updates ORDER BY priority DESC")
        news_list = cursor.fetchall()
        
        print(f"\n📊 الأخبار في قاعدة البيانات:")
        for news in news_list:
            status = "نشط" if news[2] else "معطل"
            print(f"   - {news[1]} (أولوية: {news[3]}, حالة: {status})")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجدول: {str(e)}")
        return False

def main():
    """تشغيل العملية"""
    print("🚀 إنشاء جدول الأخبار مباشرة في قاعدة البيانات")
    print("=" * 60)
    
    success = create_news_table_direct()
    
    if success:
        print(f"\n" + "=" * 60)
        print(f"📋 تم إنشاء جدول الأخبار بنجاح:")
        print(f"   ✅ جدول news_updates تم إنشاؤه")
        print(f"   ✅ أخبار تجريبية تم إضافتها")
        print(f"   ✅ النظام جاهز للاستخدام")
        
        print(f"\n🎯 الخطوات التالية:")
        print(f"   1. أعد تشغيل الخادم: python app.py")
        print(f"   2. افتح الصفحة الرئيسية: http://127.0.0.1:5000/")
        print(f"   3. تحقق من ظهور شريط الأخبار")
        print(f"   4. سجل دخول كأدمن: admin_thr")
        print(f"   5. اذهب إلى: http://127.0.0.1:5000/admin/news")
    else:
        print(f"\n❌ فشل في إنشاء الجدول")
        print(f"🔧 تحقق من وجود ملف قاعدة البيانات")

if __name__ == "__main__":
    main()
