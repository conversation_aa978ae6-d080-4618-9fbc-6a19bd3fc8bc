<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار API المواد</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>اختبار API المواد حسب المستوى</h2>
        
        <div class="row mt-4">
            <div class="col-md-6">
                <label for="level_select" class="form-label">اختر المستوى:</label>
                <select class="form-select" id="level_select">
                    <option value="">اختر المستوى</option>
                    <option value="1">السنة الأولى ابتدائي</option>
                    <option value="2">السنة الثانية ابتدائي</option>
                    <option value="3">السنة الثالثة ابتدائي</option>
                    <option value="4">السنة الرابعة ابتدائي</option>
                    <option value="5">السنة الخامسة ابتدائي</option>
                </select>
            </div>
            
            <div class="col-md-6">
                <label for="subject_select" class="form-label">المواد المتاحة:</label>
                <select class="form-select" id="subject_select" disabled>
                    <option value="">اختر المادة</option>
                </select>
            </div>
        </div>
        
        <div class="mt-4">
            <h4>معلومات التشخيص:</h4>
            <div id="debug_info" class="alert alert-info">
                اختر مستوى لرؤية المواد المتاحة
            </div>
        </div>
        
        <div class="mt-4">
            <h4>استجابة API الخام:</h4>
            <pre id="api_response" class="bg-light p-3 border rounded">
                لم يتم استدعاء API بعد
            </pre>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const levelSelect = document.getElementById('level_select');
            const subjectSelect = document.getElementById('subject_select');
            const debugInfo = document.getElementById('debug_info');
            const apiResponse = document.getElementById('api_response');
            
            levelSelect.addEventListener('change', function() {
                const levelId = this.value;
                
                if (!levelId) {
                    subjectSelect.innerHTML = '<option value="">اختر المادة</option>';
                    subjectSelect.disabled = true;
                    debugInfo.innerHTML = 'اختر مستوى لرؤية المواد المتاحة';
                    apiResponse.textContent = 'لم يتم استدعاء API بعد';
                    return;
                }
                
                debugInfo.innerHTML = `جاري تحميل المواد للمستوى ${levelId}...`;
                apiResponse.textContent = 'جاري التحميل...';
                
                // Reset subject dropdown
                subjectSelect.innerHTML = '<option value="" selected disabled>جاري التحميل...</option>';
                subjectSelect.disabled = true;
                
                // Fetch subjects for selected level
                const apiUrl = `/api/subjects/${levelId}`;
                console.log('استدعاء API:', apiUrl);
                
                fetch(apiUrl)
                    .then(response => {
                        console.log('استجابة HTTP:', response.status, response.statusText);
                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('بيانات API:', data);
                        
                        // عرض الاستجابة الخام
                        apiResponse.textContent = JSON.stringify(data, null, 2);
                        
                        // Reset subject dropdown
                        subjectSelect.innerHTML = '<option value="" selected disabled>اختر المادة</option>';
                        subjectSelect.disabled = false;
                        
                        // Populate subject dropdown
                        if (data && data.length > 0) {
                            data.forEach(subject => {
                                const option = document.createElement('option');
                                option.value = subject.id;
                                option.textContent = subject.name;
                                
                                if (subject.source === 'database') {
                                    option.textContent = `${subject.name} (من قاعدة البيانات)`;
                                }
                                
                                subjectSelect.appendChild(option);
                            });
                            
                            debugInfo.innerHTML = `✅ تم تحميل ${data.length} مادة للمستوى ${levelId}`;
                        } else {
                            debugInfo.innerHTML = `⚠️ لا توجد مواد للمستوى ${levelId}`;
                        }
                    })
                    .catch(error => {
                        console.error('خطأ في API:', error);
                        debugInfo.innerHTML = `❌ خطأ في تحميل المواد: ${error.message}`;
                        apiResponse.textContent = `خطأ: ${error.message}`;
                        subjectSelect.innerHTML = '<option value="">خطأ في التحميل</option>';
                        subjectSelect.disabled = true;
                    });
            });
        });
    </script>
</body>
</html>
