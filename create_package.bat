@echo off
echo 📦 إنشاء حزمة Ta9affi الكاملة...

REM إنشاء مجلد مؤقت
mkdir ta9affi_package 2>nul

REM نسخ الملفات الأساسية
echo ✅ نسخ الملفات الأساسية...
copy app.py ta9affi_package\
copy models_new.py ta9affi_package\
copy news_model.py ta9affi_package\
copy subscription_manager.py ta9affi_package\
copy subscription_scheduler.py ta9affi_package\
copy rate_limiter.py ta9affi_package\
copy rate_limit_monitor.py ta9affi_package\
copy admin_rate_limit_manager.py ta9affi_package\
copy config.py ta9affi_package\
copy requirements_production.txt ta9affi_package\
copy gunicorn.conf.py ta9affi_package\

REM نسخ المجلدات
echo ✅ نسخ المجلدات...
xcopy templates ta9affi_package\templates\ /E /I /Q
xcopy static ta9affi_package\static\ /E /I /Q
xcopy instance ta9affi_package\instance\ /E /I /Q
xcopy nginx ta9affi_package\nginx\ /E /I /Q

REM إنشاء ملف zip
echo ✅ إنشاء الحزمة المضغوطة...
powershell Compress-Archive -Path ta9affi_package\* -DestinationPath ta9affi_complete.zip -Force

REM تنظيف المجلد المؤقت
rmdir /s /q ta9affi_package

echo 🎉 تم إنشاء الحزمة: ta9affi_complete.zip
echo 📊 حجم الحزمة:
dir ta9affi_complete.zip

echo.
echo 🚀 تعليمات النشر:
echo 1. ارفع ta9affi_complete.zip للسيرفر
echo 2. فك الضغط في /opt/ta9affi
echo 3. ثبت المتطلبات وشغل التطبيق
echo.
pause
