/* ملف CSS محسن للأداء - Ta9affi */

/* تحسينات الأداء العامة */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-display: swap; /* تحسين تحميل الخطوط */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* تحسين تحميل الصور */
img {
    max-width: 100%;
    height: auto;
    loading: lazy; /* تحميل تدريجي للصور */
}

.lazy {
    opacity: 0;
    transition: opacity 0.3s;
}

.lazy.loaded {
    opacity: 1;
}

/* مؤشر التحميل */
.loading-spinner {
    display: inline-block;
    width: 40px;
    height: 40px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تحسين الجداول */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

/* تحسين النماذج */
.form-control {
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

/* تحسين الأزرار */
.btn {
    transition: all 0.15s ease-in-out;
    cursor: pointer;
}

.btn:disabled {
    cursor: not-allowed;
    opacity: 0.6;
}

/* تحسين الكروت */
.card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* تحسين القوائم */
.dropdown-menu {
    transform: translateY(-10px);
    opacity: 0;
    transition: all 0.2s ease-in-out;
    pointer-events: none;
}

.dropdown-menu.show {
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto;
}

/* تحسين الإشعارات */
.alert {
    animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* تحسين الشريط الجانبي */
.sidebar {
    transition: transform 0.3s ease-in-out;
}

.sidebar.collapsed {
    transform: translateX(-100%);
}

/* تحسين المحتوى التفاعلي */
.interactive-content {
    will-change: transform;
}

/* تحسين الرسوم البيانية */
.chart-container {
    position: relative;
    height: 400px;
    width: 100%;
}

.chart-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

/* تحسين الجداول الكبيرة */
.large-table {
    contain: layout style paint;
}

.large-table tbody tr {
    contain: layout style paint;
}

/* تحسين التمرير */
.scroll-container {
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
    scrollbar-color: #007bff #f1f1f1;
}

.scroll-container::-webkit-scrollbar {
    width: 8px;
}

.scroll-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.scroll-container::-webkit-scrollbar-thumb {
    background: #007bff;
    border-radius: 4px;
}

.scroll-container::-webkit-scrollbar-thumb:hover {
    background: #0056b3;
}

/* تحسين الشبكة */
.grid-container {
    display: grid;
    gap: 1rem;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

/* تحسين Flexbox */
.flex-container {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
}

.flex-item {
    flex: 1 1 300px;
    min-width: 0; /* منع overflow */
}

/* تحسين النصوص */
.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.text-clamp {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* تحسين الوسائط */
@media (max-width: 768px) {
    .mobile-hidden {
        display: none !important;
    }
    
    .mobile-stack {
        flex-direction: column !important;
    }
    
    .mobile-full-width {
        width: 100% !important;
    }
}

@media (max-width: 576px) {
    .container {
        padding-left: 10px;
        padding-right: 10px;
    }
    
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
}

/* تحسين الطباعة */
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-break {
        page-break-after: always;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
    }
    
    .container {
        width: 100% !important;
        max-width: none !important;
    }
}

/* تحسين الحركة للمستخدمين الذين يفضلون تقليل الحركة */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* تحسين الألوان للوضع المظلم */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-color: #1a1a1a;
        --text-color: #ffffff;
        --border-color: #333333;
        --card-bg: #2d2d2d;
    }
    
    body {
        background-color: var(--bg-color);
        color: var(--text-color);
    }
    
    .card {
        background-color: var(--card-bg);
        border-color: var(--border-color);
    }
    
    .form-control {
        background-color: var(--card-bg);
        border-color: var(--border-color);
        color: var(--text-color);
    }
}

/* تحسين التركيز للوصولية */
.focus-visible:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* تحسين التباين العالي */
@media (prefers-contrast: high) {
    .btn {
        border-width: 2px;
    }
    
    .card {
        border-width: 2px;
    }
    
    .form-control {
        border-width: 2px;
    }
}

/* تحسين الخطوط الكبيرة */
@media (prefers-font-size: large) {
    body {
        font-size: 1.2rem;
    }
    
    .btn {
        font-size: 1.1rem;
        padding: 0.6rem 1.2rem;
    }
}

/* فئات مساعدة للأداء */
.gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
}

.contain-layout {
    contain: layout;
}

.contain-paint {
    contain: paint;
}

.contain-strict {
    contain: strict;
}

/* تحسين الشبكة CSS Grid */
.grid-auto-fit {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.grid-auto-fill {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
}

/* تحسين التخطيط المرن */
.flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
}

.flex-between {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.flex-column {
    display: flex;
    flex-direction: column;
}

/* تحسين المساحات */
.space-y-1 > * + * {
    margin-top: 0.25rem;
}

.space-y-2 > * + * {
    margin-top: 0.5rem;
}

.space-y-3 > * + * {
    margin-top: 0.75rem;
}

.space-y-4 > * + * {
    margin-top: 1rem;
}

/* تحسين الظلال */
.shadow-sm {
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.shadow {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.shadow-md {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.shadow-lg {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* تحسين الحدود */
.rounded-sm {
    border-radius: 0.125rem;
}

.rounded {
    border-radius: 0.25rem;
}

.rounded-md {
    border-radius: 0.375rem;
}

.rounded-lg {
    border-radius: 0.5rem;
}

.rounded-xl {
    border-radius: 0.75rem;
}

.rounded-full {
    border-radius: 9999px;
}
