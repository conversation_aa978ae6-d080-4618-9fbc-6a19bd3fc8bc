<!-- مكون عرض حالة Rate Limiting للمستخدم -->
<div class="rate-limit-status-widget">
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-gradient-primary text-white">
            <h6 class="mb-0">
                <i class="fas fa-shield-alt"></i>
                حدود الاستخدام
            </h6>
        </div>
        <div class="card-body p-3">
            {% if user_rate_limits %}
                <!-- حدود إضافة التقدمات -->
                {% if user_rate_limits.add_progress %}
                <div class="limit-item mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="limit-label">
                            <i class="fas fa-plus-circle text-success"></i>
                            إضافة تقدمات
                        </span>
                        <span class="limit-count">
                            {{ user_rate_limits.add_progress.remaining }}/{{ user_rate_limits.add_progress.max_requests }}
                        </span>
                    </div>
                    
                    {% set add_percentage = (user_rate_limits.add_progress.current_count / user_rate_limits.add_progress.max_requests * 100) | round %}
                    
                    <div class="progress progress-sm">
                        <div class="progress-bar 
                            {% if add_percentage >= 90 %}bg-danger
                            {% elif add_percentage >= 70 %}bg-warning
                            {% else %}bg-success{% endif %}" 
                            role="progressbar" 
                            style="width: {{ add_percentage }}%"
                            aria-valuenow="{{ add_percentage }}" 
                            aria-valuemin="0" 
                            aria-valuemax="100">
                        </div>
                    </div>
                    
                    <small class="text-muted">
                        {% if user_rate_limits.add_progress.remaining > 0 %}
                            يمكنك إضافة {{ user_rate_limits.add_progress.remaining }} تقدمات أخرى
                        {% else %}
                            <span class="text-danger">
                                <i class="fas fa-exclamation-triangle"></i>
                                تم استنفاد الحد الأقصى
                            </span>
                        {% endif %}
                    </small>
                </div>
                {% endif %}
                
                <!-- حدود حذف التقدمات -->
                {% if user_rate_limits.delete_progress %}
                <div class="limit-item mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="limit-label">
                            <i class="fas fa-trash-alt text-danger"></i>
                            حذف تقدمات
                        </span>
                        <span class="limit-count">
                            {{ user_rate_limits.delete_progress.remaining }}/{{ user_rate_limits.delete_progress.max_requests }}
                        </span>
                    </div>
                    
                    {% set delete_percentage = (user_rate_limits.delete_progress.current_count / user_rate_limits.delete_progress.max_requests * 100) | round %}
                    
                    <div class="progress progress-sm">
                        <div class="progress-bar 
                            {% if delete_percentage >= 90 %}bg-danger
                            {% elif delete_percentage >= 70 %}bg-warning
                            {% else %}bg-info{% endif %}" 
                            role="progressbar" 
                            style="width: {{ delete_percentage }}%"
                            aria-valuenow="{{ delete_percentage }}" 
                            aria-valuemin="0" 
                            aria-valuemax="100">
                        </div>
                    </div>
                    
                    <small class="text-muted">
                        {% if user_rate_limits.delete_progress.remaining > 0 %}
                            يمكنك حذف {{ user_rate_limits.delete_progress.remaining }} تقدمات أخرى
                        {% else %}
                            <span class="text-danger">
                                <i class="fas fa-exclamation-triangle"></i>
                                تم استنفاد الحد الأقصى
                            </span>
                        {% endif %}
                    </small>
                </div>
                {% endif %}
                
                <!-- معلومات إعادة التعيين -->
                <div class="reset-info">
                    <hr class="my-2">
                    <small class="text-muted d-block">
                        <i class="fas fa-clock"></i>
                        إعادة تعيين الحدود كل 12 ساعة
                    </small>
                    {% if user_rate_limits.add_progress and user_rate_limits.add_progress.reset_time %}
                    <small class="text-muted d-block">
                        <i class="fas fa-sync-alt"></i>
                        التعيين التالي: 
                        <span class="reset-countdown" data-reset-time="{{ user_rate_limits.add_progress.reset_time }}">
                            جاري الحساب...
                        </span>
                    </small>
                    {% endif %}
                </div>
                
            {% else %}
                <!-- لا توجد بيانات حدود -->
                <div class="text-center text-muted py-3">
                    <i class="fas fa-info-circle fa-2x mb-2"></i>
                    <p class="mb-0">لا توجد قيود حالياً</p>
                    <small>يمكنك استخدام جميع الميزات بحرية</small>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- تحديث تلقائي لحالة Rate Limiting -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحديث العد التنازلي لإعادة التعيين
    function updateResetCountdown() {
        const countdownElements = document.querySelectorAll('.reset-countdown');
        
        countdownElements.forEach(element => {
            const resetTime = element.getAttribute('data-reset-time');
            if (resetTime) {
                const resetDate = new Date(resetTime);
                const now = new Date();
                const timeDiff = resetDate - now;
                
                if (timeDiff > 0) {
                    const hours = Math.floor(timeDiff / (1000 * 60 * 60));
                    const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
                    const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);
                    
                    element.textContent = `${hours}س ${minutes}د ${seconds}ث`;
                } else {
                    element.textContent = 'الآن';
                    // تحديث الصفحة إذا انتهى الوقت
                    setTimeout(() => {
                        updateRateLimitStatus();
                    }, 1000);
                }
            }
        });
    }
    
    // تحديث حالة Rate Limiting
    function updateRateLimitStatus() {
        fetch('/api/my-limits')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // تحديث الواجهة بالبيانات الجديدة
                location.reload(); // حل مؤقت - يمكن تحسينه لتحديث جزئي
            }
        })
        .catch(error => {
            console.error('خطأ في تحديث حالة Rate Limiting:', error);
        });
    }
    
    // تحديث العد التنازلي كل ثانية
    setInterval(updateResetCountdown, 1000);
    
    // تحديث حالة Rate Limiting كل دقيقة
    setInterval(updateRateLimitStatus, 60000);
    
    // تحديث أولي
    updateResetCountdown();
});

// دالة للتحقق من إمكانية تنفيذ عملية
function checkRateLimit(action, callback) {
    fetch('/api/my-limits')
    .then(response => response.json())
    .then(data => {
        if (data.success && data.limits[action]) {
            const remaining = data.limits[action].remaining;
            if (remaining > 0) {
                callback(true, remaining);
            } else {
                callback(false, 0);
                showRateLimitWarning(action);
            }
        } else {
            callback(true, null); // السماح في حالة عدم وجود بيانات
        }
    })
    .catch(error => {
        console.error('خطأ في فحص Rate Limiting:', error);
        callback(true, null); // السماح في حالة الخطأ
    });
}

// عرض تحذير Rate Limiting
function showRateLimitWarning(action) {
    const actionNames = {
        'add_progress': 'إضافة التقدمات',
        'delete_progress': 'حذف التقدمات'
    };
    
    const actionName = actionNames[action] || action;
    
    // يمكن استخدام مكتبة تنبيهات مثل SweetAlert أو Bootstrap Toast
    alert(`تم تجاوز الحد الأقصى لـ ${actionName}. يرجى المحاولة لاحقاً.`);
}

// دالة مساعدة للاستخدام في النماذج
function validateRateLimit(action, form) {
    checkRateLimit(action, function(allowed, remaining) {
        if (allowed) {
            form.submit();
        } else {
            showRateLimitWarning(action);
        }
    });
    return false; // منع الإرسال المباشر
}
</script>

<style>
.rate-limit-status-widget {
    margin-bottom: 20px;
}

.rate-limit-status-widget .card {
    border-radius: 10px;
    overflow: hidden;
}

.rate-limit-status-widget .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-bottom: none;
    padding: 12px 15px;
}

.rate-limit-status-widget .limit-item {
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 15px;
}

.rate-limit-status-widget .limit-item:last-child {
    border-bottom: none;
    margin-bottom: 0 !important;
}

.rate-limit-status-widget .limit-label {
    font-weight: 500;
    font-size: 0.9rem;
}

.rate-limit-status-widget .limit-count {
    font-weight: bold;
    font-size: 0.9rem;
    color: #495057;
}

.rate-limit-status-widget .progress {
    height: 6px;
    border-radius: 3px;
    background-color: #e9ecef;
}

.rate-limit-status-widget .progress-bar {
    border-radius: 3px;
    transition: width 0.3s ease;
}

.rate-limit-status-widget .progress-sm {
    height: 4px;
}

.rate-limit-status-widget .reset-info {
    background: #f8f9fa;
    margin: 10px -15px -15px -15px;
    padding: 10px 15px;
}

.rate-limit-status-widget .reset-countdown {
    font-weight: bold;
    color: #007bff;
}

@media (max-width: 768px) {
    .rate-limit-status-widget .card-body {
        padding: 15px;
    }
    
    .rate-limit-status-widget .limit-label,
    .rate-limit-status-widget .limit-count {
        font-size: 0.8rem;
    }
}
</style>
