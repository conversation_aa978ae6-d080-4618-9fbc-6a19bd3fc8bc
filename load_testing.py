#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام اختبار الحمولة والأداء لتطبيق Ta9affi
يختبر قدرة النظام على التعامل مع آلاف المستخدمين المتزامنين
"""

import asyncio
import aiohttp
import time
import json
import random
import logging
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor
import statistics
import psutil
import threading
from dataclasses import dataclass
from typing import List, Dict, Any

@dataclass
class TestResult:
    """نتيجة اختبار واحد"""
    url: str
    method: str
    status_code: int
    response_time: float
    success: bool
    error: str = None
    timestamp: datetime = None

class LoadTester:
    """مختبر الحمولة الرئيسي"""
    
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url.rstrip('/')
        self.results = []
        self.system_metrics = []
        self.is_monitoring = False
        
        # إعدادات الاختبار
        self.test_scenarios = {
            'light': {'users': 50, 'duration': 300},      # 50 مستخدم لـ 5 دقائق
            'medium': {'users': 200, 'duration': 600},    # 200 مستخدم لـ 10 دقائق
            'heavy': {'users': 500, 'duration': 900},     # 500 مستخدم لـ 15 دقيقة
            'extreme': {'users': 1000, 'duration': 1200}  # 1000 مستخدم لـ 20 دقيقة
        }
        
        # مسارات الاختبار
        self.test_endpoints = [
            {'path': '/', 'method': 'GET', 'weight': 30},
            {'path': '/login', 'method': 'GET', 'weight': 15},
            {'path': '/dashboard', 'method': 'GET', 'weight': 20, 'auth_required': True},
            {'path': '/api/user/profile', 'method': 'GET', 'weight': 10, 'auth_required': True},
            {'path': '/api/educational-data', 'method': 'GET', 'weight': 15, 'auth_required': True},
            {'path': '/api/progress', 'method': 'GET', 'weight': 10, 'auth_required': True}
        ]
        
        # بيانات اختبار المستخدمين
        self.test_users = [
            {'username': f'test_user_{i}', 'password': 'TestPassword123!'}
            for i in range(1, 101)  # 100 مستخدم اختبار
        ]
        
        # إعداد التسجيل
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('load_test.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    async def create_session(self):
        """إنشاء جلسة HTTP"""
        timeout = aiohttp.ClientTimeout(total=30)
        connector = aiohttp.TCPConnector(limit=1000, limit_per_host=100)
        return aiohttp.ClientSession(timeout=timeout, connector=connector)
    
    async def login_user(self, session, user):
        """تسجيل دخول مستخدم"""
        try:
            # الحصول على صفحة تسجيل الدخول أولاً
            async with session.get(f"{self.base_url}/login") as response:
                if response.status != 200:
                    return None
            
            # تسجيل الدخول
            login_data = {
                'username': user['username'],
                'password': user['password']
            }
            
            async with session.post(f"{self.base_url}/login", data=login_data) as response:
                if response.status in [200, 302]:  # نجح تسجيل الدخول أو إعادة توجيه
                    return session
                else:
                    return None
                    
        except Exception as e:
            self.logger.error(f"خطأ في تسجيل الدخول للمستخدم {user['username']}: {str(e)}")
            return None
    
    async def make_request(self, session, endpoint, user=None):
        """إجراء طلب HTTP"""
        url = f"{self.base_url}{endpoint['path']}"
        method = endpoint['method']
        
        start_time = time.time()
        
        try:
            # تسجيل الدخول إذا كان مطلوباً
            if endpoint.get('auth_required') and user:
                session = await self.login_user(session, user)
                if not session:
                    return TestResult(
                        url=url,
                        method=method,
                        status_code=401,
                        response_time=time.time() - start_time,
                        success=False,
                        error="فشل في تسجيل الدخول",
                        timestamp=datetime.now()
                    )
            
            # إجراء الطلب
            async with session.request(method, url) as response:
                response_time = time.time() - start_time
                
                # قراءة المحتوى (للتأكد من اكتمال الطلب)
                await response.read()
                
                return TestResult(
                    url=url,
                    method=method,
                    status_code=response.status,
                    response_time=response_time,
                    success=response.status < 400,
                    timestamp=datetime.now()
                )
                
        except asyncio.TimeoutError:
            return TestResult(
                url=url,
                method=method,
                status_code=0,
                response_time=time.time() - start_time,
                success=False,
                error="انتهت مهلة الطلب",
                timestamp=datetime.now()
            )
        except Exception as e:
            return TestResult(
                url=url,
                method=method,
                status_code=0,
                response_time=time.time() - start_time,
                success=False,
                error=str(e),
                timestamp=datetime.now()
            )
    
    def select_endpoint(self):
        """اختيار نقطة نهاية عشوائية بناءً على الأوزان"""
        weights = [ep['weight'] for ep in self.test_endpoints]
        return random.choices(self.test_endpoints, weights=weights)[0]
    
    async def simulate_user(self, user_id, duration):
        """محاكاة مستخدم واحد"""
        user = random.choice(self.test_users)
        session = await self.create_session()
        
        end_time = time.time() + duration
        user_results = []
        
        try:
            while time.time() < end_time:
                # اختيار نقطة نهاية عشوائية
                endpoint = self.select_endpoint()
                
                # إجراء الطلب
                result = await self.make_request(session, endpoint, user)
                user_results.append(result)
                
                # انتظار عشوائي بين الطلبات (1-5 ثواني)
                await asyncio.sleep(random.uniform(1, 5))
                
        finally:
            await session.close()
        
        return user_results
    
    async def run_load_test(self, scenario_name):
        """تشغيل اختبار الحمولة"""
        scenario = self.test_scenarios[scenario_name]
        users = scenario['users']
        duration = scenario['duration']
        
        self.logger.info(f"🚀 بدء اختبار الحمولة: {scenario_name}")
        self.logger.info(f"👥 عدد المستخدمين: {users}")
        self.logger.info(f"⏱️ المدة: {duration} ثانية")
        
        # بدء مراقبة النظام
        self.start_system_monitoring()
        
        start_time = time.time()
        
        # إنشاء مهام المستخدمين
        tasks = []
        for user_id in range(users):
            task = asyncio.create_task(self.simulate_user(user_id, duration))
            tasks.append(task)
        
        # انتظار انتهاء جميع المهام
        all_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # إيقاف مراقبة النظام
        self.stop_system_monitoring()
        
        # جمع النتائج
        for user_results in all_results:
            if isinstance(user_results, list):
                self.results.extend(user_results)
            elif isinstance(user_results, Exception):
                self.logger.error(f"خطأ في محاكاة المستخدم: {str(user_results)}")
        
        total_time = time.time() - start_time
        self.logger.info(f"✅ انتهى اختبار الحمولة في {total_time:.2f} ثانية")
        
        return self.analyze_results()
    
    def start_system_monitoring(self):
        """بدء مراقبة النظام"""
        self.is_monitoring = True
        self.system_metrics = []
        
        def monitor():
            while self.is_monitoring:
                try:
                    metrics = {
                        'timestamp': datetime.now(),
                        'cpu_percent': psutil.cpu_percent(interval=1),
                        'memory_percent': psutil.virtual_memory().percent,
                        'disk_io': psutil.disk_io_counters()._asdict() if psutil.disk_io_counters() else {},
                        'network_io': psutil.net_io_counters()._asdict() if psutil.net_io_counters() else {},
                        'connections': len(psutil.net_connections())
                    }
                    self.system_metrics.append(metrics)
                except Exception as e:
                    self.logger.error(f"خطأ في مراقبة النظام: {str(e)}")
                
                time.sleep(5)  # مراقبة كل 5 ثواني
        
        monitor_thread = threading.Thread(target=monitor, daemon=True)
        monitor_thread.start()
    
    def stop_system_monitoring(self):
        """إيقاف مراقبة النظام"""
        self.is_monitoring = False
    
    def analyze_results(self):
        """تحليل نتائج الاختبار"""
        if not self.results:
            return {"error": "لا توجد نتائج للتحليل"}
        
        # إحصائيات الطلبات
        total_requests = len(self.results)
        successful_requests = len([r for r in self.results if r.success])
        failed_requests = total_requests - successful_requests
        
        # أوقات الاستجابة
        response_times = [r.response_time for r in self.results if r.success]
        
        if response_times:
            avg_response_time = statistics.mean(response_times)
            median_response_time = statistics.median(response_times)
            min_response_time = min(response_times)
            max_response_time = max(response_times)
            p95_response_time = statistics.quantiles(response_times, n=20)[18]  # 95th percentile
            p99_response_time = statistics.quantiles(response_times, n=100)[98]  # 99th percentile
        else:
            avg_response_time = median_response_time = min_response_time = max_response_time = 0
            p95_response_time = p99_response_time = 0
        
        # معدل النجاح
        success_rate = (successful_requests / total_requests) * 100 if total_requests > 0 else 0
        
        # رموز الحالة
        status_codes = {}
        for result in self.results:
            status_codes[result.status_code] = status_codes.get(result.status_code, 0) + 1
        
        # الأخطاء
        errors = {}
        for result in self.results:
            if not result.success and result.error:
                errors[result.error] = errors.get(result.error, 0) + 1
        
        # إحصائيات النظام
        system_stats = self.analyze_system_metrics()
        
        # معدل الطلبات
        if self.results:
            test_duration = (self.results[-1].timestamp - self.results[0].timestamp).total_seconds()
            requests_per_second = total_requests / test_duration if test_duration > 0 else 0
        else:
            requests_per_second = 0
        
        analysis = {
            'summary': {
                'total_requests': total_requests,
                'successful_requests': successful_requests,
                'failed_requests': failed_requests,
                'success_rate': round(success_rate, 2),
                'requests_per_second': round(requests_per_second, 2)
            },
            'response_times': {
                'average': round(avg_response_time, 3),
                'median': round(median_response_time, 3),
                'min': round(min_response_time, 3),
                'max': round(max_response_time, 3),
                'p95': round(p95_response_time, 3),
                'p99': round(p99_response_time, 3)
            },
            'status_codes': status_codes,
            'errors': errors,
            'system_metrics': system_stats
        }
        
        return analysis
    
    def analyze_system_metrics(self):
        """تحليل مقاييس النظام"""
        if not self.system_metrics:
            return {}
        
        cpu_values = [m['cpu_percent'] for m in self.system_metrics]
        memory_values = [m['memory_percent'] for m in self.system_metrics]
        
        return {
            'cpu': {
                'average': round(statistics.mean(cpu_values), 2),
                'max': round(max(cpu_values), 2),
                'min': round(min(cpu_values), 2)
            },
            'memory': {
                'average': round(statistics.mean(memory_values), 2),
                'max': round(max(memory_values), 2),
                'min': round(min(memory_values), 2)
            },
            'peak_connections': max([m.get('connections', 0) for m in self.system_metrics])
        }
    
    def generate_report(self, analysis, scenario_name):
        """إنشاء تقرير مفصل"""
        report = f"""
# تقرير اختبار الحمولة - {scenario_name}
تاريخ الاختبار: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## ملخص النتائج
- إجمالي الطلبات: {analysis['summary']['total_requests']:,}
- الطلبات الناجحة: {analysis['summary']['successful_requests']:,}
- الطلبات الفاشلة: {analysis['summary']['failed_requests']:,}
- معدل النجاح: {analysis['summary']['success_rate']}%
- الطلبات/الثانية: {analysis['summary']['requests_per_second']}

## أوقات الاستجابة (ثانية)
- المتوسط: {analysis['response_times']['average']}
- الوسيط: {analysis['response_times']['median']}
- الأدنى: {analysis['response_times']['min']}
- الأعلى: {analysis['response_times']['max']}
- المئين 95: {analysis['response_times']['p95']}
- المئين 99: {analysis['response_times']['p99']}

## رموز الحالة
"""
        for code, count in analysis['status_codes'].items():
            report += f"- {code}: {count:,} طلب\n"
        
        if analysis['errors']:
            report += "\n## الأخطاء\n"
            for error, count in analysis['errors'].items():
                report += f"- {error}: {count} مرة\n"
        
        if analysis['system_metrics']:
            sys_metrics = analysis['system_metrics']
            report += f"""
## مقاييس النظام
### المعالج
- المتوسط: {sys_metrics['cpu']['average']}%
- الأعلى: {sys_metrics['cpu']['max']}%
- الأدنى: {sys_metrics['cpu']['min']}%

### الذاكرة
- المتوسط: {sys_metrics['memory']['average']}%
- الأعلى: {sys_metrics['memory']['max']}%
- الأدنى: {sys_metrics['memory']['min']}%

### الشبكة
- أعلى عدد اتصالات: {sys_metrics['peak_connections']}
"""
        
        return report
    
    def save_results(self, analysis, scenario_name):
        """حفظ النتائج في ملفات"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # حفظ التحليل كـ JSON
        with open(f'load_test_results_{scenario_name}_{timestamp}.json', 'w', encoding='utf-8') as f:
            json.dump(analysis, f, ensure_ascii=False, indent=2, default=str)
        
        # حفظ التقرير كـ Markdown
        report = self.generate_report(analysis, scenario_name)
        with open(f'load_test_report_{scenario_name}_{timestamp}.md', 'w', encoding='utf-8') as f:
            f.write(report)
        
        # حفظ النتائج الخام كـ CSV
        import csv
        with open(f'load_test_raw_{scenario_name}_{timestamp}.csv', 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['URL', 'Method', 'Status Code', 'Response Time', 'Success', 'Error', 'Timestamp'])
            
            for result in self.results:
                writer.writerow([
                    result.url, result.method, result.status_code,
                    result.response_time, result.success, result.error or '',
                    result.timestamp.isoformat() if result.timestamp else ''
                ])

async def main():
    """الدالة الرئيسية"""
    import argparse
    
    parser = argparse.ArgumentParser(description="اختبار الحمولة لتطبيق Ta9affi")
    parser.add_argument('--scenario', choices=['light', 'medium', 'heavy', 'extreme'], 
                       default='light', help='سيناريو الاختبار')
    parser.add_argument('--url', default='http://localhost:5000', help='رابط التطبيق')
    
    args = parser.parse_args()
    
    # إنشاء مختبر الحمولة
    tester = LoadTester(args.url)
    
    print(f"🧪 بدء اختبار الحمولة: {args.scenario}")
    print(f"🌐 الرابط: {args.url}")
    print("=" * 50)
    
    try:
        # تشغيل الاختبار
        analysis = await tester.run_load_test(args.scenario)
        
        # عرض النتائج
        print("\n📊 نتائج الاختبار:")
        print("=" * 50)
        print(f"إجمالي الطلبات: {analysis['summary']['total_requests']:,}")
        print(f"معدل النجاح: {analysis['summary']['success_rate']}%")
        print(f"متوسط وقت الاستجابة: {analysis['response_times']['average']} ثانية")
        print(f"الطلبات/الثانية: {analysis['summary']['requests_per_second']}")
        
        # حفظ النتائج
        tester.save_results(analysis, args.scenario)
        print(f"\n💾 تم حفظ النتائج في ملفات load_test_*_{args.scenario}_*.{'{json,md,csv}'}")
        
    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف الاختبار بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {str(e)}")

if __name__ == "__main__":
    asyncio.run(main())
