@echo off
REM تشغيل Ta9affi مع نظام إدارة Rate Limiting للأدمن

echo ========================================
echo 🛡️ Ta9affi - نظام إدارة Rate Limiting
echo ========================================
echo.

echo 🔍 فحص المتطلبات...

REM فحص Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    echo يرجى تثبيت Python من: https://www.python.org/downloads/
    pause
    exit /b 1
)
echo ✅ Python متاح

REM فحص الملفات المطلوبة
set "required_files=app.py rate_limiter.py rate_limit_monitor.py rate_limit_settings.py admin_rate_limit_manager.py"

for %%f in (%required_files%) do (
    if not exist "%%f" (
        echo ❌ ملف %%f غير موجود
        pause
        exit /b 1
    )
)
echo ✅ جميع الملفات الأساسية موجودة

REM فحص قوالب HTML
if not exist "templates\admin\rate_limit_manager.html" (
    echo ❌ قالب rate_limit_manager.html غير موجود
    pause
    exit /b 1
)
if not exist "templates\admin\user_rate_limit_override.html" (
    echo ❌ قالب user_rate_limit_override.html غير موجود
    pause
    exit /b 1
)
echo ✅ جميع القوالب موجودة

echo.
echo 🧪 اختبار النظام...

REM تشغيل اختبار سريع
python test_admin_rate_limiting.py
if errorlevel 1 (
    echo ⚠️ هناك مشاكل في النظام - لكن سنحاول المتابعة
    pause
)

echo.
echo 📊 إعداد قاعدة البيانات...

REM إنشاء جداول قاعدة البيانات
python create_rate_limit_tables.py
if errorlevel 1 (
    echo ❌ فشل في إعداد قاعدة البيانات
    pause
    exit /b 1
)

echo.
echo 📖 معلومات النظام:
echo ================================
echo 🎯 ميزات نظام إدارة Rate Limiting:
echo    ✅ تحكم كامل في عدد الإضافات (1-100)
echo    ✅ تحكم كامل في عدد الحذف (1-50)
echo    ✅ تحكم في النافذة الزمنية (1-168 ساعة)
echo    ✅ تخصيص حدود فردية للمستخدمين
echo    ✅ سجل شامل لجميع التغييرات
echo    ✅ إحصائيات مباشرة
echo.
echo 🎛️ كيفية الوصول للوحة الإدارة:
echo    1. سجل دخول كأدمن
echo    2. اذهب إلى لوحة تحكم الأدمن
echo    3. ابحث عن "إدارة حدود النظام"
echo    4. أو اذهب إلى: /admin/rate-limit-manager/
echo.
echo 📋 الإعدادات الافتراضية:
echo    - إضافة التقدمات: 10 كل 12 ساعة
echo    - حذف التقدمات: 3 كل 12 ساعة
echo    - يمكن تغييرها من لوحة الإدارة
echo ================================

echo.
echo 🚀 بدء تشغيل Ta9affi...
echo يمكنك الوصول للتطبيق على: http://localhost:5000
echo لوحة إدارة Rate Limiting: http://localhost:5000/admin/rate-limit-manager/
echo للإيقاف: اضغط Ctrl+C
echo.

REM تشغيل التطبيق
python app.py

echo.
echo 👋 تم إيقاف التطبيق
pause
