version: '3.8'

services:
  ta9affi:
    build: .
    ports:
      - "0.0.0.0:80:8000"      # IPv4 للمنفذ 80 → 8000 داخلي
      - "0.0.0.0:8080:8000"    # IPv4 للمنفذ 8080 → 8000 داخلي
    environment:
      - REDIS_URL=redis://redis:6379/0
      - PORT=8000              # المنفذ الجديد
      - FLASK_ENV=production
    depends_on:
      - redis
    networks:
      - ta9affi_network
    restart: unless-stopped
    command: gunicorn --config gunicorn.conf.py app:app

  redis:
    image: redis:7-alpine
    container_name: ta9affi_redis
    restart: unless-stopped
    command: redis-server --maxmemory 256mb --maxmemory-policy allkeys-lru
    networks:
      - ta9affi_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  ta9affi_network:
    driver: bridge
    enable_ipv6: false








