-- إضافة فهارس الأداء لتطبيق Ta9affi
-- تحسين الأداء للمستخدمين المتزامنين

-- ===== فهارس جدول المستخدمين =====

-- فهرس للبحث بالاسم (يدعم LIKE و ILIKE)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_username_search 
ON "user" (username text_pattern_ops);

-- فهرس للبحث برقم الهاتف
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_phone_number 
ON "user" (phone_number) WHERE phone_number IS NOT NULL;

-- فهرس مركب للدور والحالة (للفلترة السريعة)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_role_active 
ON "user" (role, _is_active);

-- فهرس للولاية والدور (للإحصائيات الجغرافية)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_wilaya_role 
ON "user" (wilaya_code, role) WHERE wilaya_code IS NOT NULL;

-- فهرس لآخر تسجيل دخول (للحسابات المهجورة)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_last_login 
ON "user" (last_login) WHERE last_login IS NOT NULL;

-- فهرس لتاريخ الإنشاء (للترتيب الزمني)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_created_at 
ON "user" (created_at);

-- فهرس لآخر نشاط
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_last_activity 
ON "user" (last_activity) WHERE last_activity IS NOT NULL;

-- ===== فهارس جدول البيانات التعليمية =====

-- فهرس مركب محسن للاستعلامات الشائعة
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_level_data_entry_optimized 
ON level_data_entry (database_id, entry_type, is_active, parent_id);

-- فهرس للبحث بالاسم
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_level_data_entry_name_search 
ON level_data_entry (name text_pattern_ops);

-- فهرس للترتيب
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_level_data_entry_order 
ON level_data_entry (database_id, entry_type, order_num, is_active);

-- فهرس للنوع والحالة فقط (للإحصائيات السريعة)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_level_data_entry_type_active 
ON level_data_entry (entry_type, is_active);

-- فهرس للعنصر الأب (للتسلسل الهرمي)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_level_data_entry_parent 
ON level_data_entry (parent_id) WHERE parent_id IS NOT NULL;

-- ===== فهارس جدول التقدم =====

-- فهرس مركب للمستخدم والتاريخ (للاستعلامات الزمنية)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_progress_entry_user_date 
ON progress_entry (user_id, date DESC);

-- فهرس للمستوى والمادة (للتقارير التعليمية)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_progress_entry_level_subject 
ON progress_entry (level_id, subject_id) 
WHERE level_id IS NOT NULL AND subject_id IS NOT NULL;

-- فهرس للحالة والتاريخ (للإحصائيات)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_progress_entry_status_date 
ON progress_entry (status, date DESC);

-- فهرس للمستخدم والحالة (للتقدم الشخصي)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_progress_entry_user_status 
ON progress_entry (user_id, status);

-- فهرس للمادة المعرفية (للتتبع التفصيلي)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_progress_entry_material 
ON progress_entry (material_id) WHERE material_id IS NOT NULL;

-- ===== فهارس جدول الجداول الدراسية =====

-- فهرس للمستخدم والمستوى
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_schedule_user_level 
ON schedule (user_id, level_id);

-- فهرس للمستوى فقط (للإحصائيات)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_schedule_level 
ON schedule (level_id);

-- ===== فهارس جدول الإشعارات الإدارية =====

-- فهرس للمستقبل وحالة القراءة والتاريخ
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_admin_inspector_notification_receiver_read 
ON admin_inspector_notification (receiver_id, is_read, created_at DESC);

-- فهرس للمرسل والتاريخ
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_admin_inspector_notification_sender 
ON admin_inspector_notification (sender_id, created_at DESC);

-- ===== فهارس جدول إشعارات المفتشين والأساتذة =====

-- فهرس للمستقبل وحالة القراءة والتاريخ
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_inspector_teacher_notification_receiver_read 
ON inspector_teacher_notification (receiver_id, is_read, created_at DESC);

-- فهرس للمرسل والتاريخ
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_inspector_teacher_notification_sender 
ON inspector_teacher_notification (sender_id, created_at DESC);

-- ===== فهارس جدول الإشعارات العامة =====

-- فهرس للنوع والدور والتاريخ
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_general_notification_target 
ON general_notification (target_type, target_role, created_at DESC);

-- فهرس للتاريخ فقط (للترتيب)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_general_notification_date 
ON general_notification (created_at DESC);

-- ===== فهارس جدول قراءة الإشعارات العامة =====

-- فهرس مركب للمستخدم والإشعار
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_general_notification_read_user_notification 
ON general_notification_read (user_id, notification_id);

-- فهرس للإشعار فقط
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_general_notification_read_notification 
ON general_notification_read (notification_id);

-- ===== فهارس جدول الاشتراكات =====

-- فهرس للمستخدم والحالة وتاريخ الانتهاء
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_subscription_user_active 
ON subscription (user_id, is_active, end_date DESC);

-- فهرس لتاريخ الانتهاء (للاشتراكات النشطة)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_subscription_end_date 
ON subscription (end_date) WHERE is_active = true;

-- فهرس لتاريخ البداية
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_subscription_start_date 
ON subscription (start_date);

-- فهرس للباقة
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_subscription_plan 
ON subscription (plan_id);

-- ===== فهارس جدول المدفوعات =====

-- فهرس للمستخدم والحالة والتاريخ
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payment_user_status 
ON payment (user_id, status, created_at DESC);

-- فهرس للحالة والتاريخ (للإحصائيات)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payment_status_date 
ON payment (status, created_at DESC);

-- فهرس للمبلغ والعملة
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payment_amount 
ON payment (amount, currency);

-- فهرس لمعرف الدفع الخارجي
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payment_external_id 
ON payment (chargily_payment_id) WHERE chargily_payment_id IS NOT NULL;

-- ===== فهارس جدول الجلسات =====

-- فهرس للمستخدم والحالة وآخر نشاط
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_session_user_active 
ON user_session (user_id, is_active, last_activity DESC);

-- فهرس لآخر نشاط (للجلسات النشطة)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_session_last_activity 
ON user_session (last_activity DESC) WHERE is_active = true;

-- فهرس لتاريخ تسجيل الدخول
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_session_login_time 
ON user_session (login_time DESC);

-- فهرس لعنوان IP (للأمان)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_session_ip 
ON user_session (ip_address) WHERE ip_address IS NOT NULL;

-- ===== فهارس جدول الأخبار =====

-- فهرس للحالة والتاريخ
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_news_update_active_date 
ON news_update (is_active, created_at DESC);

-- فهرس لتاريخ الانتهاء
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_news_update_end_date 
ON news_update (end_date) WHERE end_date IS NOT NULL;

-- فهرس للأولوية والتاريخ
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_news_update_priority 
ON news_update (priority DESC, created_at DESC) WHERE is_active = true;

-- ===== فهارس جدول قواعد البيانات للمستويات =====

-- فهرس للمستوى والحالة
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_level_database_level_active 
ON level_database (level_id, is_active);

-- ===== فهارس جدول المستويات التعليمية =====

-- فهرس للحالة والترتيب
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_educational_level_active_order 
ON educational_level (is_active, order_num);

-- ===== فهارس جدول العلاقات =====

-- فهارس لجدول العلاقة بين المفتشين والأساتذة
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_inspector_teacher_inspector 
ON inspector_teacher (inspector_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_inspector_teacher_teacher 
ON inspector_teacher (teacher_id);

-- فهرس مركب للعلاقة الكاملة
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_inspector_teacher_both 
ON inspector_teacher (inspector_id, teacher_id);

-- ===== فهارس جدول إعدادات الأدوار =====

-- فهرس للحالة والدور
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_role_settings_enabled 
ON role_settings (is_enabled, role_name);

-- ===== فهارس جدول باقات الاشتراك =====

-- فهرس للحالة والسعر
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_subscription_plan_active_price 
ON subscription_plan (is_active, price);

-- ===== فهارس إضافية للأداء =====

-- فهرس للبحث النصي المتقدم (إذا كان PostgreSQL يدعم full-text search)
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_fulltext_search 
-- ON "user" USING gin(to_tsvector('arabic', username || ' ' || COALESCE(phone_number, '')));

-- فهرس للبحث في البيانات التعليمية
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_level_data_entry_fulltext 
-- ON level_data_entry USING gin(to_tsvector('arabic', name || ' ' || COALESCE(description, '')));

-- ===== تحليل الجداول بعد إضافة الفهارس =====

ANALYZE "user";
ANALYZE level_data_entry;
ANALYZE progress_entry;
ANALYZE schedule;
ANALYZE admin_inspector_notification;
ANALYZE inspector_teacher_notification;
ANALYZE general_notification;
ANALYZE general_notification_read;
ANALYZE subscription;
ANALYZE payment;
ANALYZE user_session;
ANALYZE news_update;
ANALYZE level_database;
ANALYZE educational_level;
ANALYZE inspector_teacher;
ANALYZE role_settings;
ANALYZE subscription_plan;

-- رسالة النجاح
SELECT 'تم إنشاء جميع الفهارس بنجاح! 🎉' AS message;
