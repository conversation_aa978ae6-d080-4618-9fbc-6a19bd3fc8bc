#!/usr/bin/env python3
"""
تشخيص مشاكل SSL/HTTPS لـ ta9affi.com
"""

import ssl
import socket
import subprocess
import requests
from datetime import datetime
import json

def print_header(title):
    """طباعة عنوان مع تنسيق"""
    print("\n" + "="*60)
    print(f"🔍 {title}")
    print("="*60)

def check_ssl_certificate():
    """فحص شهادة SSL"""
    print_header("فحص شهادة SSL")
    
    try:
        # الاتصال بالخادم وفحص الشهادة
        context = ssl.create_default_context()
        
        with socket.create_connection(('ta9affi.com', 443), timeout=10) as sock:
            with context.wrap_socket(sock, server_hostname='ta9affi.com') as ssock:
                cert = ssock.getpeercert()
                
                print("✅ تم العثور على شهادة SSL:")
                print(f"   📋 الموضوع: {cert.get('subject', 'غير محدد')}")
                print(f"   🏢 المُصدر: {cert.get('issuer', 'غير محدد')}")
                print(f"   📅 صالحة من: {cert.get('notBefore', 'غير محدد')}")
                print(f"   📅 صالحة حتى: {cert.get('notAfter', 'غير محدد')}")
                
                # فحص أسماء النطاقات المدعومة
                san = cert.get('subjectAltName', [])
                if san:
                    print(f"   🌐 النطاقات المدعومة:")
                    for name_type, name in san:
                        print(f"      - {name}")
                
                return True
                
    except ssl.SSLError as e:
        print(f"❌ خطأ في SSL: {str(e)}")
        return False
    except socket.timeout:
        print("⏰ انتهت مهلة الاتصال بالمنفذ 443")
        return False
    except ConnectionRefusedError:
        print("❌ تم رفض الاتصال بالمنفذ 443")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {str(e)}")
        return False

def check_dns_resolution():
    """فحص حل أسماء النطاقات"""
    print_header("فحص DNS")
    
    domains = ['ta9affi.com', 'www.ta9affi.com']
    
    for domain in domains:
        try:
            import socket
            ip = socket.gethostbyname(domain)
            print(f"✅ {domain} → {ip}")
        except socket.gaierror:
            print(f"❌ {domain} → فشل في حل النطاق")

def check_port_443():
    """فحص المنفذ 443"""
    print_header("فحص المنفذ 443")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        result = sock.connect_ex(('ta9affi.com', 443))
        sock.close()
        
        if result == 0:
            print("✅ المنفذ 443 مفتوح ومتاح")
            return True
        else:
            print("❌ المنفذ 443 مغلق أو غير متاح")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في فحص المنفذ 443: {str(e)}")
        return False

def test_https_requests():
    """اختبار طلبات HTTPS"""
    print_header("اختبار طلبات HTTPS")
    
    urls = [
        'https://ta9affi.com',
        'https://ta9affi.com/health',
        'https://www.ta9affi.com'
    ]
    
    for url in urls:
        try:
            print(f"🔍 اختبار: {url}")
            response = requests.get(url, timeout=15, verify=True)
            print(f"   ✅ الحالة: {response.status_code}")
            print(f"   📏 الحجم: {len(response.content)} بايت")
            
            # فحص إعادة التوجيه
            if response.history:
                print(f"   🔄 إعادة توجيه من: {response.history[0].url}")
                
        except requests.exceptions.SSLError as e:
            print(f"   ❌ خطأ SSL: {str(e)}")
        except requests.exceptions.ConnectionError as e:
            print(f"   ❌ خطأ اتصال: {str(e)}")
        except requests.exceptions.Timeout:
            print(f"   ⏰ انتهت المهلة")
        except Exception as e:
            print(f"   ❌ خطأ عام: {str(e)}")

def check_http_to_https_redirect():
    """فحص إعادة التوجيه من HTTP إلى HTTPS"""
    print_header("فحص إعادة التوجيه HTTP → HTTPS")
    
    try:
        response = requests.get('http://ta9affi.com', allow_redirects=False, timeout=10)
        
        if response.status_code in [301, 302, 307, 308]:
            location = response.headers.get('Location', '')
            if location.startswith('https://'):
                print(f"✅ إعادة توجيه صحيحة: {response.status_code} → {location}")
            else:
                print(f"⚠️ إعادة توجيه غير صحيحة: {response.status_code} → {location}")
        else:
            print(f"❌ لا توجد إعادة توجيه: {response.status_code}")
            
    except Exception as e:
        print(f"❌ خطأ في فحص إعادة التوجيه: {str(e)}")

def check_dokploy_configuration():
    """فحص إعدادات Dokploy"""
    print_header("نصائح لإعدادات Dokploy")
    
    print("🔧 تحقق من الإعدادات التالية في Dokploy:")
    print()
    print("1. 📋 Domain Configuration:")
    print("   ✅ Domain: ta9affi.com")
    print("   ✅ Path: /")
    print("   ✅ Port: 80 (للتطبيق الداخلي)")
    print("   ✅ HTTPS: مفعل")
    print("   ✅ Certificate: Let's Encrypt")
    print()
    print("2. 🔒 SSL Certificate:")
    print("   - تأكد من أن الشهادة صالحة وغير منتهية الصلاحية")
    print("   - تأكد من أن النطاق ta9affi.com مطابق للشهادة")
    print()
    print("3. 🌐 Reverse Proxy:")
    print("   - تأكد من أن Dokploy يوجه HTTPS إلى HTTP داخلياً")
    print("   - المنفذ الداخلي يجب أن يكون 80 (حيث يعمل التطبيق)")
    print()
    print("4. 🔥 Firewall:")
    print("   - تأكد من فتح المنفذ 443 للـ HTTPS")
    print("   - تأكد من فتح المنفذ 80 للـ HTTP")

def generate_fix_suggestions():
    """اقتراح حلول"""
    print_header("اقتراحات الحلول")
    
    print("🔧 خطوات الحل المقترحة:")
    print()
    print("1. 🔍 فحص إعدادات Dokploy:")
    print("   - تأكد من أن HTTPS مفعل بشكل صحيح")
    print("   - تأكد من أن الشهادة صالحة")
    print("   - تأكد من أن المنفذ الداخلي هو 80")
    print()
    print("2. 🔄 إعادة إنشاء الشهادة:")
    print("   - احذف الشهادة الحالية")
    print("   - أنشئ شهادة جديدة من Let's Encrypt")
    print()
    print("3. 🌐 فحص DNS:")
    print("   - تأكد من أن ta9affi.com يشير إلى 82.25.119.242")
    print("   - تأكد من عدم وجود تعارض في DNS")
    print()
    print("4. 🔥 فحص Firewall:")
    print("   sudo ufw allow 443")
    print("   sudo ufw status")
    print()
    print("5. 🔄 إعادة تشغيل الخدمات:")
    print("   - أعد تشغيل Dokploy")
    print("   - أعد تشغيل Nginx (إذا كان مستخدماً)")
    print()
    print("6. 📋 فحص سجلات Dokploy:")
    print("   - ابحث عن أخطاء SSL في السجلات")
    print("   - ابحث عن أخطاء Reverse Proxy")

def main():
    """الدالة الرئيسية"""
    print("🔒 تشخيص مشاكل SSL/HTTPS لـ ta9affi.com")
    print(f"⏰ الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # تشغيل جميع الفحوصات
    check_dns_resolution()
    check_port_443()
    check_ssl_certificate()
    test_https_requests()
    check_http_to_https_redirect()
    check_dokploy_configuration()
    generate_fix_suggestions()
    
    print("\n" + "="*60)
    print("✅ انتهى تشخيص SSL")
    print("💡 إذا استمرت المشكلة، تحقق من إعدادات Dokploy")
    print("="*60)

if __name__ == '__main__':
    main()
