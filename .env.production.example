# ملف البيئة للإنتاج - Ta9affi
# انسخ هذا الملف إلى .env.production وقم بتعديل القيم

# ===== إعدادات التطبيق الأساسية =====
FLASK_ENV=production
SECRET_KEY=your-super-secret-key-here-change-this-in-production
APP_NAME=Ta9affi
APP_VERSION=1.0.0
DEBUG=False

# ===== إعدادات قاعدة البيانات =====
DB_HOST=postgres
DB_PORT=5432
DB_NAME=ta9affi
DB_USER=ta9affi_user
DB_PASSWORD=your-strong-database-password-here

# رابط قاعدة البيانات الكامل
DATABASE_URL=**************************************************************************/ta9affi

# ===== إعدادات Redis =====
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=
REDIS_URL=redis://redis:6379/0

# ===== إعدادات البريد الإلكتروني =====
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USE_SSL=False
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_DEFAULT_SENDER=<EMAIL>

# إعدادات البريد للتنبيهات
ALERT_FROM_EMAIL=<EMAIL>
ALERT_TO_EMAILS=<EMAIL>,<EMAIL>

# ===== إعدادات الأمان =====
# مفتاح تشفير إضافي
ENCRYPTION_KEY=your-encryption-key-32-characters

# إعدادات CSRF
WTF_CSRF_ENABLED=True
WTF_CSRF_TIME_LIMIT=3600

# إعدادات الجلسات
SESSION_TIMEOUT=3600
PERMANENT_SESSION_LIFETIME=86400

# إعدادات كلمات المرور
PASSWORD_MIN_LENGTH=8
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=1800

# ===== إعدادات التخزين السحابي (AWS) =====
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_BACKUP_BUCKET=ta9affi-backups

# إعدادات S3 للملفات
AWS_S3_BUCKET=ta9affi-files
AWS_S3_REGION=us-east-1

# ===== إعدادات النسخ الاحتياطي =====
BACKUP_DIR=backups
MAX_LOCAL_BACKUPS=7
MAX_REMOTE_BACKUPS=30
BACKUP_CLOUD_PROVIDER=aws

# ===== إعدادات المراقبة =====
# Grafana
GRAFANA_PASSWORD=your-grafana-admin-password

# Prometheus
PROMETHEUS_RETENTION=200h

# إعدادات التنبيهات
ENABLE_MONITORING=True
MONITORING_INTERVAL=60

# ===== إعدادات الأداء =====
# Gunicorn
WORKERS=4
WORKER_CLASS=gevent
WORKER_CONNECTIONS=1000
MAX_REQUESTS=1000
TIMEOUT=30

# Redis Cache
CACHE_TYPE=redis
CACHE_DEFAULT_TIMEOUT=300
CACHE_KEY_PREFIX=ta9affi:

# ===== إعدادات الملفات =====
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=52428800  # 50MB
ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,pdf,doc,docx,txt

# ===== إعدادات SSL/TLS =====
SSL_DISABLE=False
PREFERRED_URL_SCHEME=https
SSL_CERT_PATH=/etc/nginx/ssl/ta9affi.crt
SSL_KEY_PATH=/etc/nginx/ssl/ta9affi.key

# ===== إعدادات CDN =====
CDN_DOMAIN=cdn.ta9affi.dz
USE_CDN=False

# ===== إعدادات التحليلات =====
GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX
ENABLE_ANALYTICS=True

# ===== إعدادات الشبكة =====
ALLOWED_HOSTS=ta9affi.dz,www.ta9affi.dz,localhost
CORS_ORIGINS=https://ta9affi.dz,https://www.ta9affi.dz

# ===== إعدادات Rate Limiting =====
RATELIMIT_STORAGE_URL=redis://redis:6379/1
RATELIMIT_DEFAULT=1000 per hour

# ===== إعدادات Celery =====
CELERY_BROKER_URL=redis://redis:6379/2
CELERY_RESULT_BACKEND=redis://redis:6379/3
CELERY_TASK_SERIALIZER=json
CELERY_ACCEPT_CONTENT=json
CELERY_RESULT_SERIALIZER=json
CELERY_TIMEZONE=Africa/Algiers

# ===== إعدادات السجلات =====
LOG_LEVEL=INFO
LOG_FILE=logs/ta9affi.log
LOG_MAX_BYTES=10485760  # 10MB
LOG_BACKUP_COUNT=5

# إعدادات Elasticsearch للسجلات
ELASTICSEARCH_URL=http://elasticsearch:9200
ENABLE_ELASTICSEARCH_LOGGING=True

# ===== إعدادات التطوير والاختبار =====
# (يتم تجاهلها في الإنتاج)
TESTING=False
WTF_CSRF_ENABLED=True

# ===== إعدادات قاعدة البيانات المتقدمة =====
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600

# ===== إعدادات الضغط =====
COMPRESS_MIMETYPES=text/html,text/css,text/xml,application/json,application/javascript
COMPRESS_LEVEL=6
COMPRESS_MIN_SIZE=500

# ===== إعدادات الأمان المتقدمة =====
# Content Security Policy
CSP_DEFAULT_SRC='self'
CSP_SCRIPT_SRC='self' 'unsafe-inline' https://cdn.jsdelivr.net
CSP_STYLE_SRC='self' 'unsafe-inline' https://fonts.googleapis.com
CSP_IMG_SRC='self' data: https:
CSP_FONT_SRC='self' https://fonts.gstatic.com

# HSTS
HSTS_MAX_AGE=31536000
HSTS_INCLUDE_SUBDOMAINS=True

# ===== إعدادات التخزين المؤقت =====
CACHE_REDIS_HOST=redis
CACHE_REDIS_PORT=6379
CACHE_REDIS_DB=4
CACHE_REDIS_PASSWORD=

# إعدادات التخزين المؤقت للصفحات
PAGE_CACHE_TIMEOUT=300
API_CACHE_TIMEOUT=60

# ===== إعدادات الإشعارات =====
ENABLE_PUSH_NOTIFICATIONS=True
VAPID_PUBLIC_KEY=your-vapid-public-key
VAPID_PRIVATE_KEY=your-vapid-private-key
VAPID_CLAIMS_EMAIL=<EMAIL>

# ===== إعدادات التكامل مع خدمات خارجية =====
# Google Services
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Facebook
FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret

# ===== إعدادات الصحة والمراقبة =====
HEALTH_CHECK_ENABLED=True
HEALTH_CHECK_PATH=/health
METRICS_ENABLED=True
METRICS_PATH=/metrics

# ===== إعدادات البيئة المحددة =====
ENVIRONMENT=production
DEPLOYMENT_DATE=2024-01-01
VERSION_HASH=commit-hash-here

# ===== إعدادات الشبكة المتقدمة =====
PROXY_COUNT=1
PROXY_HEADERS=X-Forwarded-For,X-Forwarded-Proto,X-Forwarded-Host

# ===== إعدادات التحسين =====
ENABLE_GZIP=True
ENABLE_BROTLI=False
STATIC_FILE_CACHE_TIMEOUT=31536000  # سنة واحدة

# ===== إعدادات المطورين =====
# (للاستخدام في حالات خاصة)
DEVELOPER_MODE=False
MAINTENANCE_MODE=False
FEATURE_FLAGS=new_dashboard:true,beta_features:false

# ===== ملاحظات مهمة =====
# 1. تأكد من تغيير جميع كلمات المرور والمفاتيح السرية
# 2. استخدم مفاتيح قوية ومعقدة
# 3. لا تشارك هذا الملف في نظام التحكم بالإصدارات
# 4. قم بعمل نسخة احتياطية آمنة من هذا الملف
# 5. راجع الإعدادات بانتظام وحدثها حسب الحاجة
