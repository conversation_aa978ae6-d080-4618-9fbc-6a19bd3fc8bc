#!/usr/bin/env python3
"""
سكريپت اختبار Ta9affi محلياً قبل النشر على الخادم
"""

import os
import sys
from dotenv import load_dotenv

def setup_local_test_environment():
    """إعداد بيئة الاختبار المحلي"""
    
    # تحميل إعدادات الاختبار المحلي
    if os.path.exists('.env.local'):
        load_dotenv('.env.local')
        print("✅ تم تحميل إعدادات الاختبار المحلي من .env.local")
    else:
        print("⚠️ ملف .env.local غير موجود، سيتم استخدام إعدادات افتراضية")
        # إعدادات افتراضية للاختبار المحلي
        os.environ['PRODUCTION_MODE'] = 'true'
        os.environ['SERVER_IP'] = 'localhost'
        os.environ['SECRET_KEY'] = 'test-secret-key-local'
        os.environ['DATABASE_URL'] = 'sqlite:///ta9affi_local_test.db'
        os.environ['PORT'] = '8000'
    
    print("🧪 إعداد بيئة الاختبار المحلي:")
    print(f"   - وضع Production: {os.environ.get('PRODUCTION_MODE', 'false')}")
    print(f"   - الخادم: {os.environ.get('SERVER_IP', 'localhost')}")
    print(f"   - المنفذ: {os.environ.get('PORT', '8000')}")
    print(f"   - قاعدة البيانات: {os.environ.get('DATABASE_URL', 'sqlite:///ta9affi_local_test.db')}")
    
    return True

def test_imports():
    """اختبار استيراد المكتبات المطلوبة"""
    print("\n🔍 اختبار استيراد المكتبات...")
    
    required_modules = [
        'flask',
        'flask_sqlalchemy',
        'flask_login',
        'werkzeug',
        'pandas',
        'openpyxl'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"   ✅ {module}")
        except ImportError:
            print(f"   ❌ {module}")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\n❌ مكتبات مفقودة: {', '.join(missing_modules)}")
        print("يرجى تثبيتها باستخدام: pip install -r requirements.txt")
        return False
    
    print("✅ جميع المكتبات المطلوبة متوفرة")
    return True

def test_database_setup():
    """اختبار إعداد قاعدة البيانات"""
    print("\n🗄️ اختبار إعداد قاعدة البيانات...")
    
    try:
        # استيراد التطبيق
        from app import app, db, User, Role
        
        with app.app_context():
            # إنشاء الجداول
            db.create_all()
            print("   ✅ تم إنشاء جداول قاعدة البيانات")
            
            # اختبار الاتصال
            db.session.execute('SELECT 1')
            print("   ✅ اختبار اتصال قاعدة البيانات نجح")
            
            # التحقق من وجود مستخدم أدمن
            admin_count = User.query.filter_by(role=Role.ADMIN).count()
            print(f"   📊 عدد مستخدمي الأدمن: {admin_count}")
            
            return True
            
    except Exception as e:
        print(f"   ❌ خطأ في إعداد قاعدة البيانات: {str(e)}")
        return False

def test_routes():
    """اختبار الصفحات الأساسية"""
    print("\n🌐 اختبار الصفحات الأساسية...")
    
    try:
        from app import app
        
        with app.test_client() as client:
            # اختبار الصفحة الرئيسية
            response = client.get('/')
            print(f"   📄 الصفحة الرئيسية: {response.status_code}")
            
            # اختبار صفحة تسجيل الدخول
            response = client.get('/login')
            print(f"   🔐 صفحة تسجيل الدخول: {response.status_code}")
            
            # اختبار صفحة التسجيل
            response = client.get('/register')
            print(f"   📝 صفحة التسجيل: {response.status_code}")
            
            # اختبار Health Check
            response = client.get('/health')
            print(f"   ❤️ Health Check: {response.status_code}")
            
            if response.status_code == 200:
                data = response.get_json()
                print(f"      - حالة التطبيق: {data.get('status', 'غير معروف')}")
                print(f"      - حالة قاعدة البيانات: {data.get('database', 'غير معروف')}")
            
            return True
            
    except Exception as e:
        print(f"   ❌ خطأ في اختبار الصفحات: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار Ta9affi محلياً")
    print("=" * 50)
    
    # إعداد البيئة
    if not setup_local_test_environment():
        sys.exit(1)
    
    # اختبار المكتبات
    if not test_imports():
        sys.exit(1)
    
    # اختبار قاعدة البيانات
    if not test_database_setup():
        sys.exit(1)
    
    # اختبار الصفحات
    if not test_routes():
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("✅ جميع الاختبارات نجحت!")
    print("🚀 التطبيق جاهز للتشغيل محلياً")
    print("\nلتشغيل التطبيق:")
    print("   python app.py")
    print(f"\nأو زيارة: http://localhost:{os.environ.get('PORT', '8000')}")

if __name__ == '__main__':
    main()
