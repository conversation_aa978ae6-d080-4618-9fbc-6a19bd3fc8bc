#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
استعلامات محسنة لتطبيق Ta9affi
تحسين الاستعلامات الثقيلة لدعم آلاف المستخدمين المتزامنين
"""

from sqlalchemy import text, and_, or_, func, case, distinct
from sqlalchemy.orm import joinedload, selectinload, contains_eager
from models_new import db, User, Role, LevelDataEntry, ProgressEntry, Schedule, EducationalLevel, LevelDatabase
from models_new import GeneralNotification, GeneralNotificationRead, UserSession, AdminInspectorNotification, InspectorTeacherNotification
from cache_manager import cache_manager
from datetime import datetime, timedelta

class OptimizedQueries:
    """فئة الاستعلامات المحسنة"""
    
    @staticmethod
    @cache_manager.cached(timeout=1800, key_prefix="users_search")
    def search_users_optimized(search_term="", role_filter="", wilaya_filter="", status_filter="", page=1, per_page=20):
        """بحث محسن في المستخدمين مع التخزين المؤقت"""
        
        # بناء الاستعلام الأساسي
        query = db.session.query(User)
        
        # شروط البحث
        conditions = []
        
        if search_term:
            # استخدام ILIKE للبحث غير الحساس للحالة
            search_condition = or_(
                User.username.ilike(f'%{search_term}%'),
                User.phone_number.ilike(f'%{search_term}%')
            )
            conditions.append(search_condition)
        
        if role_filter:
            conditions.append(User.role == role_filter)
        
        if wilaya_filter:
            conditions.append(User.wilaya_code == wilaya_filter)
        
        if status_filter == 'active':
            conditions.append(User._is_active == True)
        elif status_filter == 'inactive':
            conditions.append(User._is_active == False)
        
        # تطبيق الشروط
        if conditions:
            query = query.filter(and_(*conditions))
        
        # ترتيب محسن باستخدام CASE
        priority_order = case(
            (User.role == Role.ADMIN, 1),
            (User.role == Role.USER_MANAGER, 2),
            else_=3
        )
        query = query.order_by(priority_order, User.username)
        
        # تقسيم الصفحات
        return query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
    
    @staticmethod
    @cache_manager.cached(timeout=3600, key_prefix="user_stats")
    def get_user_statistics():
        """إحصائيات المستخدمين المحسنة"""
        
        # استعلام واحد للحصول على جميع الإحصائيات
        stats_query = db.session.query(
            func.count(User.id).label('total'),
            func.sum(case((User._is_active == True, 1), else_=0)).label('active'),
            func.sum(case((User.role == Role.TEACHER, 1), else_=0)).label('teachers'),
            func.sum(case((User.role == Role.INSPECTOR, 1), else_=0)).label('inspectors'),
            func.sum(case((User.role == Role.ADMIN, 1), else_=0)).label('admins'),
            func.sum(case((User.role == Role.USER_MANAGER, 1), else_=0)).label('user_managers')
        ).first()
        
        # عدد المستخدمين المتصلين (من Redis أو قاعدة البيانات)
        online_count = UserSession.get_online_users_count()
        
        return {
            'total': stats_query.total or 0,
            'active': stats_query.active or 0,
            'inactive': (stats_query.total or 0) - (stats_query.active or 0),
            'teachers': stats_query.teachers or 0,
            'inspectors': stats_query.inspectors or 0,
            'admins': stats_query.admins or 0,
            'user_managers': stats_query.user_managers or 0,
            'online': online_count
        }
    
    @staticmethod
    @cache_manager.cached(timeout=1800, key_prefix="educational_data")
    def get_educational_data_optimized(database_id, entry_type=None, parent_id=None, is_active=True):
        """الحصول على البيانات التعليمية بشكل محسن"""
        
        query = db.session.query(LevelDataEntry).filter(
            LevelDataEntry.database_id == database_id
        )
        
        if entry_type:
            query = query.filter(LevelDataEntry.entry_type == entry_type)
        
        if parent_id is not None:
            query = query.filter(LevelDataEntry.parent_id == parent_id)
        
        if is_active is not None:
            query = query.filter(LevelDataEntry.is_active == is_active)
        
        # ترتيب محسن
        return query.order_by(LevelDataEntry.order_num, LevelDataEntry.name).all()
    
    @staticmethod
    @cache_manager.cached(timeout=900, key_prefix="progress_stats")
    def get_progress_statistics_optimized(user_id, level_id=None):
        """إحصائيات التقدم المحسنة"""
        
        # الحصول على جداول المستخدم
        schedules_query = db.session.query(Schedule).filter(Schedule.user_id == user_id)
        if level_id:
            schedules_query = schedules_query.filter(Schedule.level_id == level_id)
        
        schedules = schedules_query.all()
        
        if not schedules:
            # إذا لم يكن للمستخدم جدول، استخدم جميع المستويات
            level_ids = [level.id for level in EducationalLevel.query.filter_by(is_active=True).all()]
        else:
            level_ids = list(set([schedule.level_id for schedule in schedules]))
        
        # استعلام محسن للتقدم
        progress_stats = db.session.query(
            func.count(ProgressEntry.id).label('total_entries'),
            func.sum(case((ProgressEntry.status == 'completed', 1), else_=0)).label('completed'),
            func.sum(case((ProgressEntry.status == 'in_progress', 1), else_=0)).label('in_progress'),
            func.sum(case((ProgressEntry.status == 'planned', 1), else_=0)).label('planned')
        ).filter(
            ProgressEntry.user_id == user_id,
            ProgressEntry.level_id.in_(level_ids) if level_ids else True
        ).first()
        
        return {
            'total_entries': progress_stats.total_entries or 0,
            'completed': progress_stats.completed or 0,
            'in_progress': progress_stats.in_progress or 0,
            'planned': progress_stats.planned or 0,
            'completion_rate': round((progress_stats.completed or 0) / max(progress_stats.total_entries or 1, 1) * 100, 2)
        }
    
    @staticmethod
    @cache_manager.cached(timeout=600, key_prefix="notifications")
    def get_unread_notifications_optimized(user_id):
        """حساب الإشعارات غير المقروءة بشكل محسن"""
        
        user = User.query.get(user_id)
        if not user:
            return 0
        
        # إشعارات مباشرة
        direct_unread = 0
        
        if user.role == Role.INSPECTOR:
            direct_unread = AdminInspectorNotification.query.filter_by(
                receiver_id=user_id, is_read=False
            ).count()
        elif user.role == Role.TEACHER:
            direct_unread = InspectorTeacherNotification.query.filter_by(
                receiver_id=user_id, is_read=False
            ).count()
        
        # إشعارات عامة غير مقروءة (استعلام محسن)
        read_notification_ids = db.session.query(GeneralNotificationRead.notification_id).filter(
            GeneralNotificationRead.user_id == user_id
        ).subquery()
        
        general_unread = GeneralNotification.query.filter(
            or_(
                GeneralNotification.target_type == 'all',
                and_(
                    GeneralNotification.target_type == 'role',
                    GeneralNotification.target_role == user.role
                )
            ),
            ~GeneralNotification.id.in_(read_notification_ids)
        ).count()
        
        return direct_unread + general_unread
    
    @staticmethod
    @cache_manager.cached(timeout=300, key_prefix="supervised_teachers")
    def get_supervised_teachers_optimized(inspector_id):
        """الحصول على الأساتذة المشرف عليهم بشكل محسن"""
        
        # استعلام محسن مع JOIN
        teachers = db.session.query(User).join(
            db.text('inspector_teacher'),
            User.id == db.text('inspector_teacher.teacher_id')
        ).filter(
            db.text('inspector_teacher.inspector_id') == inspector_id,
            User.role == Role.TEACHER,
            User._is_active == True
        ).options(
            # تحميل البيانات المطلوبة مسبقاً
            selectinload(User.schedules),
            selectinload(User.progress_entries)
        ).all()
        
        return teachers
    
    @staticmethod
    @cache_manager.cached(timeout=1800, key_prefix="level_database_stats")
    def get_level_database_statistics(database_id):
        """إحصائيات قاعدة البيانات للمستوى"""
        
        # استعلام واحد للحصول على جميع الإحصائيات
        stats = db.session.query(
            func.sum(case((LevelDataEntry.entry_type == 'subject', 1), else_=0)).label('subjects_count'),
            func.sum(case((LevelDataEntry.entry_type == 'domain', 1), else_=0)).label('domains_count'),
            func.sum(case((LevelDataEntry.entry_type == 'material', 1), else_=0)).label('materials_count'),
            func.sum(case((LevelDataEntry.entry_type == 'competency', 1), else_=0)).label('competencies_count'),
            func.sum(case((and_(LevelDataEntry.entry_type == 'subject', LevelDataEntry.is_active == True), 1), else_=0)).label('subjects_active'),
            func.sum(case((and_(LevelDataEntry.entry_type == 'domain', LevelDataEntry.is_active == True), 1), else_=0)).label('domains_active'),
            func.sum(case((and_(LevelDataEntry.entry_type == 'material', LevelDataEntry.is_active == True), 1), else_=0)).label('materials_active'),
            func.sum(case((and_(LevelDataEntry.entry_type == 'competency', LevelDataEntry.is_active == True), 1), else_=0)).label('competencies_active')
        ).filter(LevelDataEntry.database_id == database_id).first()
        
        return {
            'subjects_count': stats.subjects_count or 0,
            'domains_count': stats.domains_count or 0,
            'materials_count': stats.materials_count or 0,
            'competencies_count': stats.competencies_count or 0,
            'subjects_active': stats.subjects_active or 0,
            'domains_active': stats.domains_active or 0,
            'materials_active': stats.materials_active or 0,
            'competencies_active': stats.competencies_active or 0
        }
    
    @staticmethod
    @cache_manager.cached(timeout=3600, key_prefix="abandoned_accounts")
    def get_abandoned_accounts_optimized(days=180):
        """الحصول على الحسابات المهجورة بشكل محسن"""
        
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        # استعلام محسن للحسابات المهجورة
        abandoned_accounts = User.query.filter(
            or_(
                User.last_login.is_(None),  # لم يسجل دخول أبداً
                User.last_login < cutoff_date  # آخر تسجيل دخول قديم
            ),
            User.role.in_([Role.TEACHER, Role.INSPECTOR, Role.USER_MANAGER])  # استثناء الأدمن
        ).order_by(User.created_at.desc()).all()
        
        return abandoned_accounts
    
    @staticmethod
    def get_recent_progress_optimized(user_id, limit=10):
        """الحصول على آخر التقدمات بشكل محسن"""
        
        return ProgressEntry.query.filter_by(user_id=user_id).options(
            joinedload(ProgressEntry.level),
            joinedload(ProgressEntry.subject),
            joinedload(ProgressEntry.domain),
            joinedload(ProgressEntry.material)
        ).order_by(ProgressEntry.date.desc(), ProgressEntry.created_at.desc()).limit(limit).all()
    
    @staticmethod
    @cache_manager.cached(timeout=600, key_prefix="online_users")
    def get_online_users_count_optimized():
        """حساب المستخدمين المتصلين بشكل محسن"""
        
        # محاولة استخدام Redis أولاً
        try:
            from session_manager import session_manager
            return session_manager.get_online_users_count()
        except:
            # العودة للطريقة التقليدية
            cutoff_time = datetime.utcnow() - timedelta(minutes=30)
            return UserSession.query.filter(
                UserSession.is_active == True,
                UserSession.last_activity > cutoff_time
            ).count()
    
    @staticmethod
    def bulk_update_user_activity(user_ids):
        """تحديث نشاط عدة مستخدمين دفعة واحدة"""
        
        if not user_ids:
            return
        
        # تحديث دفعي محسن
        db.session.execute(
            text("""
                UPDATE "user" 
                SET last_activity = :now 
                WHERE id = ANY(:user_ids)
            """),
            {
                'now': datetime.utcnow(),
                'user_ids': user_ids
            }
        )
        db.session.commit()
    
    @staticmethod
    def cleanup_old_sessions_optimized(days=7):
        """تنظيف الجلسات القديمة بشكل محسن"""
        
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        # حذف دفعي للجلسات القديمة
        deleted_count = UserSession.query.filter(
            or_(
                UserSession.is_active == False,
                UserSession.last_activity < cutoff_date
            )
        ).delete(synchronize_session=False)
        
        db.session.commit()
        return deleted_count

# دوال مساعدة للتخزين المؤقت
def invalidate_user_cache(user_id):
    """إلغاء التخزين المؤقت للمستخدم"""
    cache_manager.invalidate_user_cache(user_id)

def invalidate_educational_cache(level_id=None):
    """إلغاء التخزين المؤقت للبيانات التعليمية"""
    cache_manager.invalidate_educational_cache(level_id)

def warm_up_common_queries():
    """تسخين الاستعلامات الشائعة"""
    try:
        # تسخين إحصائيات المستخدمين
        OptimizedQueries.get_user_statistics()
        
        # تسخين عدد المستخدمين المتصلين
        OptimizedQueries.get_online_users_count_optimized()
        
        # تسخين البيانات التعليمية للمستويات النشطة
        active_levels = EducationalLevel.query.filter_by(is_active=True).all()
        for level in active_levels:
            level_db = LevelDatabase.query.filter_by(level_id=level.id, is_active=True).first()
            if level_db:
                OptimizedQueries.get_level_database_statistics(level_db.id)
        
        print("✅ تم تسخين الاستعلامات الشائعة")
        
    except Exception as e:
        print(f"⚠️ خطأ في تسخين الاستعلامات: {str(e)}")
