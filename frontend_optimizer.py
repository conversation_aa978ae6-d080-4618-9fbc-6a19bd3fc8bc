#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
محسن الواجهة الأمامية لتطبيق Ta9affi
يحسن أداء الواجهة الأمامية بضغط الملفات وتحسين التحميل
"""

import os
import gzip
import shutil
import hashlib
import json
import logging
from pathlib import Path
from flask import Blueprint, request, send_file, current_app, jsonify
from flask_assets import Environment, Bundle
import cssmin
import jsmin
from PIL import Image
import subprocess

class FrontendOptimizer:
    """محسن الواجهة الأمامية"""
    
    def __init__(self, app=None):
        self.app = app
        self.assets = None
        self.optimized_files = {}
        self.cache_manifest = {}
        
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app):
        """تهيئة محسن الواجهة الأمامية"""
        self.app = app
        
        # إعداد Flask-Assets
        self.assets = Environment(app)
        self.assets.url = app.static_url_path
        self.assets.directory = app.static_folder
        
        # إعدادات التحسين
        self.static_folder = Path(app.static_folder)
        self.optimized_folder = self.static_folder / 'optimized'
        self.optimized_folder.mkdir(exist_ok=True)
        
        # إعداد ضغط الملفات
        self.setup_compression()
        
        # إعداد حزم الأصول
        self.setup_asset_bundles()
        
        # تحسين الصور
        self.optimize_images()
        
        # إنشاء manifest للتخزين المؤقت
        self.create_cache_manifest()
        
        logging.info("✅ تم تهيئة محسن الواجهة الأمامية")
    
    def setup_compression(self):
        """إعداد ضغط الملفات"""
        # ضغط ملفات CSS
        self.compress_css_files()
        
        # ضغط ملفات JavaScript
        self.compress_js_files()
        
        # إنشاء ملفات gzip
        self.create_gzip_files()
    
    def compress_css_files(self):
        """ضغط ملفات CSS"""
        css_folder = self.static_folder / 'css'
        if not css_folder.exists():
            return
        
        for css_file in css_folder.glob('*.css'):
            if css_file.name.endswith('.min.css'):
                continue
            
            try:
                with open(css_file, 'r', encoding='utf-8') as f:
                    css_content = f.read()
                
                # ضغط CSS
                compressed_css = cssmin.cssmin(css_content)
                
                # حفظ النسخة المضغوطة
                min_file = css_file.parent / f"{css_file.stem}.min.css"
                with open(min_file, 'w', encoding='utf-8') as f:
                    f.write(compressed_css)
                
                # حساب نسبة الضغط
                original_size = len(css_content)
                compressed_size = len(compressed_css)
                compression_ratio = (1 - compressed_size / original_size) * 100
                
                self.optimized_files[str(css_file)] = {
                    'original_size': original_size,
                    'compressed_size': compressed_size,
                    'compression_ratio': compression_ratio,
                    'min_file': str(min_file)
                }
                
                logging.info(f"ضغط CSS: {css_file.name} ({compression_ratio:.1f}% توفير)")
                
            except Exception as e:
                logging.error(f"خطأ في ضغط {css_file}: {str(e)}")
    
    def compress_js_files(self):
        """ضغط ملفات JavaScript"""
        js_folder = self.static_folder / 'js'
        if not js_folder.exists():
            return
        
        for js_file in js_folder.glob('*.js'):
            if js_file.name.endswith('.min.js'):
                continue
            
            try:
                with open(js_file, 'r', encoding='utf-8') as f:
                    js_content = f.read()
                
                # ضغط JavaScript
                compressed_js = jsmin.jsmin(js_content)
                
                # حفظ النسخة المضغوطة
                min_file = js_file.parent / f"{js_file.stem}.min.js"
                with open(min_file, 'w', encoding='utf-8') as f:
                    f.write(compressed_js)
                
                # حساب نسبة الضغط
                original_size = len(js_content)
                compressed_size = len(compressed_js)
                compression_ratio = (1 - compressed_size / original_size) * 100
                
                self.optimized_files[str(js_file)] = {
                    'original_size': original_size,
                    'compressed_size': compressed_size,
                    'compression_ratio': compression_ratio,
                    'min_file': str(min_file)
                }
                
                logging.info(f"ضغط JS: {js_file.name} ({compression_ratio:.1f}% توفير)")
                
            except Exception as e:
                logging.error(f"خطأ في ضغط {js_file}: {str(e)}")
    
    def create_gzip_files(self):
        """إنشاء ملفات gzip للملفات الثابتة"""
        extensions = ['.css', '.js', '.html', '.json', '.svg']
        
        for ext in extensions:
            for file_path in self.static_folder.rglob(f'*{ext}'):
                if file_path.suffix == ext and not file_path.name.endswith('.gz'):
                    try:
                        gzip_path = file_path.with_suffix(file_path.suffix + '.gz')
                        
                        with open(file_path, 'rb') as f_in:
                            with gzip.open(gzip_path, 'wb') as f_out:
                                shutil.copyfileobj(f_in, f_out)
                        
                        # حساب نسبة الضغط
                        original_size = file_path.stat().st_size
                        compressed_size = gzip_path.stat().st_size
                        compression_ratio = (1 - compressed_size / original_size) * 100
                        
                        logging.info(f"gzip: {file_path.name} ({compression_ratio:.1f}% توفير)")
                        
                    except Exception as e:
                        logging.error(f"خطأ في gzip {file_path}: {str(e)}")
    
    def setup_asset_bundles(self):
        """إعداد حزم الأصول"""
        # حزمة CSS الرئيسية
        css_bundle = Bundle(
            'css/bootstrap.min.css',
            'css/fontawesome.min.css',
            'css/style.css',
            filters='cssmin',
            output='optimized/main.min.css'
        )
        self.assets.register('css_main', css_bundle)
        
        # حزمة JavaScript الرئيسية
        js_bundle = Bundle(
            'js/jquery.min.js',
            'js/bootstrap.min.js',
            'js/app.js',
            filters='jsmin',
            output='optimized/main.min.js'
        )
        self.assets.register('js_main', js_bundle)
        
        # حزمة CSS للوحة التحكم
        admin_css_bundle = Bundle(
            'css/admin.css',
            'css/charts.css',
            filters='cssmin',
            output='optimized/admin.min.css'
        )
        self.assets.register('css_admin', admin_css_bundle)
        
        # حزمة JavaScript للوحة التحكم
        admin_js_bundle = Bundle(
            'js/chart.min.js',
            'js/admin.js',
            filters='jsmin',
            output='optimized/admin.min.js'
        )
        self.assets.register('js_admin', admin_js_bundle)
    
    def optimize_images(self):
        """تحسين الصور"""
        images_folder = self.static_folder / 'images'
        if not images_folder.exists():
            return
        
        optimized_images_folder = self.optimized_folder / 'images'
        optimized_images_folder.mkdir(exist_ok=True)
        
        for image_file in images_folder.glob('*'):
            if image_file.suffix.lower() in ['.jpg', '.jpeg', '.png', '.gif']:
                try:
                    self.optimize_single_image(image_file, optimized_images_folder)
                except Exception as e:
                    logging.error(f"خطأ في تحسين الصورة {image_file}: {str(e)}")
    
    def optimize_single_image(self, image_path, output_folder):
        """تحسين صورة واحدة"""
        with Image.open(image_path) as img:
            # تحويل RGBA إلى RGB للـ JPEG
            if img.mode == 'RGBA' and image_path.suffix.lower() in ['.jpg', '.jpeg']:
                img = img.convert('RGB')
            
            # تحسين الجودة والحجم
            optimized_path = output_folder / image_path.name
            
            # تقليل الحجم إذا كان كبيراً
            max_size = (1920, 1080)
            if img.size[0] > max_size[0] or img.size[1] > max_size[1]:
                img.thumbnail(max_size, Image.Resampling.LANCZOS)
            
            # حفظ بجودة محسنة
            if image_path.suffix.lower() in ['.jpg', '.jpeg']:
                img.save(optimized_path, 'JPEG', quality=85, optimize=True)
            elif image_path.suffix.lower() == '.png':
                img.save(optimized_path, 'PNG', optimize=True)
            else:
                img.save(optimized_path, optimize=True)
            
            # حساب نسبة التوفير
            original_size = image_path.stat().st_size
            optimized_size = optimized_path.stat().st_size
            savings = (1 - optimized_size / original_size) * 100
            
            logging.info(f"تحسين الصورة: {image_path.name} ({savings:.1f}% توفير)")
    
    def create_cache_manifest(self):
        """إنشاء manifest للتخزين المؤقت"""
        manifest = {}
        
        # إضافة الملفات المحسنة
        for file_path in self.optimized_folder.rglob('*'):
            if file_path.is_file():
                # حساب hash للملف
                with open(file_path, 'rb') as f:
                    file_hash = hashlib.md5(f.read()).hexdigest()[:8]
                
                relative_path = file_path.relative_to(self.static_folder)
                manifest[str(relative_path)] = {
                    'hash': file_hash,
                    'size': file_path.stat().st_size,
                    'mtime': file_path.stat().st_mtime
                }
        
        # حفظ manifest
        manifest_path = self.static_folder / 'manifest.json'
        with open(manifest_path, 'w', encoding='utf-8') as f:
            json.dump(manifest, f, indent=2)
        
        self.cache_manifest = manifest
        logging.info(f"تم إنشاء cache manifest مع {len(manifest)} ملف")
    
    def get_optimized_url(self, filename):
        """الحصول على رابط الملف المحسن"""
        # البحث عن الملف في manifest
        if filename in self.cache_manifest:
            file_info = self.cache_manifest[filename]
            return f"/static/{filename}?v={file_info['hash']}"
        
        return f"/static/{filename}"
    
    def create_service_worker(self):
        """إنشاء Service Worker للتخزين المؤقت"""
        import time
        sw_content = f"""
// Service Worker لتطبيق Ta9affi
const CACHE_NAME = 'ta9affi-v{int(time.time())}';
const urlsToCache = [
    '/',
    '/static/optimized/main.min.css',
    '/static/optimized/main.min.js',
    '/static/optimized/admin.min.css',
    '/static/optimized/admin.min.js'
];

// تثبيت Service Worker
self.addEventListener('install', function(event) {{
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(function(cache) {{
                return cache.addAll(urlsToCache);
            }})
    );
}});

// اعتراض الطلبات
self.addEventListener('fetch', function(event) {{
    event.respondWith(
        caches.match(event.request)
            .then(function(response) {{
                // إرجاع النسخة المخزنة إذا وجدت
                if (response) {{
                    return response;
                }}

                // جلب من الشبكة
                return fetch(event.request);
            }})
    );
}});

// تنظيف التخزين المؤقت القديم
self.addEventListener('activate', function(event) {{
    event.waitUntil(
        caches.keys().then(function(cacheNames) {{
            return Promise.all(
                cacheNames.map(function(cacheName) {{
                    if (cacheName !== CACHE_NAME) {{
                        return caches.delete(cacheName);
                    }}
                }})
            );
        }})
    );
}});
"""
        
        sw_path = self.static_folder / 'sw.js'
        with open(sw_path, 'w', encoding='utf-8') as f:
            f.write(sw_content)
        
        logging.info("تم إنشاء Service Worker")
    
    def create_lazy_loading_script(self):
        """إنشاء سكريبت التحميل التدريجي"""
        lazy_script = """
// التحميل التدريجي للصور
document.addEventListener('DOMContentLoaded', function() {
    const images = document.querySelectorAll('img[data-src]');
    
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });
    
    images.forEach(img => imageObserver.observe(img));
});

// التحميل التدريجي للمحتوى
function loadContent(url, containerId) {
    const container = document.getElementById(containerId);
    if (!container) return;
    
    // إظهار مؤشر التحميل
    container.innerHTML = '<div class="loading-spinner">جاري التحميل...</div>';
    
    fetch(url)
        .then(response => response.text())
        .then(html => {
            container.innerHTML = html;
            // تفعيل السكريبتات الجديدة
            const scripts = container.querySelectorAll('script');
            scripts.forEach(script => {
                const newScript = document.createElement('script');
                newScript.textContent = script.textContent;
                document.head.appendChild(newScript);
            });
        })
        .catch(error => {
            container.innerHTML = '<div class="error">خطأ في التحميل</div>';
            console.error('Error loading content:', error);
        });
}

// ضغط الطلبات
function compressRequest(data) {
    // ضغط البيانات قبل الإرسال (إذا كان المتصفح يدعم ذلك)
    if (typeof CompressionStream !== 'undefined') {
        const stream = new CompressionStream('gzip');
        const writer = stream.writable.getWriter();
        writer.write(new TextEncoder().encode(JSON.stringify(data)));
        writer.close();
        return stream.readable;
    }
    return data;
}
"""
        
        lazy_path = self.static_folder / 'js' / 'lazy-loading.js'
        lazy_path.parent.mkdir(exist_ok=True)
        
        with open(lazy_path, 'w', encoding='utf-8') as f:
            f.write(lazy_script)
        
        logging.info("تم إنشاء سكريبت التحميل التدريجي")
    
    def generate_optimization_report(self):
        """إنشاء تقرير التحسين"""
        total_original_size = sum(info['original_size'] for info in self.optimized_files.values())
        total_compressed_size = sum(info['compressed_size'] for info in self.optimized_files.values())
        total_savings = total_original_size - total_compressed_size
        total_savings_percent = (total_savings / total_original_size) * 100 if total_original_size > 0 else 0
        
        report = {
            'optimization_summary': {
                'total_files_optimized': len(self.optimized_files),
                'total_original_size': total_original_size,
                'total_compressed_size': total_compressed_size,
                'total_savings_bytes': total_savings,
                'total_savings_percent': total_savings_percent
            },
            'optimized_files': self.optimized_files,
            'cache_manifest_entries': len(self.cache_manifest)
        }
        
        # حفظ التقرير
        report_path = self.static_folder / 'optimization_report.json'
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logging.info(f"تم توفير {total_savings_percent:.1f}% من حجم الملفات")
        return report

# إنشاء Blueprint للتحسينات
optimization_bp = Blueprint('optimization', __name__)

@optimization_bp.route('/api/optimization/report')
def get_optimization_report():
    """الحصول على تقرير التحسين"""
    try:
        report_path = Path(current_app.static_folder) / 'optimization_report.json'
        if report_path.exists():
            with open(report_path, 'r', encoding='utf-8') as f:
                report = json.load(f)
            return jsonify(report)
        else:
            return jsonify({'error': 'تقرير التحسين غير متاح'}), 404
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@optimization_bp.route('/api/optimization/manifest')
def get_cache_manifest():
    """الحصول على cache manifest"""
    try:
        manifest_path = Path(current_app.static_folder) / 'manifest.json'
        if manifest_path.exists():
            with open(manifest_path, 'r', encoding='utf-8') as f:
                manifest = json.load(f)
            return jsonify(manifest)
        else:
            return jsonify({'error': 'Manifest غير متاح'}), 404
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# إنشاء مثيل عام لمحسن الواجهة الأمامية
frontend_optimizer = FrontendOptimizer()

def optimize_frontend_assets(app):
    """دالة مساعدة لتحسين أصول الواجهة الأمامية"""
    optimizer = FrontendOptimizer(app)
    
    # إنشاء Service Worker
    optimizer.create_service_worker()
    
    # إنشاء سكريبت التحميل التدريجي
    optimizer.create_lazy_loading_script()
    
    # إنشاء تقرير التحسين
    report = optimizer.generate_optimization_report()
    
    return report
