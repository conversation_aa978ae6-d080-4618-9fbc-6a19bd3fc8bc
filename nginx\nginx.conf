# إعدادات Nginx للبيئة الإنتاجية - Ta9affi

user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

# تحسين الأداء
worker_rlimit_nofile 65535;

events {
    worker_connections 4096;
    use epoll;
    multi_accept on;
    accept_mutex off;
}

http {
    # إعدادات MIME
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # تحسين الأداء
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    keepalive_requests 1000;
    types_hash_max_size 2048;
    server_tokens off;

    # إعدادات الضغط
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # إعدادات Brotli (إذ<PERSON> كان متاحاً)
    # brotli on;
    # brotli_comp_level 6;
    # brotli_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    # إعدادات التخزين المؤقت
    open_file_cache max=200000 inactive=20s;
    open_file_cache_valid 30s;
    open_file_cache_min_uses 2;
    open_file_cache_errors on;

    # إعدادات الحدود
    client_max_body_size 50M;
    client_body_buffer_size 128k;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
    client_body_timeout 12;
    client_header_timeout 12;
    send_timeout 10;

    # إعدادات السجلات
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    '$request_time $upstream_response_time';

    log_format detailed '$remote_addr - $remote_user [$time_local] "$request" '
                       '$status $body_bytes_sent "$http_referer" '
                       '"$http_user_agent" "$http_x_forwarded_for" '
                       '$request_time $upstream_response_time '
                       '$upstream_addr $upstream_status';

    access_log /var/log/nginx/access.log main;

    # إعدادات الأمان
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # إعدادات Rate Limiting
    limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=general:10m rate=1r/s;

    # إعدادات Connection Limiting
    limit_conn_zone $binary_remote_addr zone=perip:10m;
    limit_conn_zone $server_name zone=perserver:10m;

    # Upstream للتطبيق
    upstream ta9affi_app {
        least_conn;
        server ta9affi-app:5000 max_fails=3 fail_timeout=30s;
        # يمكن إضافة خوادم إضافية للتوازن
        # server ta9affi-app-2:5000 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    # إعدادات التخزين المؤقت
    proxy_cache_path /var/cache/nginx/ta9affi levels=1:2 keys_zone=ta9affi_cache:10m max_size=1g inactive=60m use_temp_path=off;

    # الخادم الرئيسي
    server {
        listen 80;
        server_name ta9affi.dz www.ta9affi.dz;

        # إعادة توجيه HTTP إلى HTTPS
        return 301 https://$server_name$request_uri;
    }

    # الخادم الآمن (HTTPS)
    server {
        listen 443 ssl http2;
        server_name ta9affi.dz www.ta9affi.dz;

        # إعدادات SSL
        ssl_certificate /etc/nginx/ssl/ta9affi.crt;
        ssl_certificate_key /etc/nginx/ssl/ta9affi.key;
        ssl_session_timeout 1d;
        ssl_session_cache shared:SSL:50m;
        ssl_session_tickets off;

        # بروتوكولات SSL الحديثة
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;

        # HSTS
        add_header Strict-Transport-Security "max-age=63072000" always;

        # إعدادات الحدود
        limit_conn perip 10;
        limit_conn perserver 1000;

        # مجلد الجذر
        root /var/www;
        index index.html index.htm;

        # إعدادات الملفات الثابتة
        location /static/ {
            alias /var/www/static/;
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header Vary Accept-Encoding;
            
            # ضغط الملفات
            gzip_static on;
            
            # التحقق من وجود النسخة المضغوطة
            location ~* \.(css|js)$ {
                add_header Vary Accept-Encoding;
                try_files $uri$gzip_static $uri =404;
            }
        }

        # ملفات التحميل
        location /uploads/ {
            alias /var/www/uploads/;
            expires 30d;
            add_header Cache-Control "public";
            
            # حماية من تنفيذ الملفات
            location ~* \.(php|pl|py|jsp|asp|sh|cgi)$ {
                deny all;
            }
        }

        # API endpoints
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            
            proxy_pass http://ta9affi_app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # إعدادات المهلة الزمنية
            proxy_connect_timeout 5s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
            
            # تخزين مؤقت للاستجابات
            proxy_cache ta9affi_cache;
            proxy_cache_valid 200 302 10m;
            proxy_cache_valid 404 1m;
            proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
            proxy_cache_lock on;
            
            add_header X-Cache-Status $upstream_cache_status;
        }

        # صفحات تسجيل الدخول
        location /login {
            limit_req zone=login burst=5 nodelay;
            
            proxy_pass http://ta9affi_app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # الصفحات العامة
        location / {
            limit_req zone=general burst=10 nodelay;
            
            # محاولة تقديم الملف الثابت أولاً
            try_files $uri $uri/ @app;
        }

        # التطبيق الرئيسي
        location @app {
            proxy_pass http://ta9affi_app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # إعدادات المهلة الزمنية
            proxy_connect_timeout 5s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
            
            # تحسين الاتصال
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            
            # تخزين مؤقت للصفحات
            proxy_cache ta9affi_cache;
            proxy_cache_valid 200 5m;
            proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
            
            add_header X-Cache-Status $upstream_cache_status;
        }

        # صفحة فحص الصحة
        location /health {
            access_log off;
            proxy_pass http://ta9affi_app;
            proxy_set_header Host $host;
        }

        # حظر الملفات الحساسة
        location ~ /\. {
            deny all;
            access_log off;
            log_not_found off;
        }

        location ~ ~$ {
            deny all;
            access_log off;
            log_not_found off;
        }

        # صفحات الأخطاء المخصصة
        error_page 404 /404.html;
        error_page 500 502 503 504 /50x.html;
        
        location = /404.html {
            root /var/www/error_pages;
            internal;
        }
        
        location = /50x.html {
            root /var/www/error_pages;
            internal;
        }

        # إعدادات الأمان الإضافية
        location ~ ^/(admin|dashboard)/ {
            # حماية إضافية للوحات الإدارة
            allow ***********/24;  # شبكة داخلية
            allow 10.0.0.0/8;      # شبكة داخلية
            deny all;
            
            proxy_pass http://ta9affi_app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    # خادم المراقبة (اختياري)
    server {
        listen 8080;
        server_name monitoring.ta9affi.dz;
        
        # حماية بكلمة مرور
        auth_basic "Monitoring Area";
        auth_basic_user_file /etc/nginx/.htpasswd;
        
        location /nginx_status {
            stub_status on;
            access_log off;
            allow 127.0.0.1;
            allow ***********/24;
            deny all;
        }
        
        location /grafana/ {
            proxy_pass http://grafana:3000/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        location /prometheus/ {
            proxy_pass http://prometheus:9090/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
