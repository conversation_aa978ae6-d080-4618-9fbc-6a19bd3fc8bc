"""
سكريبت لتحديث قاعدة البيانات وإضافة الحقول الجديدة للاشتراكات
"""

import sqlite3
from datetime import datetime, timedelta
import os

def migrate_database():
    """تحديث قاعدة البيانات لإضافة حقول الاشتراكات"""
    
    # مسار قاعدة البيانات
    db_path = 'instance/ta9affi_new.db'
    
    if not os.path.exists(db_path):
        print("قاعدة البيانات غير موجودة. سيتم إنشاؤها عند تشغيل التطبيق.")
        return
    
    # الاتصال بقاعدة البيانات
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        print("بدء تحديث قاعدة البيانات...")
        
        # التحقق من وجود الأعمدة الجديدة في جدول user
        cursor.execute("PRAGMA table_info(user)")
        columns = [column[1] for column in cursor.fetchall()]
        
        # إضافة عمود free_trial_end إذا لم يكن موجوداً
        if 'free_trial_end' not in columns:
            print("إضافة عمود free_trial_end...")
            cursor.execute("ALTER TABLE user ADD COLUMN free_trial_end DATETIME")
        
        # إضافة عمود subscription_status إذا لم يكن موجوداً
        if 'subscription_status' not in columns:
            print("إضافة عمود subscription_status...")
            cursor.execute("ALTER TABLE user ADD COLUMN subscription_status VARCHAR(20) DEFAULT 'trial'")
        
        # إنشاء جدول subscription_plan إذا لم يكن موجوداً
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS subscription_plan (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(100) NOT NULL,
                description TEXT,
                price FLOAT NOT NULL,
                duration_months INTEGER NOT NULL,
                is_institutional BOOLEAN DEFAULT FALSE,
                is_active BOOLEAN DEFAULT TRUE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        print("تم إنشاء/التحقق من جدول subscription_plan")
        
        # إنشاء جدول subscription إذا لم يكن موجوداً
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS subscription (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                plan_id INTEGER NOT NULL,
                start_date DATETIME NOT NULL,
                end_date DATETIME NOT NULL,
                is_free_trial BOOLEAN DEFAULT FALSE,
                is_active BOOLEAN DEFAULT TRUE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES user (id),
                FOREIGN KEY (plan_id) REFERENCES subscription_plan (id)
            )
        """)
        print("تم إنشاء/التحقق من جدول subscription")
        
        # إنشاء جدول payment إذا لم يكن موجوداً
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS payment (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                subscription_id INTEGER,
                amount FLOAT NOT NULL,
                currency VARCHAR(10) DEFAULT 'DZD',
                status VARCHAR(20) DEFAULT 'pending',
                chargily_checkout_id VARCHAR(255),
                chargily_response TEXT,
                paid_at DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES user (id),
                FOREIGN KEY (subscription_id) REFERENCES subscription (id)
            )
        """)
        print("تم إنشاء/التحقق من جدول payment")
        
        # إنشاء جدول subscription_notification إذا لم يكن موجوداً
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS subscription_notification (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                subscription_id INTEGER,
                notification_type VARCHAR(50) NOT NULL,
                message TEXT NOT NULL,
                is_read BOOLEAN DEFAULT FALSE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES user (id),
                FOREIGN KEY (subscription_id) REFERENCES subscription (id)
            )
        """)
        print("تم إنشاء/التحقق من جدول subscription_notification")
        
        # تحديث المستخدمين الموجودين لإضافة الفترة التجريبية
        print("تحديث المستخدمين الموجودين...")
        
        # الحصول على المستخدمين الذين هم أساتذة أو مفتشين وليس لديهم free_trial_end
        cursor.execute("""
            SELECT id, role FROM user 
            WHERE (role = 'teacher' OR role = 'inspector') 
            AND (free_trial_end IS NULL OR free_trial_end = '')
        """)
        
        users_to_update = cursor.fetchall()
        
        for user_id, role in users_to_update:
            # إضافة شهر مجاني من تاريخ اليوم
            trial_end = datetime.utcnow() + timedelta(days=30)
            cursor.execute("""
                UPDATE user 
                SET free_trial_end = ?, subscription_status = 'trial'
                WHERE id = ?
            """, (trial_end.isoformat(), user_id))
        
        print(f"تم تحديث {len(users_to_update)} مستخدم بالفترة التجريبية")
        
        # إدراج باقات الاشتراك الافتراضية
        print("إضافة باقات الاشتراك الافتراضية...")
        
        plans = [
            ('الباقة الشهرية', 'اشتراك لمدة شهر واحد', 1000.0, 1, False),
            ('الباقة الفصلية', 'اشتراك لمدة 3 أشهر', 2000.0, 3, False),
            ('الباقة السنوية', 'اشتراك للموسم الدراسي الكامل (9 أشهر)', 5000.0, 9, False),
            ('الباقة المؤسسية', 'للمؤسسات الخاصة والنقابات المعتمدة - لمعلومات أكثر اتصل بالإدارة', 0.0, 12, True)
        ]
        
        for name, description, price, duration, is_institutional in plans:
            # التحقق من عدم وجود الباقة مسبقاً
            cursor.execute("SELECT id FROM subscription_plan WHERE name = ?", (name,))
            if not cursor.fetchone():
                cursor.execute("""
                    INSERT INTO subscription_plan (name, description, price, duration_months, is_institutional)
                    VALUES (?, ?, ?, ?, ?)
                """, (name, description, price, duration, is_institutional))
        
        print("تم إضافة باقات الاشتراك الافتراضية")
        
        # حفظ التغييرات
        conn.commit()
        print("✅ تم تحديث قاعدة البيانات بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في تحديث قاعدة البيانات: {str(e)}")
        conn.rollback()
        
    finally:
        conn.close()

if __name__ == "__main__":
    migrate_database()
