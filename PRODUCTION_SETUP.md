# إعداد Ta9affi للعمل في وضع Production

## متطلبات النظام

### قاعدة البيانات
- MySQL 8.0+ أو PostgreSQL 13+
- أو SQLite للاختبار المحلي

### Python
- Python 3.8+
- pip

## إعداد متغيرات البيئة

قم بإنشاء ملف `.env` أو تعيين متغيرات البيئة التالية:

```bash
# تفعيل وضع Production
PRODUCTION_MODE=true

# عنوان IP الخادم
SERVER_IP=*************

# مفتاح الأمان (يجب تغييره)
SECRET_KEY=your-very-secure-secret-key-here

# إعدادات قاعدة البيانات
# الطريقة الأولى: استخدام DATABASE_URL
DATABASE_URL=mysql+pymysql://username:password@*************:3306/ta9affi_db?charset=utf8mb4

# الطريقة الثانية: استخدام متغيرات منفصلة
DB_HOST=*************
DB_PORT=3306
DB_NAME=ta9affi_db
DB_USER=ta9affi_user
DB_PASSWORD=your_database_password

# منفذ التطبيق (اختياري)
PORT=8000

# Redis للـ Rate Limiting (اختياري)
REDIS_URL=redis://*************:6379/0
```

## تثبيت المتطلبات

```bash
# تثبيت المتطلبات الأساسية
pip install -r requirements.txt

# تثبيت مكتبة MySQL (إذا كنت تستخدم MySQL)
pip install pymysql

# تثبيت مكتبة PostgreSQL (إذا كنت تستخدم PostgreSQL)
pip install psycopg2-binary
```

## إعداد قاعدة البيانات

### MySQL
```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE ta9affi_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- إنشاء مستخدم
CREATE USER 'ta9affi_user'@'%' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON ta9affi_db.* TO 'ta9affi_user'@'%';
FLUSH PRIVILEGES;
```

### PostgreSQL
```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE ta9affi_db;

-- إنشاء مستخدم
CREATE USER ta9affi_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE ta9affi_db TO ta9affi_user;
```

## تشغيل التطبيق

### في وضع Development
```bash
python app.py
```

### في وضع Production
```bash
# تعيين متغيرات البيئة
export PRODUCTION_MODE=true
export SERVER_IP=*************
export SECRET_KEY=your-secret-key
export DATABASE_URL=mysql+pymysql://user:pass@*************:3306/ta9affi_db

# تشغيل التطبيق
python app.py
```

### باستخدام Gunicorn (موصى به للـ Production)
```bash
# تثبيت Gunicorn
pip install gunicorn

# تشغيل التطبيق
gunicorn --bind 0.0.0.0:8000 --workers 4 app:app
```

## التحقق من حالة التطبيق

### Health Check
```bash
curl http://*************:8000/health
```

### حالة قاعدة البيانات (في وضع Development فقط)
```bash
curl http://localhost:5000/db-status
```

## الميزات الجديدة في وضع Production

1. **إعدادات أمان محسنة**
   - Secure cookies
   - HTTPS support
   - Session timeout

2. **إدارة أخطاء محسنة**
   - رسائل خطأ عامة للمستخدمين
   - تسجيل مفصل للأخطاء

3. **تحسينات قاعدة البيانات**
   - Connection pooling
   - Auto-reconnect
   - Timeout handling

4. **مراقبة النظام**
   - Health check endpoint
   - Database status monitoring

## استكشاف الأخطاء

### خطأ في الاتصال بقاعدة البيانات
1. تحقق من إعدادات قاعدة البيانات
2. تأكد من أن الخادم يقبل الاتصالات الخارجية
3. تحقق من صحة اسم المستخدم وكلمة المرور

### خطأ في تشغيل التطبيق
1. تحقق من متغيرات البيئة
2. تأكد من تثبيت جميع المتطلبات
3. تحقق من سجلات الأخطاء

## الأمان

1. **تغيير المفاتيح الافتراضية**
   - SECRET_KEY
   - كلمات مرور قاعدة البيانات

2. **تأمين قاعدة البيانات**
   - استخدام SSL/TLS
   - تقييد الوصول بـ IP
   - كلمات مرور قوية

3. **تأمين الخادم**
   - Firewall configuration
   - Regular updates
   - Monitoring

## النسخ الاحتياطية

يوصى بإنشاء نسخ احتياطية دورية من:
- قاعدة البيانات
- ملفات التطبيق
- ملفات الإعدادات

## الدعم

للحصول على المساعدة:
1. تحقق من سجلات التطبيق
2. استخدم `/health` للتحقق من حالة النظام
3. راجع هذا الدليل للحلول الشائعة
