#!/bin/bash
# سكريبت التحضير النهائي للإنتاج - Ta9affi

set -e

# ألوان للرسائل
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# دوال المساعدة
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}$1${NC}"
}

# عرض مقدمة
show_intro() {
    log_header "🚀 التحضير النهائي لـ Ta9affi للإنتاج"
    echo "=============================================="
    echo "هذا السكريبت سيقوم بـ:"
    echo "1. تنظيف المشروع من ملفات التطوير"
    echo "2. فحص جودة الكود والإعدادات"
    echo "3. تحسين الملفات للإنتاج"
    echo "4. إنشاء حزمة جاهزة للنشر"
    echo "=============================================="
    echo ""
}

# التحقق من المتطلبات
check_prerequisites() {
    log_header "🔍 التحقق من المتطلبات"
    
    # التحقق من Python
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 غير مثبت"
        exit 1
    fi
    
    # التحقق من Git
    if ! command -v git &> /dev/null; then
        log_error "Git غير مثبت"
        exit 1
    fi
    
    # التحقق من وجود الملفات الأساسية
    local required_files=("app_postgresql.py" "requirements.txt" "deploy.sh")
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            log_error "الملف المطلوب غير موجود: $file"
            exit 1
        fi
    done
    
    log_success "جميع المتطلبات متوفرة"
}

# تنظيف المشروع
cleanup_project() {
    log_header "🧹 تنظيف المشروع"
    
    if [ -f "cleanup_for_production.sh" ]; then
        chmod +x cleanup_for_production.sh
        ./cleanup_for_production.sh
    else
        log_warning "سكريبت التنظيف غير موجود، سيتم التنظيف اليدوي"
        
        # تنظيف أساسي
        rm -rf __pycache__/ .pytest_cache/ htmlcov/ .coverage
        find . -name "*.pyc" -delete
        find . -name "*.pyo" -delete
        find . -name "*~" -delete
        find . -name ".DS_Store" -delete
        
        log_success "تم التنظيف الأساسي"
    fi
}

# فحص جودة الكود
quality_check() {
    log_header "🔍 فحص جودة الكود"
    
    if [ -f "production_quality_check.py" ]; then
        python3 production_quality_check.py
        local exit_code=$?
        
        if [ $exit_code -eq 0 ]; then
            log_success "فحص الجودة نجح - المشروع جاهز للإنتاج"
        elif [ $exit_code -eq 1 ]; then
            log_warning "فحص الجودة: المشروع يحتاج تحسينات"
            echo "هل تريد المتابعة؟ (y/N)"
            read -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                log_info "تم إيقاف التحضير"
                exit 1
            fi
        else
            log_error "فحص الجودة فشل - المشروع غير جاهز"
            exit 1
        fi
    else
        log_warning "فاحص الجودة غير موجود، سيتم تخطي الفحص"
    fi
}

# تحسين الملفات
optimize_files() {
    log_header "⚡ تحسين الملفات"
    
    # ضغط ملفات CSS و JS
    if [ -d "static" ]; then
        log_info "ضغط ملفات CSS و JavaScript..."
        
        # ضغط CSS
        find static -name "*.css" -not -name "*.min.css" -exec gzip -k {} \;
        
        # ضغط JS
        find static -name "*.js" -not -name "*.min.js" -exec gzip -k {} \;
        
        log_success "تم ضغط الملفات الثابتة"
    fi
    
    # تحسين الصور
    if command -v optipng &> /dev/null && [ -d "static/images" ]; then
        log_info "تحسين الصور..."
        find static/images -name "*.png" -exec optipng -quiet {} \; 2>/dev/null || true
        log_success "تم تحسين الصور"
    fi
}

# إنشاء ملف الإصدار
create_version_file() {
    log_header "📋 إنشاء ملف الإصدار"
    
    local version="1.0.0"
    local build_date=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    local git_commit=$(git rev-parse HEAD 2>/dev/null || echo "unknown")
    local git_branch=$(git branch --show-current 2>/dev/null || echo "unknown")
    
    cat > version.json << EOF
{
    "name": "Ta9affi",
    "version": "$version",
    "build_date": "$build_date",
    "git_commit": "$git_commit",
    "git_branch": "$git_branch",
    "environment": "production",
    "description": "منصة التعليم الذكية"
}
EOF

    log_success "تم إنشاء ملف الإصدار: version.json"
}

# إنشاء دليل النشر
create_deployment_guide() {
    log_header "📖 إنشاء دليل النشر"
    
    cat > DEPLOYMENT.md << 'EOF'
# دليل النشر - Ta9affi

## 📋 متطلبات الخادم

### الحد الأدنى
- **المعالج**: 2 cores
- **الذاكرة**: 4GB RAM
- **التخزين**: 20GB SSD
- **الشبكة**: 100 Mbps

### المُوصى به
- **المعالج**: 4+ cores
- **الذاكرة**: 8GB+ RAM
- **التخزين**: 50GB+ SSD
- **الشبكة**: 1 Gbps

## 🛠️ البرامج المطلوبة

```bash
# تحديث النظام
sudo apt update && sudo apt upgrade -y

# تثبيت Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# تثبيت Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# إعادة تسجيل الدخول أو تشغيل
newgrp docker
```

## 🚀 خطوات النشر

### 1. تحضير الخادم
```bash
# إنشاء مستخدم للتطبيق
sudo useradd -m -s /bin/bash ta9affi
sudo usermod -aG docker ta9affi

# إنشاء مجلدات التطبيق
sudo mkdir -p /opt/ta9affi
sudo chown ta9affi:ta9affi /opt/ta9affi
```

### 2. رفع الملفات
```bash
# نسخ ملفات المشروع
scp -r . ta9affi@your-server:/opt/ta9affi/

# أو استخدام Git
git clone <repository-url> /opt/ta9affi
```

### 3. إعداد البيئة
```bash
cd /opt/ta9affi

# إنشاء ملف البيئة
cp .env.production.example .env.production

# تعديل الإعدادات
nano .env.production
```

### 4. تشغيل التطبيق
```bash
# جعل سكريبت النشر قابل للتنفيذ
chmod +x deploy.sh

# تشغيل النشر
./deploy.sh deploy
```

## 🔧 إعدادات مهمة

### قاعدة البيانات
```bash
# إعداد PostgreSQL خارجي (اختياري)
DB_HOST=your-postgres-server
DB_PORT=5432
DB_NAME=ta9affi_production
DB_USER=ta9affi_user
DB_PASSWORD=strong-password-here
```

### SSL/TLS
```bash
# وضع شهادات SSL
sudo mkdir -p /opt/ta9affi/nginx/ssl
sudo cp your-certificate.crt /opt/ta9affi/nginx/ssl/ta9affi.crt
sudo cp your-private-key.key /opt/ta9affi/nginx/ssl/ta9affi.key
sudo chmod 600 /opt/ta9affi/nginx/ssl/*
```

### النسخ الاحتياطي
```bash
# إعداد AWS للنسخ الاحتياطي (اختياري)
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_BACKUP_BUCKET=ta9affi-backups
```

## 📊 المراقبة

### الوصول للخدمات
- **التطبيق**: https://your-domain.com
- **Grafana**: http://your-domain.com:3000
- **Prometheus**: http://your-domain.com:9090

### أوامر المراقبة
```bash
# فحص حالة الخدمات
./deploy.sh status

# عرض السجلات
./deploy.sh logs

# فحص الصحة
./deploy.sh health
```

## 🔒 الأمان

### جدار الحماية
```bash
# إعداد UFW
sudo ufw enable
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 3000/tcp  # Grafana (اختياري)
sudo ufw allow 9090/tcp  # Prometheus (اختياري)
```

### تحديثات الأمان
```bash
# تحديثات تلقائية
sudo apt install unattended-upgrades
sudo dpkg-reconfigure -plow unattended-upgrades
```

## 🆘 استكشاف الأخطاء

### مشاكل شائعة
```bash
# فحص السجلات
docker-compose -f docker-compose.production.yml logs

# إعادة تشغيل الخدمات
./deploy.sh restart

# فحص استخدام الموارد
docker stats

# فحص مساحة القرص
df -h
```

### الدعم
- **البريد الإلكتروني**: <EMAIL>
- **الوثائق**: https://docs.ta9affi.dz
EOF

    log_success "تم إنشاء دليل النشر: DEPLOYMENT.md"
}

# إنشاء حزمة النشر
create_deployment_package() {
    log_header "📦 إنشاء حزمة النشر"
    
    local package_name="ta9affi-production-$(date +%Y%m%d_%H%M%S)"
    local package_dir="$package_name"
    
    # إنشاء مجلد الحزمة
    mkdir -p "$package_dir"
    
    # نسخ الملفات المطلوبة للإنتاج
    local production_files=(
        "app_postgresql.py"
        "requirements.txt"
        "docker-compose.production.yml"
        "Dockerfile.production"
        "gunicorn_config.py"
        "deploy.sh"
        ".env.production.example"
        "README.md"
        "DEPLOYMENT.md"
        "CHANGELOG.md"
        "LICENSE"
        "version.json"
        ".gitignore"
    )
    
    for file in "${production_files[@]}"; do
        if [ -f "$file" ]; then
            cp "$file" "$package_dir/"
        fi
    done
    
    # نسخ المجلدات المطلوبة
    local production_dirs=(
        "static"
        "templates"
        "nginx"
        "monitoring"
        "database"
    )
    
    for dir in "${production_dirs[@]}"; do
        if [ -d "$dir" ]; then
            cp -r "$dir" "$package_dir/"
        fi
    done
    
    # نسخ ملفات Python الأساسية
    cp *.py "$package_dir/" 2>/dev/null || true
    
    # إنشاء أرشيف مضغوط
    tar -czf "${package_name}.tar.gz" "$package_dir"
    
    # حذف المجلد المؤقت
    rm -rf "$package_dir"
    
    log_success "تم إنشاء حزمة النشر: ${package_name}.tar.gz"
    
    # عرض معلومات الحزمة
    local package_size=$(du -h "${package_name}.tar.gz" | cut -f1)
    echo "📊 معلومات الحزمة:"
    echo "   الاسم: ${package_name}.tar.gz"
    echo "   الحجم: $package_size"
    echo "   المحتويات: $(tar -tzf "${package_name}.tar.gz" | wc -l) ملف/مجلد"
}

# إنشاء تقرير التحضير
create_preparation_report() {
    log_header "📋 إنشاء تقرير التحضير"
    
    cat > preparation_report.md << EOF
# تقرير التحضير للإنتاج - Ta9affi

## 📅 معلومات التحضير
- **تاريخ التحضير**: $(date)
- **الإصدار**: 1.0.0
- **البيئة**: Production
- **المحضر بواسطة**: $(whoami)

## ✅ العمليات المكتملة
- [x] تنظيف ملفات التطوير
- [x] فحص جودة الكود
- [x] تحسين الملفات الثابتة
- [x] إنشاء ملف الإصدار
- [x] إنشاء دليل النشر
- [x] إنشاء حزمة النشر

## 📦 الملفات المُحضرة
- app_postgresql.py - التطبيق الرئيسي
- requirements.txt - متطلبات Python
- docker-compose.production.yml - إعداد Docker للإنتاج
- Dockerfile.production - صورة Docker
- deploy.sh - سكريبت النشر
- nginx/nginx.conf - إعدادات Nginx
- monitoring/ - ملفات المراقبة
- static/ - الملفات الثابتة المحسنة
- templates/ - قوالب HTML

## 🚀 خطوات النشر التالية
1. رفع حزمة النشر للخادم
2. فك ضغط الحزمة
3. إعداد ملف .env.production
4. تشغيل ./deploy.sh deploy
5. فحص الخدمات والمراقبة

## 📊 إحصائيات المشروع
- عدد ملفات Python: $(find . -name "*.py" | wc -l)
- عدد ملفات HTML: $(find templates -name "*.html" 2>/dev/null | wc -l || echo 0)
- عدد ملفات CSS: $(find static -name "*.css" 2>/dev/null | wc -l || echo 0)
- عدد ملفات JS: $(find static -name "*.js" 2>/dev/null | wc -l || echo 0)

## 🔗 روابط مفيدة
- [دليل النشر](DEPLOYMENT.md)
- [سجل التغييرات](CHANGELOG.md)
- [الترخيص](LICENSE)

---
تم إنشاء هذا التقرير تلقائياً بواسطة سكريبت التحضير للإنتاج.
EOF

    log_success "تم إنشاء تقرير التحضير: preparation_report.md"
}

# عرض الملخص النهائي
show_final_summary() {
    log_header "🎉 ملخص التحضير النهائي"
    
    echo "=============================================="
    echo "✅ تم تحضير Ta9affi للإنتاج بنجاح!"
    echo "=============================================="
    echo ""
    echo "📦 الملفات الجاهزة:"
    echo "   - حزمة النشر: ta9affi-production-*.tar.gz"
    echo "   - دليل النشر: DEPLOYMENT.md"
    echo "   - تقرير التحضير: preparation_report.md"
    echo "   - ملف الإصدار: version.json"
    echo ""
    echo "🚀 الخطوات التالية:"
    echo "   1. رفع حزمة النشر للخادم"
    echo "   2. فك ضغط الحزمة"
    echo "   3. إعداد ملف .env.production"
    echo "   4. تشغيل ./deploy.sh deploy"
    echo ""
    echo "📞 الدعم:"
    echo "   - البريد: <EMAIL>"
    echo "   - الوثائق: راجع DEPLOYMENT.md"
    echo "=============================================="
}

# الدالة الرئيسية
main() {
    show_intro
    
    # تأكيد من المستخدم
    echo "هل تريد المتابعة مع التحضير للإنتاج؟ (y/N)"
    read -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "تم إلغاء التحضير"
        exit 0
    fi
    
    # تنفيذ خطوات التحضير
    check_prerequisites
    cleanup_project
    quality_check
    optimize_files
    create_version_file
    create_deployment_guide
    create_deployment_package
    create_preparation_report
    
    # عرض الملخص النهائي
    show_final_summary
    
    log_success "🎉 تم تحضير Ta9affi للإنتاج بنجاح!"
}

# تشغيل السكريبت
main "$@"
