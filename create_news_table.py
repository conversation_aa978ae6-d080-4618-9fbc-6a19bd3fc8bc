#!/usr/bin/env python3
"""
إنشاء جدول الأخبار والتحديثات
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db
from models_new import NewsUpdate, User

def create_news_table():
    """إنشاء جدول الأخبار"""
    with app.app_context():
        try:
            # إنشاء الجدول
            db.create_all()
            print("✅ تم إنشاء جدول الأخبار بنجاح")
            
            # إضافة بعض الأخبار التجريبية
            admin_user = User.query.filter_by(role='admin').first()
            if admin_user:
                sample_news = [
                    {
                        'title': 'مرحباً بكم في نظام Ta9affi',
                        'content': 'نرحب بجميع المستخدمين في النظام الجديد لإدارة البرنامج السنوي للتدريس',
                        'priority': 10,
                        'is_active': True
                    },
                    {
                        'title': 'تحديث جديد للنظام',
                        'content': 'تم إضافة ميزات جديدة لتحسين تجربة المستخدم وسهولة الاستخدام',
                        'priority': 8,
                        'is_active': True
                    },
                    {
                        'title': 'نصائح للاستخدام الأمثل',
                        'content': 'ننصح بمراجعة دليل المستخدم للاستفادة من جميع ميزات النظام',
                        'priority': 5,
                        'is_active': True
                    }
                ]
                
                for news_data in sample_news:
                    # التحقق من عدم وجود الخبر
                    existing = NewsUpdate.query.filter_by(title=news_data['title']).first()
                    if not existing:
                        news = NewsUpdate(
                            title=news_data['title'],
                            content=news_data['content'],
                            priority=news_data['priority'],
                            is_active=news_data['is_active'],
                            created_by=admin_user.id
                        )
                        db.session.add(news)
                
                db.session.commit()
                print("✅ تم إضافة الأخبار التجريبية بنجاح")
            else:
                print("⚠️ لم يتم العثور على مستخدم أدمن لإضافة الأخبار التجريبية")
                
        except Exception as e:
            print(f"❌ خطأ في إنشاء جدول الأخبار: {str(e)}")
            db.session.rollback()

def check_news_table():
    """فحص جدول الأخبار"""
    with app.app_context():
        try:
            news_count = NewsUpdate.query.count()
            active_count = NewsUpdate.query.filter_by(is_active=True).count()
            
            print(f"📊 إحصائيات الأخبار:")
            print(f"   - إجمالي الأخبار: {news_count}")
            print(f"   - الأخبار النشطة: {active_count}")
            print(f"   - الأخبار المعطلة: {news_count - active_count}")
            
            if news_count > 0:
                print(f"\n📋 قائمة الأخبار:")
                news_list = NewsUpdate.query.order_by(NewsUpdate.priority.desc()).all()
                for news in news_list:
                    status = "نشط" if news.is_active else "معطل"
                    print(f"   - {news.title} (أولوية: {news.priority}, حالة: {status})")
            
        except Exception as e:
            print(f"❌ خطأ في فحص جدول الأخبار: {str(e)}")

def main():
    """تشغيل العمليات"""
    print("🚀 إنشاء وإعداد جدول الأخبار والتحديثات")
    print("=" * 60)
    
    # إنشاء الجدول
    create_news_table()
    
    # فحص الجدول
    check_news_table()
    
    print(f"\n" + "=" * 60)
    print(f"📋 ملخص العمليات:")
    print(f"   ✅ تم إنشاء جدول news_updates")
    print(f"   ✅ تم إضافة أخبار تجريبية")
    print(f"   ✅ النظام جاهز لإدارة الأخبار")
    
    print(f"\n🎯 للاختبار:")
    print(f"   1. سجل دخول كأدمن: admin_thr")
    print(f"   2. اذهب إلى: http://127.0.0.1:5000/admin/news")
    print(f"   3. أضف/عدل/احذف الأخبار")
    print(f"   4. تحقق من ظهورها في الصفحة الرئيسية")

if __name__ == "__main__":
    main()
