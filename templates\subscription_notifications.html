{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-bell me-2"></i>إشعارات الاشتراك</h2>
                <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-1"></i>العودة للوحة التحكم
                </a>
            </div>

            {% if notifications %}
            <div class="row">
                <div class="col-md-12">
                    {% for notification in notifications %}
                    <div class="card mb-3 {% if notification.notification_type == 'expired' %}border-danger{% elif notification.notification_type == 'expiring_soon' %}border-warning{% else %}border-success{% endif %}">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <div class="d-flex align-items-center mb-2">
                                        {% if notification.notification_type == 'expired' %}
                                        <i class="fas fa-exclamation-triangle text-danger me-2"></i>
                                        <span class="badge bg-danger">انتهى الاشتراك</span>
                                        {% elif notification.notification_type == 'expiring_soon' %}
                                        <i class="fas fa-clock text-warning me-2"></i>
                                        <span class="badge bg-warning">ينتهي قريباً</span>
                                        {% elif notification.notification_type == 'renewed' %}
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        <span class="badge bg-success">تم التجديد</span>
                                        {% else %}
                                        <i class="fas fa-info-circle text-info me-2"></i>
                                        <span class="badge bg-info">إشعار</span>
                                        {% endif %}
                                    </div>
                                    <p class="card-text mb-2">{{ notification.message }}</p>
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>
                                        {{ notification.created_at.strftime('%Y-%m-%d %H:%M') }}
                                    </small>
                                </div>
                                {% if notification.notification_type in ['expired', 'expiring_soon'] %}
                                <div class="ms-3">
                                    <a href="{{ url_for('subscription_plans') }}" class="btn btn-primary btn-sm">
                                        <i class="fas fa-credit-card me-1"></i>تجديد الاشتراك
                                    </a>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% else %}
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد إشعارات</h5>
                            <p class="text-muted">لم يتم إرسال أي إشعارات متعلقة بالاشتراك حتى الآن.</p>
                            <a href="{{ url_for('subscription_plans') }}" class="btn btn-primary">
                                <i class="fas fa-eye me-1"></i>عرض باقات الاشتراك
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<style>
.card {
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
}

.badge {
    font-size: 0.75em;
}
</style>
{% endblock %}
