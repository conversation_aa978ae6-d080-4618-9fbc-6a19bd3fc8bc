{% extends 'base.html' %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-newspaper me-2"></i>
                إدارة الأخبار والتحديثات
            </h2>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addNewsModal">
                <i class="fas fa-plus me-1"></i>
                إضافة خبر جديد
            </button>
        </div>

        <!-- معلومات سريعة -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center bg-primary text-white">
                    <div class="card-body">
                        <h5 class="card-title">{{ news_list|selectattr('is_active')|list|length }}</h5>
                        <p class="card-text">أخبار نشطة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center bg-secondary text-white">
                    <div class="card-body">
                        <h5 class="card-title">{{ news_list|rejectattr('is_active')|list|length }}</h5>
                        <p class="card-text">أخبار معطلة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center bg-info text-white">
                    <div class="card-body">
                        <h5 class="card-title">{{ news_list|length }}</h5>
                        <p class="card-text">إجمالي الأخبار</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center bg-warning text-white">
                    <div class="card-body">
                        <h5 class="card-title">{{ 10 - news_list|selectattr('is_active')|list|length }}</h5>
                        <p class="card-text">متاح للإضافة</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- تنبيه حول الحد الأقصى -->
        {% if news_list|selectattr('is_active')|list|length >= 10 %}
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>تنبيه:</strong> تم الوصول للحد الأقصى من الأخبار النشطة (10 أخبار). يجب إلغاء تفعيل خبر موجود لإضافة
            خبر جديد نشط.
        </div>
        {% endif %}

        <!-- جدول الأخبار -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-1"></i>
                    قائمة الأخبار والتحديثات
                </h5>
            </div>
            <div class="card-body">
                {% if news_list %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-light">
                            <tr>
                                <th style="color: black; font-weight: bold;">الأولوية</th>
                                <th style="color: black; font-weight: bold;">العنوان</th>
                                <th style="color: black; font-weight: bold;">المحتوى</th>
                                <th style="color: black; font-weight: bold;">الحالة</th>
                                <th style="color: black; font-weight: bold;">تاريخ الإنشاء</th>
                                <th style="color: black; font-weight: bold;">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for news in news_list %}
                            <tr class="{% if not news.is_active %}table-secondary{% endif %}">
                                <td>
                                    <span class="badge bg-primary">{{ news.priority }}</span>
                                </td>
                                <td>
                                    <strong>{{ news.title }}</strong>
                                </td>
                                <td>
                                    <div class="text-truncate" style="max-width: 300px;" title="{{ news.content }}">
                                        {{ news.content }}
                                    </div>
                                </td>
                                <td>
                                    {% if news.is_active %}
                                    <span class="badge bg-success">نشط</span>
                                    {% else %}
                                    <span class="badge bg-secondary">معطل</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <small>
                                        {% if news.created_at %}
                                        {{ news.created_at.strftime('%Y-%m-%d %H:%M') }}
                                        {% else %}
                                        غير محدد
                                        {% endif %}
                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button class="btn btn-sm btn-warning edit-news-btn"
                                            data-news-id="{{ news.id }}" data-news-title="{{ news.title }}"
                                            data-news-content="{{ news.content }}"
                                            data-news-priority="{{ news.priority }}"
                                            data-news-active="{{ news.is_active }}" data-bs-toggle="modal"
                                            data-bs-target="#editNewsModal">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger delete-news-btn"
                                            data-news-id="{{ news.id }}" data-news-title="{{ news.title }}"
                                            data-bs-toggle="modal" data-bs-target="#deleteNewsModal">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-newspaper fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد أخبار حالياً</h5>
                    <p class="text-muted">ابدأ بإضافة أول خبر أو تحديث</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة خبر جديد -->
<div class="modal fade" id="addNewsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>
                    إضافة خبر جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('add_news') }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="title" class="form-label">العنوان *</label>
                        <input type="text" class="form-control" id="title" name="title" required maxlength="200">
                        <div class="form-text">الحد الأقصى 200 حرف</div>
                    </div>

                    <div class="mb-3">
                        <label for="content" class="form-label">المحتوى *</label>
                        <textarea class="form-control" id="content" name="content" rows="4" required></textarea>
                        <div class="form-text">محتوى الخبر أو التحديث</div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <label for="priority" class="form-label">الأولوية</label>
                            <select class="form-select" id="priority" name="priority">
                                <option value="1">1 - منخفضة</option>
                                <option value="2">2</option>
                                <option value="3">3</option>
                                <option value="4">4</option>
                                <option value="5" selected>5 - متوسطة</option>
                                <option value="6">6</option>
                                <option value="7">7</option>
                                <option value="8">8</option>
                                <option value="9">9</option>
                                <option value="10">10 - عالية</option>
                            </select>
                        </div>
                        <div class="col-md-6 d-flex align-items-end">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                                <label class="form-check-label" for="is_active">
                                    تفعيل الخبر
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>
                        حفظ الخبر
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal تعديل خبر -->
<div class="modal fade" id="editNewsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>
                    تعديل الخبر
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="editNewsForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_title" class="form-label">العنوان *</label>
                        <input type="text" class="form-control" id="edit_title" name="title" required maxlength="200">
                    </div>

                    <div class="mb-3">
                        <label for="edit_content" class="form-label">المحتوى *</label>
                        <textarea class="form-control" id="edit_content" name="content" rows="4" required></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <label for="edit_priority" class="form-label">الأولوية</label>
                            <select class="form-select" id="edit_priority" name="priority">
                                <option value="1">1 - منخفضة</option>
                                <option value="2">2</option>
                                <option value="3">3</option>
                                <option value="4">4</option>
                                <option value="5">5 - متوسطة</option>
                                <option value="6">6</option>
                                <option value="7">7</option>
                                <option value="8">8</option>
                                <option value="9">9</option>
                                <option value="10">10 - عالية</option>
                            </select>
                        </div>
                        <div class="col-md-6 d-flex align-items-end">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="edit_is_active" name="is_active">
                                <label class="form-check-label" for="edit_is_active">
                                    تفعيل الخبر
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-save me-1"></i>
                        تحديث الخبر
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal حذف خبر -->
<div class="modal fade" id="deleteNewsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-danger">
                    <i class="fas fa-trash me-2"></i>
                    حذف الخبر
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    هل أنت متأكد من حذف الخبر "<span id="deleteNewsTitle"></span>"؟
                </div>
                <p class="text-muted">هذا الإجراء لا يمكن التراجع عنه.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form method="POST" id="deleteNewsForm" class="d-inline">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i>
                        حذف نهائياً
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    // إعداد event listeners للأزرار
    document.addEventListener('DOMContentLoaded', function () {
        // أزرار التعديل
        const editButtons = document.querySelectorAll('.edit-news-btn');
        editButtons.forEach(button => {
            button.addEventListener('click', function () {
                const id = this.getAttribute('data-news-id');
                const title = this.getAttribute('data-news-title');
                const content = this.getAttribute('data-news-content');
                const priority = this.getAttribute('data-news-priority');
                const isActive = this.getAttribute('data-news-active') === 'True';

                // تعبئة النموذج
                document.getElementById('editNewsForm').action = `/admin/news/${id}/edit`;
                document.getElementById('edit_title').value = title;
                document.getElementById('edit_content').value = content;
                document.getElementById('edit_priority').value = priority;
                document.getElementById('edit_is_active').checked = isActive;
            });
        });

        // أزرار الحذف
        const deleteButtons = document.querySelectorAll('.delete-news-btn');
        deleteButtons.forEach(button => {
            button.addEventListener('click', function () {
                const id = this.getAttribute('data-news-id');
                const title = this.getAttribute('data-news-title');

                // تعبئة نموذج الحذف
                document.getElementById('deleteNewsForm').action = `/admin/news/${id}/delete`;
                document.getElementById('deleteNewsTitle').textContent = title;
            });
        });
    });
</script>
{% endblock %}