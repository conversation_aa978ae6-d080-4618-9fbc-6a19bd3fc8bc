#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
لوحة تحكم المراقبة لتطبيق Ta9affi
توفر واجهة ويب لمراقبة الأداء والتنبيهات
"""

from flask import Blueprint, render_template_string, jsonify, request
from flask_login import login_required, current_user
from models_new import Role
from monitoring_system import performance_monitor
from alerting_system import alert_manager
from redis_manager import redis_manager
from cache_manager import cache_manager
import json
import time

# إنشاء Blueprint للمراقبة
monitoring_bp = Blueprint('monitoring', __name__, url_prefix='/admin/monitoring')

@monitoring_bp.route('/')
@login_required
def dashboard():
    """لوحة تحكم المراقبة الرئيسية"""
    if current_user.role != Role.ADMIN:
        return jsonify({'error': 'غير مصرح بالوصول'}), 403
    
    # قالب HTML للوحة التحكم
    dashboard_template = """
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة مراقبة الأداء - Ta9affi</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .alert-card {
            border-left: 4px solid;
            margin-bottom: 10px;
        }
        .alert-critical { border-left-color: #dc3545; }
        .alert-warning { border-left-color: #ffc107; }
        .alert-info { border-left-color: #17a2b8; }
        .chart-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .refresh-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center my-4">
                    <i class="fas fa-chart-line"></i>
                    لوحة مراقبة الأداء
                </h1>
            </div>
        </div>
        
        <!-- مقاييس النظام -->
        <div class="row" id="system-metrics">
            <div class="col-md-3">
                <div class="metric-card text-center">
                    <i class="fas fa-microchip fa-2x mb-2"></i>
                    <h4 id="cpu-usage">--</h4>
                    <p>استخدام المعالج</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card text-center">
                    <i class="fas fa-memory fa-2x mb-2"></i>
                    <h4 id="memory-usage">--</h4>
                    <p>استخدام الذاكرة</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card text-center">
                    <i class="fas fa-users fa-2x mb-2"></i>
                    <h4 id="online-users">--</h4>
                    <p>المستخدمين المتصلين</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card text-center">
                    <i class="fas fa-clock fa-2x mb-2"></i>
                    <h4 id="response-time">--</h4>
                    <p>متوسط وقت الاستجابة</p>
                </div>
            </div>
        </div>
        
        <!-- الرسوم البيانية -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="chart-container">
                    <h5>استخدام الموارد</h5>
                    <canvas id="resourceChart"></canvas>
                </div>
            </div>
            <div class="col-md-6">
                <div class="chart-container">
                    <h5>المستخدمين المتصلين</h5>
                    <canvas id="usersChart"></canvas>
                </div>
            </div>
        </div>
        
        <!-- التنبيهات الحديثة -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-exclamation-triangle"></i> التنبيهات الحديثة</h5>
                    </div>
                    <div class="card-body" id="alerts-container">
                        <p class="text-muted">جاري تحميل التنبيهات...</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- إحصائيات قاعدة البيانات -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-database"></i> قاعدة البيانات</h5>
                    </div>
                    <div class="card-body" id="database-stats">
                        <p class="text-muted">جاري تحميل إحصائيات قاعدة البيانات...</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-server"></i> Redis</h5>
                    </div>
                    <div class="card-body" id="redis-stats">
                        <p class="text-muted">جاري تحميل إحصائيات Redis...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- زر التحديث -->
    <button class="btn btn-primary refresh-btn" onclick="refreshData()">
        <i class="fas fa-sync-alt"></i>
    </button>
    
    <script>
        let resourceChart, usersChart;
        
        // تهيئة الرسوم البيانية
        function initCharts() {
            // رسم بياني لاستخدام الموارد
            const resourceCtx = document.getElementById('resourceChart').getContext('2d');
            resourceChart = new Chart(resourceCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'المعالج %',
                        data: [],
                        borderColor: 'rgb(255, 99, 132)',
                        tension: 0.1
                    }, {
                        label: 'الذاكرة %',
                        data: [],
                        borderColor: 'rgb(54, 162, 235)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });
            
            // رسم بياني للمستخدمين
            const usersCtx = document.getElementById('usersChart').getContext('2d');
            usersChart = new Chart(usersCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'المستخدمين المتصلين',
                        data: [],
                        borderColor: 'rgb(75, 192, 192)',
                        tension: 0.1,
                        fill: true,
                        backgroundColor: 'rgba(75, 192, 192, 0.2)'
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }
        
        // تحديث البيانات
        function refreshData() {
            // تحديث المقاييس الحالية
            fetch('/admin/monitoring/api/current-metrics')
                .then(response => response.json())
                .then(data => {
                    updateMetrics(data);
                })
                .catch(error => console.error('Error:', error));
            
            // تحديث التنبيهات
            fetch('/admin/monitoring/api/alerts')
                .then(response => response.json())
                .then(data => {
                    updateAlerts(data);
                })
                .catch(error => console.error('Error:', error));
            
            // تحديث إحصائيات قاعدة البيانات
            fetch('/admin/monitoring/api/database-stats')
                .then(response => response.json())
                .then(data => {
                    updateDatabaseStats(data);
                })
                .catch(error => console.error('Error:', error));
            
            // تحديث إحصائيات Redis
            fetch('/admin/monitoring/api/redis-stats')
                .then(response => response.json())
                .then(data => {
                    updateRedisStats(data);
                })
                .catch(error => console.error('Error:', error));
        }
        
        // تحديث المقاييس
        function updateMetrics(data) {
            document.getElementById('cpu-usage').textContent = data.cpu_percent ? data.cpu_percent.toFixed(1) + '%' : '--';
            document.getElementById('memory-usage').textContent = data.memory_percent ? data.memory_percent.toFixed(1) + '%' : '--';
            document.getElementById('online-users').textContent = data.application ? data.application.online_users : '--';
            document.getElementById('response-time').textContent = data.avg_response_time ? data.avg_response_time.toFixed(2) + 'ms' : '--';
            
            // تحديث الرسوم البيانية
            const now = new Date().toLocaleTimeString('ar-SA');
            
            if (resourceChart) {
                resourceChart.data.labels.push(now);
                resourceChart.data.datasets[0].data.push(data.cpu_percent || 0);
                resourceChart.data.datasets[1].data.push(data.memory_percent || 0);
                
                // الاحتفاظ بآخر 20 نقطة
                if (resourceChart.data.labels.length > 20) {
                    resourceChart.data.labels.shift();
                    resourceChart.data.datasets[0].data.shift();
                    resourceChart.data.datasets[1].data.shift();
                }
                
                resourceChart.update();
            }
            
            if (usersChart && data.application) {
                usersChart.data.labels.push(now);
                usersChart.data.datasets[0].data.push(data.application.online_users || 0);
                
                if (usersChart.data.labels.length > 20) {
                    usersChart.data.labels.shift();
                    usersChart.data.datasets[0].data.shift();
                }
                
                usersChart.update();
            }
        }
        
        // تحديث التنبيهات
        function updateAlerts(alerts) {
            const container = document.getElementById('alerts-container');
            
            if (alerts.length === 0) {
                container.innerHTML = '<p class="text-success"><i class="fas fa-check"></i> لا توجد تنبيهات حديثة</p>';
                return;
            }
            
            let html = '';
            alerts.forEach(alert => {
                const severityClass = `alert-${alert.severity}`;
                const icon = alert.severity === 'critical' ? 'fas fa-exclamation-circle' : 
                           alert.severity === 'warning' ? 'fas fa-exclamation-triangle' : 'fas fa-info-circle';
                
                html += `
                    <div class="alert-card card ${severityClass}">
                        <div class="card-body">
                            <h6><i class="${icon}"></i> ${alert.rule_name}</h6>
                            <p class="mb-1">${alert.metric}: ${alert.value} (العتبة: ${alert.threshold})</p>
                            <small class="text-muted">${new Date(alert.timestamp).toLocaleString('ar-SA')}</small>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }
        
        // تحديث إحصائيات قاعدة البيانات
        function updateDatabaseStats(stats) {
            const container = document.getElementById('database-stats');
            
            if (!stats || Object.keys(stats).length === 0) {
                container.innerHTML = '<p class="text-muted">غير متاح</p>';
                return;
            }
            
            container.innerHTML = `
                <div class="row">
                    <div class="col-6">
                        <strong>الاتصالات النشطة:</strong><br>
                        <span class="h5">${stats.active_connections || 0}</span>
                    </div>
                    <div class="col-6">
                        <strong>حجم قاعدة البيانات:</strong><br>
                        <span class="h5">${stats.database_size_mb ? stats.database_size_mb.toFixed(1) + ' MB' : 'غير متاح'}</span>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-12">
                        <strong>الاستعلامات البطيئة:</strong>
                        <span class="badge bg-warning">${stats.slow_queries_count || 0}</span>
                    </div>
                </div>
            `;
        }
        
        // تحديث إحصائيات Redis
        function updateRedisStats(stats) {
            const container = document.getElementById('redis-stats');
            
            if (!stats || Object.keys(stats).length === 0) {
                container.innerHTML = '<p class="text-muted">غير متاح</p>';
                return;
            }
            
            container.innerHTML = `
                <div class="row">
                    <div class="col-6">
                        <strong>العملاء المتصلين:</strong><br>
                        <span class="h5">${stats.connected_clients || 0}</span>
                    </div>
                    <div class="col-6">
                        <strong>الذاكرة المستخدمة:</strong><br>
                        <span class="h5">${stats.used_memory_mb ? stats.used_memory_mb.toFixed(1) + ' MB' : 'غير متاح'}</span>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-6">
                        <strong>Cache Hits:</strong>
                        <span class="badge bg-success">${stats.keyspace_hits || 0}</span>
                    </div>
                    <div class="col-6">
                        <strong>Cache Misses:</strong>
                        <span class="badge bg-danger">${stats.keyspace_misses || 0}</span>
                    </div>
                </div>
            `;
        }
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initCharts();
            refreshData();
            
            // تحديث تلقائي كل 30 ثانية
            setInterval(refreshData, 30000);
        });
    </script>
</body>
</html>
    """
    
    return render_template_string(dashboard_template)

@monitoring_bp.route('/api/current-metrics')
@login_required
def api_current_metrics():
    """API للحصول على المقاييس الحالية"""
    if current_user.role != Role.ADMIN:
        return jsonify({'error': 'غير مصرح بالوصول'}), 403
    
    try:
        # الحصول على ملخص الأداء
        summary = performance_monitor.get_performance_summary(hours=1)
        return jsonify(summary)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@monitoring_bp.route('/api/alerts')
@login_required
def api_alerts():
    """API للحصول على التنبيهات الحديثة"""
    if current_user.role != Role.ADMIN:
        return jsonify({'error': 'غير مصرح بالوصول'}), 403
    
    try:
        alerts = alert_manager.get_recent_alerts(limit=10)
        return jsonify(alerts)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@monitoring_bp.route('/api/database-stats')
@login_required
def api_database_stats():
    """API للحصول على إحصائيات قاعدة البيانات"""
    if current_user.role != Role.ADMIN:
        return jsonify({'error': 'غير مصرح بالوصول'}), 403
    
    try:
        # الحصول على إحصائيات قاعدة البيانات من المراقب
        summary = performance_monitor.get_performance_summary(hours=1)
        return jsonify(summary.get('database', {}))
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@monitoring_bp.route('/api/redis-stats')
@login_required
def api_redis_stats():
    """API للحصول على إحصائيات Redis"""
    if current_user.role != Role.ADMIN:
        return jsonify({'error': 'غير مصرح بالوصول'}), 403
    
    try:
        if redis_manager.is_available():
            stats = redis_manager.get_stats()
            return jsonify(stats)
        else:
            return jsonify({'error': 'Redis غير متاح'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@monitoring_bp.route('/api/performance-summary')
@login_required
def api_performance_summary():
    """API للحصول على ملخص الأداء"""
    if current_user.role != Role.ADMIN:
        return jsonify({'error': 'غير مصرح بالوصول'}), 403
    
    hours = request.args.get('hours', 24, type=int)
    
    try:
        summary = performance_monitor.get_performance_summary(hours=hours)
        alert_stats = alert_manager.get_alert_statistics(hours=hours)
        cache_stats = cache_manager.get_cache_stats()
        
        return jsonify({
            'performance': summary,
            'alerts': alert_stats,
            'cache': cache_stats
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500
