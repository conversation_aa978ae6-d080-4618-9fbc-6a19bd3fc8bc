#!/bin/bash
# سكريبت نشر Ta9affi على cPanel - DZSecurity

set -e

# ألوان للرسائل
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}$1${NC}"
}

# متغيرات الإعداد
DOMAIN="ta9affi.com"
PROJECT_NAME="ta9affi"
DB_NAME="ta9affi_prod"
DB_USER="ta9affi_user"

# عرض مقدمة
show_intro() {
    log_header "🚀 نشر Ta9affi على cPanel - DZSecurity"
    echo "=============================================="
    echo "هذا السكريبت سيساعدك في:"
    echo "1. تحضير المشروع لـ cPanel"
    echo "2. إنشاء ملفات الإعداد المناسبة"
    echo "3. إرشادات الرفع خطوة بخطوة"
    echo "=============================================="
    echo ""
}

# فحص المتطلبات
check_requirements() {
    log_header "🔍 فحص المتطلبات"
    
    # فحص وجود الملفات الأساسية
    local required_files=("app_postgresql.py" "requirements.txt" "deploy.sh")
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            log_error "الملف المطلوب غير موجود: $file"
            exit 1
        fi
    done
    
    log_success "جميع الملفات الأساسية موجودة"
}

# تنظيف المشروع لـ cPanel
cleanup_for_cpanel() {
    log_header "🧹 تنظيف المشروع لـ cPanel"
    
    if [ -f "smart_cleanup.sh" ]; then
        chmod +x smart_cleanup.sh
        ./smart_cleanup.sh
    else
        log_warning "سكريبت التنظيف الذكي غير موجود، سيتم التنظيف اليدوي"
        
        # تنظيف أساسي
        rm -rf __pycache__/ .pytest_cache/ htmlcov/ .coverage
        find . -name "*.pyc" -delete
        find . -name "*.pyo" -delete
        find . -name "*~" -delete
        find . -name ".DS_Store" -delete
        
        log_success "تم التنظيف الأساسي"
    fi
}

# إنشاء ملف إعداد خاص بـ cPanel
create_cpanel_config() {
    log_header "⚙️ إنشاء ملف إعداد cPanel"
    
    cat > .env.cpanel << EOF
# إعدادات Ta9affi لـ cPanel - DZSecurity

# معلومات النطاق
DOMAIN_NAME=${DOMAIN}
ALLOWED_HOSTS=${DOMAIN},www.${DOMAIN}

# إعدادات قاعدة البيانات (سيتم تحديثها في cPanel)
DATABASE_URL=postgresql://${DB_USER}:YOUR_DB_PASSWORD@localhost:5432/${DB_NAME}
DB_HOST=localhost
DB_PORT=5432
DB_NAME=${DB_NAME}
DB_USER=${DB_USER}
DB_PASSWORD=YOUR_STRONG_DB_PASSWORD

# إعدادات Redis (إذا كان متاح في cPanel)
REDIS_URL=redis://localhost:6379/0

# إعدادات Flask
SECRET_KEY=YOUR_VERY_STRONG_SECRET_KEY_HERE
FLASK_ENV=production
FLASK_DEBUG=False

# إعدادات الأمان
SECURITY_PASSWORD_SALT=YOUR_SECURITY_SALT
WTF_CSRF_SECRET_KEY=YOUR_CSRF_SECRET_KEY

# إعدادات البريد الإلكتروني
MAIL_SERVER=mail.${DOMAIN}
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=noreply@${DOMAIN}
MAIL_PASSWORD=YOUR_EMAIL_PASSWORD
MAIL_DEFAULT_SENDER=noreply@${DOMAIN}

# إعدادات الملفات المرفوعة
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=16777216

# إعدادات التخزين المؤقت
CACHE_TYPE=simple
CACHE_DEFAULT_TIMEOUT=300

# إعدادات الجلسات
SESSION_TYPE=filesystem
SESSION_PERMANENT=False
SESSION_USE_SIGNER=True

# إعدادات المراقبة
MONITORING_ENABLED=True
METRICS_ENABLED=True

# إعدادات النسخ الاحتياطي (اختياري)
BACKUP_ENABLED=True
BACKUP_SCHEDULE=daily
BACKUP_RETENTION_DAYS=30

# إعدادات الأداء
SQLALCHEMY_ENGINE_OPTIONS={"pool_pre_ping": True, "pool_recycle": 300}
SQLALCHEMY_TRACK_MODIFICATIONS=False
EOF

    log_success "تم إنشاء ملف إعداد cPanel: .env.cpanel"
}

# إنشاء ملف requirements مُحسن لـ cPanel
create_cpanel_requirements() {
    log_header "📦 إنشاء requirements مُحسن لـ cPanel"
    
    cat > requirements.cpanel.txt << 'EOF'
# متطلبات Ta9affi لـ cPanel - محسنة للاستضافة المشتركة

# Flask Framework
Flask==2.3.3
Flask-SQLAlchemy==3.0.5
Flask-Login==0.6.3
Flask-WTF==1.1.1
Flask-Mail==0.9.1
Flask-Migrate==4.0.5
Flask-Limiter==3.5.0
Flask-Talisman==1.1.0
Flask-Session==0.5.0

# قاعدة البيانات
psycopg2-binary==2.9.7
SQLAlchemy==2.0.21

# Redis (اختياري - حسب توفره في cPanel)
redis==5.0.0

# خادم التطبيق (للاستضافة المشتركة)
gunicorn==21.2.0

# الأمان والتشفير
cryptography==41.0.4
bcrypt==4.0.1

# معالجة الملفات
Pillow==10.0.0
python-magic==0.4.27

# المراقبة (مبسطة للاستضافة المشتركة)
psutil==5.9.5

# أدوات مساعدة
python-dotenv==1.0.0
click==8.1.7
Werkzeug==2.3.7
Jinja2==3.1.2
WTForms==3.0.1
requests==2.31.0
python-dateutil==2.8.2
email-validator==2.0.0
EOF

    log_success "تم إنشاء requirements محسن لـ cPanel: requirements.cpanel.txt"
}

# إنشاء ملف تطبيق مُحسن لـ cPanel
create_cpanel_app() {
    log_header "🐍 إنشاء ملف تطبيق مُحسن لـ cPanel"
    
    cat > app_cpanel.py << 'EOF'
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ta9affi - تطبيق محسن لـ cPanel
"""

import os
import sys
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# تحميل متغيرات البيئة
from dotenv import load_dotenv

# تحميل ملف البيئة المناسب
env_file = project_root / '.env.cpanel'
if env_file.exists():
    load_dotenv(env_file)
else:
    load_dotenv()

# استيراد التطبيق الرئيسي
try:
    from app_postgresql import app
except ImportError as e:
    print(f"خطأ في استيراد التطبيق: {e}")
    sys.exit(1)

# إعدادات خاصة بـ cPanel
app.config.update(
    # تعطيل وضع التطوير
    DEBUG=False,
    TESTING=False,
    
    # إعدادات الأمان
    SESSION_COOKIE_SECURE=True,
    SESSION_COOKIE_HTTPONLY=True,
    SESSION_COOKIE_SAMESITE='Lax',
    
    # إعدادات الأداء
    SEND_FILE_MAX_AGE_DEFAULT=31536000,  # سنة واحدة للملفات الثابتة
    
    # إعدادات التخزين المؤقت
    CACHE_TYPE='simple',
    CACHE_DEFAULT_TIMEOUT=300,
)

# دالة للتحقق من صحة التطبيق
def health_check():
    """فحص صحة التطبيق"""
    try:
        # فحص قاعدة البيانات
        from flask_sqlalchemy import SQLAlchemy
        db = SQLAlchemy(app)
        
        with app.app_context():
            db.engine.execute('SELECT 1')
        
        return True, "التطبيق يعمل بشكل صحيح"
    except Exception as e:
        return False, f"خطأ في التطبيق: {str(e)}"

# نقطة دخول WSGI لـ cPanel
application = app

if __name__ == '__main__':
    # للتشغيل المحلي فقط
    port = int(os.environ.get('PORT', 5000))
    app.run(host='0.0.0.0', port=port, debug=False)
EOF

    log_success "تم إنشاء ملف التطبيق المحسن لـ cPanel: app_cpanel.py"
}

# إنشاء ملف .htaccess لـ cPanel
create_htaccess() {
    log_header "🌐 إنشاء ملف .htaccess"
    
    cat > .htaccess << 'EOF'
# Ta9affi - إعدادات Apache لـ cPanel

# إعادة توجيه HTTPS
RewriteEngine On
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# إعدادات الأمان
Header always set X-Frame-Options DENY
Header always set X-Content-Type-Options nosniff
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"
Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"

# إعدادات التخزين المؤقت
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
</IfModule>

# ضغط الملفات
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# حماية الملفات الحساسة
<Files ".env*">
    Order allow,deny
    Deny from all
</Files>

<Files "*.py">
    Order allow,deny
    Deny from all
</Files>

# السماح بالوصول للملفات الثابتة
<FilesMatch "\.(css|js|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot)$">
    Order allow,deny
    Allow from all
</FilesMatch>
EOF

    log_success "تم إنشاء ملف .htaccess"
}

# إنشاء دليل رفع cPanel
create_cpanel_instructions() {
    log_header "📖 إنشاء دليل رفع cPanel"
    
    cat > CPANEL_UPLOAD_GUIDE.md << 'EOF'
# 🚀 دليل رفع Ta9affi على cPanel - خطوة بخطوة

## 📋 الملفات الجاهزة للرفع
- ✅ `app_cpanel.py` - التطبيق المحسن لـ cPanel
- ✅ `.env.cpanel` - ملف الإعداد
- ✅ `requirements.cpanel.txt` - المتطلبات المحسنة
- ✅ `.htaccess` - إعدادات Apache
- ✅ جميع ملفات المشروع الأساسية

## 🔧 خطوات الرفع في cPanel

### 1. إعداد قاعدة البيانات
1. **دخول cPanel**: `https://cpanel.ta9affi.com`
2. **MySQL Databases** → إنشاء قاعدة بيانات:
   - اسم القاعدة: `ta9affi_prod`
3. **إنشاء مستخدم**:
   - اسم المستخدم: `ta9affi_user`
   - كلمة مرور قوية
4. **ربط المستخدم بالقاعدة** مع جميع الصلاحيات

### 2. رفع الملفات
1. **File Manager** → `public_html`
2. **رفع الحزمة المضغوطة**
3. **فك الضغط**
4. **نقل الملفات** من المجلد المفكوك إلى `public_html`

### 3. إعداد Python App
1. **Python App** → Create Application
2. **إعدادات التطبيق**:
   - Python Version: 3.11
   - Application Root: `/public_html`
   - Application URL: `ta9affi.com`
   - Application Startup File: `app_cpanel.py`

### 4. تثبيت المتطلبات
1. **في Python App** → Packages
2. **رفع** `requirements.cpanel.txt`
3. **Install Packages**

### 5. إعداد متغيرات البيئة
1. **نسخ** `.env.cpanel` إلى `.env`
2. **تعديل القيم**:
   - `DB_PASSWORD`: كلمة مرور قاعدة البيانات
   - `SECRET_KEY`: مفتاح تشفير قوي
   - `MAIL_PASSWORD`: كلمة مرور البريد

### 6. تشغيل التطبيق
1. **Python App** → Restart
2. **اختبار**: `https://ta9affi.com`

## 🔒 إعداد SSL
1. **SSL/TLS** → Let's Encrypt
2. **إضافة النطاقات**:
   - `ta9affi.com`
   - `www.ta9affi.com`
3. **Issue Certificate**

## 📊 المراقبة
- **Resource Usage**: مراقبة الموارد
- **Error Logs**: فحص الأخطاء
- **Access Logs**: مراقبة الزيارات

## 🆘 استكشاف الأخطاء
- **Error Logs** في cPanel
- **Python App Logs**
- **فحص** `.env` للتأكد من الإعدادات

---
**Ta9affi جاهز للعمل على cPanel! 🎉**
EOF

    log_success "تم إنشاء دليل رفع cPanel: CPANEL_UPLOAD_GUIDE.md"
}

# إنشاء حزمة cPanel
create_cpanel_package() {
    log_header "📦 إنشاء حزمة cPanel"
    
    local package_name="ta9affi-cpanel-$(date +%Y%m%d_%H%M%S)"
    
    # إنشاء أرشيف مضغوط محسن لـ cPanel
    tar --exclude='.git' --exclude='backup_*' --exclude='*.tar.gz' \
        --exclude='docker-compose*.yml' --exclude='Dockerfile*' \
        --exclude='deploy.sh' --exclude='nginx/' --exclude='monitoring/' \
        -czf "${package_name}.tar.gz" .
    
    local package_size=$(du -h "${package_name}.tar.gz" | cut -f1)
    local file_count=$(tar -tzf "${package_name}.tar.gz" | wc -l)
    
    log_success "تم إنشاء حزمة cPanel:"
    echo "  📦 الاسم: ${package_name}.tar.gz"
    echo "  📏 الحجم: $package_size"
    echo "  📄 عدد الملفات: $file_count"
    
    echo ""
    echo "🚀 خطوات الرفع:"
    echo "  1. رفع الحزمة لـ cPanel File Manager"
    echo "  2. فك الضغط في public_html"
    echo "  3. اتباع دليل CPANEL_UPLOAD_GUIDE.md"
}

# عرض الملخص النهائي
show_final_summary() {
    log_header "🎉 ملخص التحضير لـ cPanel"
    
    echo "=============================================="
    echo "✅ تم تحضير Ta9affi لـ cPanel بنجاح!"
    echo "=============================================="
    echo ""
    echo "📦 الملفات الجاهزة:"
    echo "   - حزمة cPanel: ta9affi-cpanel-*.tar.gz"
    echo "   - دليل الرفع: CPANEL_UPLOAD_GUIDE.md"
    echo "   - تطبيق محسن: app_cpanel.py"
    echo "   - إعدادات: .env.cpanel"
    echo "   - متطلبات: requirements.cpanel.txt"
    echo ""
    echo "🚀 الخطوات التالية:"
    echo "   1. رفع الحزمة لـ cPanel"
    echo "   2. إعداد قاعدة البيانات"
    echo "   3. تكوين Python App"
    echo "   4. تفعيل SSL"
    echo ""
    echo "📞 الدعم:"
    echo "   - DZSecurity: دعم فني 24/7"
    echo "   - دليل الرفع: CPANEL_UPLOAD_GUIDE.md"
    echo "=============================================="
}

# الدالة الرئيسية
main() {
    show_intro
    
    # تأكيد من المستخدم
    echo "هل تريد تحضير Ta9affi لـ cPanel؟ (y/N)"
    read -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "تم إلغاء التحضير"
        exit 0
    fi
    
    # تنفيذ خطوات التحضير
    check_requirements
    cleanup_for_cpanel
    create_cpanel_config
    create_cpanel_requirements
    create_cpanel_app
    create_htaccess
    create_cpanel_instructions
    create_cpanel_package
    
    # عرض الملخص النهائي
    show_final_summary
    
    log_success "🎉 Ta9affi جاهز لـ cPanel!"
}

# تشغيل السكريبت
main "$@"
