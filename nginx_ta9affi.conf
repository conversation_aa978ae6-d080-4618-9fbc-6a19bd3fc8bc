# إعدادات Nginx لتطبيق Ta9affi
# محسن للأداء العالي مع آلاف المستخدمين المتزامنين

# تجمع العمليات العلوية
upstream ta9affi_app {
    # خوادم Gunicorn
    server 127.0.0.1:8000 max_fails=3 fail_timeout=30s;
    # يمكن إضافة خوادم إضافية للتوزيع
    # server 127.0.0.1:8001 max_fails=3 fail_timeout=30s;
    # server 127.0.0.1:8002 max_fails=3 fail_timeout=30s;
    
    # إعدادات التوزيع
    keepalive 32;
}

# إعادة توجيه HTTP إلى HTTPS (للإنتاج)
server {
    listen 80;
    server_name ta9affi.dz www.ta9affi.dz;
    
    # إعادة توجيه جميع الطلبات إلى HTTPS
    return 301 https://$server_name$request_uri;
}

# الخادم الرئيسي
server {
    # إعدادات الاستماع
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    
    # اسم الخادم
    server_name ta9affi.dz www.ta9affi.dz;
    
    # مجلد الجذر
    root /path/to/ta9affi;
    
    # فهرس الملفات
    index index.html index.htm;
    
    # ===== إعدادات SSL =====
    
    # شهادات SSL (يجب تحديثها للإنتاج)
    ssl_certificate /path/to/ssl/ta9affi.crt;
    ssl_certificate_key /path/to/ssl/ta9affi.key;
    
    # إعدادات SSL محسنة
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # HSTS (HTTP Strict Transport Security)
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # ===== إعدادات الأمان =====
    
    # إخفاء معلومات الخادم
    server_tokens off;
    
    # حماية من XSS
    add_header X-XSS-Protection "1; mode=block" always;
    
    # حماية من MIME type sniffing
    add_header X-Content-Type-Options "nosniff" always;
    
    # حماية من Clickjacking
    add_header X-Frame-Options "SAMEORIGIN" always;
    
    # Content Security Policy
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; img-src 'self' data: https:; font-src 'self' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com;" always;
    
    # Referrer Policy
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # ===== إعدادات الأداء =====
    
    # ضغط الملفات
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # تخزين مؤقت للملفات الثابتة
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|pdf|txt|tar|gz)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
        
        # ضغط إضافي للملفات الثابتة
        gzip_static on;
    }
    
    # ===== إعدادات التطبيق =====
    
    # الملفات الثابتة
    location /static/ {
        alias /path/to/ta9affi/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
        
        # تحسين التسليم
        sendfile on;
        tcp_nopush on;
        tcp_nodelay on;
    }
    
    # ملفات التحميل
    location /uploads/ {
        alias /path/to/ta9affi/uploads/;
        expires 1d;
        add_header Cache-Control "public";
        access_log off;
    }
    
    # favicon
    location = /favicon.ico {
        alias /path/to/ta9affi/static/img/favicon.ico;
        expires 1y;
        access_log off;
    }
    
    # robots.txt
    location = /robots.txt {
        alias /path/to/ta9affi/static/robots.txt;
        expires 1d;
        access_log off;
    }
    
    # ===== التطبيق الرئيسي =====
    
    location / {
        # تمرير الطلبات إلى Gunicorn
        proxy_pass http://ta9affi_app;
        
        # إعدادات الوكيل
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # إعدادات المهلة الزمنية
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # إعدادات التخزين المؤقت
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        proxy_busy_buffers_size 8k;
        
        # إعدادات إعادة المحاولة
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
        proxy_next_upstream_tries 3;
        proxy_next_upstream_timeout 30s;
        
        # تمكين HTTP/1.1
        proxy_http_version 1.1;
        proxy_set_header Connection "";
    }
    
    # ===== إعدادات خاصة =====
    
    # API endpoints - تخزين مؤقت قصير
    location /api/ {
        proxy_pass http://ta9affi_app;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # تخزين مؤقت قصير للAPI
        expires 5m;
        add_header Cache-Control "public";
    }
    
    # تحميل الملفات - حد أقصى أكبر
    location /upload {
        client_max_body_size 50M;
        proxy_pass http://ta9affi_app;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # مهلة أطول للتحميل
        proxy_connect_timeout 60s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }
    
    # صفحة الصحة
    location /health {
        access_log off;
        proxy_pass http://ta9affi_app;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # ===== معالجة الأخطاء =====
    
    # صفحات الأخطاء المخصصة
    error_page 404 /static/errors/404.html;
    error_page 500 502 503 504 /static/errors/50x.html;
    
    # إعدادات السجلات
    access_log /var/log/nginx/ta9affi_access.log combined;
    error_log /var/log/nginx/ta9affi_error.log warn;
    
    # ===== حدود الأمان =====
    
    # حد أقصى لحجم الطلب
    client_max_body_size 16M;
    
    # حد أقصى لحجم العنوان
    large_client_header_buffers 4 16k;
    
    # مهلة العميل
    client_body_timeout 60s;
    client_header_timeout 60s;
    
    # حماية من DDoS
    limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
    limit_req_zone $binary_remote_addr zone=api:10m rate=30r/m;
    
    location /login {
        limit_req zone=login burst=3 nodelay;
        proxy_pass http://ta9affi_app;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# إعدادات عامة لـ Nginx
# يجب إضافتها في nginx.conf الرئيسي

# worker_processes auto;
# worker_connections 1024;
# keepalive_timeout 65;
# types_hash_max_size 2048;
# server_names_hash_bucket_size 64;
