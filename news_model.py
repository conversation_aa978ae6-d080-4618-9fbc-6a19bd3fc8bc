#!/usr/bin/env python3
"""
نموذج الأخبار والتحديثات
"""

from datetime import datetime
from flask_sqlalchemy import SQLAlchemy

# استيراد db من models_new
from models_new import db

class NewsUpdate(db.Model):
    """نموذج الأخبار والتحديثات للشريط الإخباري"""
    __tablename__ = 'news_updates'
    __table_args__ = {'extend_existing': True}
    
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)  # عنوان الخبر
    content = db.Column(db.Text, nullable=False)  # محتوى الخبر
    is_active = db.Column(db.Bo<PERSON>an, default=True)  # نشط أم لا
    priority = db.Column(db.Integer, default=1)  # الأولوية (1-10)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    
    def __repr__(self):
        return f'<NewsUpdate {self.title[:30]}...>'
    
    @classmethod
    def get_active_news(cls, limit=10):
        """الحصول على الأخبار النشطة مرتبة حسب الأولوية والتاريخ"""
        return cls.query.filter_by(is_active=True)\
                       .order_by(cls.priority.desc(), cls.created_at.desc())\
                       .limit(limit).all()
