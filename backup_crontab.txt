# جدولة النسخ الاحتياطي التلقائي لتطبيق Ta9affi
# لتطبيق هذه الجدولة: crontab backup_crontab.txt

# متغيرات البيئة
SHELL=/bin/bash
PATH=/usr/local/sbin:/usr/local/bin:/sbin:/bin:/usr/sbin:/usr/bin
MAILTO=<EMAIL>

# مسارات التطبيق
APP_DIR=/path/to/ta9affi
BACKUP_SCRIPT=$APP_DIR/backup_script.sh
PYTHON_BACKUP=$APP_DIR/backup_manager.py
VENV_PYTHON=$APP_DIR/venv/bin/python

# ===== النسخ الاحتياطي اليومي =====

# نسخ احتياطي لقاعدة البيانات - يومياً في الساعة 2:00 صباحاً
0 2 * * * cd $APP_DIR && $BACKUP_SCRIPT database >> /var/log/ta9affi_backup.log 2>&1

# نسخ احتياطي للملفات - يومياً في الساعة 3:00 صباحاً
0 3 * * * cd $APP_DIR && $BACKUP_SCRIPT files >> /var/log/ta9affi_backup.log 2>&1

# ===== النسخ الاحتياطي الأسبوعي =====

# نسخ احتياطي للإعدادات - أسبوعياً يوم الأحد في الساعة 4:00 صباحاً
0 4 * * 0 cd $APP_DIR && $BACKUP_SCRIPT config >> /var/log/ta9affi_backup.log 2>&1

# نسخ احتياطي شامل - أسبوعياً يوم السبت في الساعة 1:00 صباحاً
0 1 * * 6 cd $APP_DIR && $BACKUP_SCRIPT >> /var/log/ta9affi_backup.log 2>&1

# ===== التنظيف والصيانة =====

# تنظيف النسخ القديمة - يومياً في الساعة 5:00 صباحاً
0 5 * * * cd $APP_DIR && $BACKUP_SCRIPT cleanup >> /var/log/ta9affi_backup.log 2>&1

# تدوير ملفات السجل - أسبوعياً يوم الاثنين في الساعة 6:00 صباحاً
0 6 * * 1 /usr/sbin/logrotate /etc/logrotate.d/ta9affi

# ===== النسخ الاحتياطي باستخدام Python (بديل) =====

# نسخ احتياطي Python - يومياً في الساعة 2:30 صباحاً (كنسخة احتياطية)
# 30 2 * * * cd $APP_DIR && $VENV_PYTHON $PYTHON_BACKUP --all >> /var/log/ta9affi_backup.log 2>&1

# ===== مراقبة النظام =====

# فحص مساحة القرص - كل 6 ساعات
0 */6 * * * df -h | grep -E "(Filesystem|/dev/)" | awk '{if($5+0 > 85) print "تحذير: مساحة القرص ممتلئة " $5 " في " $1}' | mail -s "تحذير مساحة القرص - Ta9affi" <EMAIL>

# فحص حالة النسخ الاحتياطي - يومياً في الساعة 8:00 صباحاً
0 8 * * * cd $APP_DIR && $BACKUP_SCRIPT status | mail -s "تقرير النسخ الاحتياطي اليومي - Ta9affi" <EMAIL>

# ===== النسخ الاحتياطي الطارئ =====

# نسخ احتياطي طارئ لقاعدة البيانات - كل 4 ساعات (في حالة الحاجة)
# 0 */4 * * * cd $APP_DIR && $BACKUP_SCRIPT database

# ===== التحقق من سلامة النسخ الاحتياطية =====

# فحص سلامة النسخ الاحتياطية - أسبوعياً يوم الثلاثاء في الساعة 7:00 صباحاً
0 7 * * 2 cd $APP_DIR && find backups/ -name "*.gz" -exec gzip -t {} \; 2>&1 | grep -v "OK" | mail -s "تقرير سلامة النسخ الاحتياطية - Ta9affi" <EMAIL>

# ===== النسخ الاحتياطي للتخزين السحابي =====

# رفع النسخ الاحتياطية للتخزين السحابي - يومياً في الساعة 6:00 صباحاً
# 0 6 * * * cd $APP_DIR && aws s3 sync backups/ s3://ta9affi-backups/ --delete

# ===== إعدادات خاصة للبيئات المختلفة =====

# للبيئة الإنتاجية - نسخ احتياطي كل ساعتين لقاعدة البيانات
# 0 */2 * * * cd $APP_DIR && $BACKUP_SCRIPT database

# للبيئة التطويرية - نسخ احتياطي يومي فقط
# 0 2 * * * cd $APP_DIR && $BACKUP_SCRIPT

# ===== تنبيهات وإشعارات =====

# إرسال تقرير أسبوعي عن حالة النسخ الاحتياطي - يوم الجمعة في الساعة 9:00 صباحاً
0 9 * * 5 cd $APP_DIR && echo "تقرير النسخ الاحتياطي الأسبوعي:" > /tmp/backup_report.txt && $BACKUP_SCRIPT status >> /tmp/backup_report.txt && mail -s "التقرير الأسبوعي للنسخ الاحتياطي - Ta9affi" <EMAIL> < /tmp/backup_report.txt

# ===== ملاحظات مهمة =====

# 1. تأكد من تحديث المسارات في متغيرات APP_DIR
# 2. تأكد من وجود صلاحيات التنفيذ للسكريبتات: chmod +x backup_script.sh
# 3. تأكد من إعداد البريد الإلكتروني للإشعارات
# 4. راقب ملف السجل: tail -f /var/log/ta9affi_backup.log
# 5. اختبر النسخ الاحتياطي يدوياً قبل الاعتماد على الجدولة

# ===== أوامر مفيدة =====

# عرض الجدولة الحالية:
# crontab -l

# تحرير الجدولة:
# crontab -e

# حذف جميع المهام المجدولة:
# crontab -r

# تطبيق هذا الملف:
# crontab backup_crontab.txt

# فحص حالة خدمة cron:
# systemctl status cron

# إعادة تشغيل خدمة cron:
# sudo systemctl restart cron

# ===== مثال على إعداد logrotate =====

# إنشاء ملف /etc/logrotate.d/ta9affi:
# /var/log/ta9affi_backup.log {
#     daily
#     missingok
#     rotate 30
#     compress
#     delaycompress
#     notifempty
#     create 644 root root
# }
