#!/usr/bin/env python3
"""
إعداد Nginx مع SSL لـ ta9affi.com كبديل لـ Dokploy
"""

import os
import subprocess
import sys

def check_nginx_installed():
    """فحص تثبيت Nginx"""
    try:
        result = subprocess.run(['nginx', '-v'], capture_output=True, text=True)
        print(f"✅ Nginx مثبت: {result.stderr.strip()}")
        return True
    except FileNotFoundError:
        print("❌ Nginx غير مثبت")
        return False

def install_nginx():
    """تثبيت Nginx"""
    print("📦 تثبيت Nginx...")
    try:
        subprocess.run(['apt', 'update'], check=True)
        subprocess.run(['apt', 'install', '-y', 'nginx'], check=True)
        print("✅ تم تثبيت Nginx")
        return True
    except subprocess.CalledProcessError:
        print("❌ فشل في تثبيت Nginx")
        return False

def install_certbot():
    """تثبيت Certbot لـ Let's Encrypt"""
    print("🔒 تثبيت Certbot...")
    try:
        subprocess.run(['apt', 'install', '-y', 'certbot', 'python3-certbot-nginx'], check=True)
        print("✅ تم تثبيت Certbot")
        return True
    except subprocess.CalledProcessError:
        print("❌ فشل في تثبيت Certbot")
        return False

def create_nginx_config():
    """إنشاء إعدادات Nginx"""
    print("📝 إنشاء إعدادات Nginx...")
    
    config = """server {
    listen 80;
    server_name ta9affi.com www.ta9affi.com;
    
    location / {
        proxy_pass http://127.0.0.1:80;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
"""
    
    try:
        with open('/etc/nginx/sites-available/ta9affi', 'w') as f:
            f.write(config)
        
        # تفعيل الموقع
        if os.path.exists('/etc/nginx/sites-enabled/ta9affi'):
            os.remove('/etc/nginx/sites-enabled/ta9affi')
        
        os.symlink('/etc/nginx/sites-available/ta9affi', '/etc/nginx/sites-enabled/ta9affi')
        
        # حذف الإعداد الافتراضي
        default_enabled = '/etc/nginx/sites-enabled/default'
        if os.path.exists(default_enabled):
            os.remove(default_enabled)
        
        print("✅ تم إنشاء إعدادات Nginx")
        return True
        
    except Exception as e:
        print(f"❌ فشل في إنشاء إعدادات Nginx: {str(e)}")
        return False

def test_nginx_config():
    """اختبار إعدادات Nginx"""
    print("🧪 اختبار إعدادات Nginx...")
    try:
        result = subprocess.run(['nginx', '-t'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ إعدادات Nginx صحيحة")
            return True
        else:
            print(f"❌ خطأ في إعدادات Nginx: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ فشل في اختبار Nginx: {str(e)}")
        return False

def restart_nginx():
    """إعادة تشغيل Nginx"""
    print("🔄 إعادة تشغيل Nginx...")
    try:
        subprocess.run(['systemctl', 'restart', 'nginx'], check=True)
        subprocess.run(['systemctl', 'enable', 'nginx'], check=True)
        print("✅ تم إعادة تشغيل Nginx")
        return True
    except subprocess.CalledProcessError:
        print("❌ فشل في إعادة تشغيل Nginx")
        return False

def obtain_ssl_certificate():
    """الحصول على شهادة SSL"""
    print("🔒 الحصول على شهادة SSL من Let's Encrypt...")
    
    try:
        # الحصول على الشهادة
        cmd = [
            'certbot', '--nginx',
            '-d', 'ta9affi.com',
            '-d', 'www.ta9affi.com',
            '--non-interactive',
            '--agree-tos',
            '--email', '<EMAIL>',
            '--redirect'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ تم الحصول على شهادة SSL")
            print("✅ تم تفعيل إعادة التوجيه HTTPS")
            return True
        else:
            print(f"❌ فشل في الحصول على شهادة SSL: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في Certbot: {str(e)}")
        return False

def test_ssl():
    """اختبار SSL"""
    print("🧪 اختبار SSL...")
    
    try:
        # اختبار HTTP
        result_http = subprocess.run(['curl', '-I', 'http://ta9affi.com'], 
                                   capture_output=True, text=True, timeout=10)
        
        # اختبار HTTPS
        result_https = subprocess.run(['curl', '-I', 'https://ta9affi.com'], 
                                    capture_output=True, text=True, timeout=10)
        
        if result_https.returncode == 0:
            print("✅ HTTPS يعمل بشكل صحيح")
            print("🔗 الروابط:")
            print("   📱 https://ta9affi.com")
            print("   🔐 https://ta9affi.com/login")
            print("   📝 https://ta9affi.com/register")
            return True
        else:
            print(f"❌ HTTPS لا يعمل: {result_https.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ فشل في اختبار SSL: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔒 إعداد Nginx مع SSL لـ ta9affi.com")
    print("="*50)
    
    # فحص الصلاحيات
    if os.geteuid() != 0:
        print("❌ يجب تشغيل هذا السكريپت بصلاحيات root")
        print("💡 استخدم: sudo python setup_nginx_ssl.py")
        sys.exit(1)
    
    # تثبيت Nginx إذا لم يكن مثبتاً
    if not check_nginx_installed():
        if not install_nginx():
            sys.exit(1)
    
    # تثبيت Certbot
    if not install_certbot():
        sys.exit(1)
    
    # إنشاء إعدادات Nginx
    if not create_nginx_config():
        sys.exit(1)
    
    # اختبار الإعدادات
    if not test_nginx_config():
        sys.exit(1)
    
    # إعادة تشغيل Nginx
    if not restart_nginx():
        sys.exit(1)
    
    # الحصول على شهادة SSL
    if not obtain_ssl_certificate():
        print("⚠️ فشل في الحصول على شهادة SSL")
        print("💡 تحقق من:")
        print("   - أن ta9affi.com يشير إلى هذا الخادم")
        print("   - أن المنفذ 80 مفتوح ومتاح")
        print("   - أن DNS محدث")
        sys.exit(1)
    
    # اختبار SSL
    test_ssl()
    
    print("\n" + "="*50)
    print("✅ تم إعداد SSL بنجاح!")
    print("🔗 اختبر الروابط:")
    print("   📱 https://ta9affi.com")
    print("   🔐 https://ta9affi.com/login")
    print("   📝 https://ta9affi.com/register")
    print("   ❤️ https://ta9affi.com/health")
    print("="*50)

if __name__ == '__main__':
    main()
