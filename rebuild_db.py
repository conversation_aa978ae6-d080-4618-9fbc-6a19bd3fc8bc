"""
سكريبت لإعادة بناء قاعدة البيانات بالكامل
يحذف قاعدة البيانات الحالية ويعيد إنشائها من الصفر
"""

import os
import sqlite3
import shutil
from app import app, db
from models import User, Role, EducationalLevel, Subject, Domain, KnowledgeMaterial, Competency, Schedule, ProgressEntry, LevelDatabase, LevelDataEntry
from werkzeug.security import generate_password_hash
from datetime import datetime

def rebuild_database():
    """إعادة بناء قاعدة البيانات بالكامل"""
    db_path = os.path.join(os.getcwd(), 'ta9affi.db')
    
    print(f"المسار الحالي: {os.getcwd()}")
    print(f"مسار قاعدة البيانات: {db_path}")
    
    # حذف قاعدة البيانات إذا كانت موجودة
    if os.path.exists(db_path):
        print(f"حذف قاعدة البيانات الحالية: {db_path}")
        os.remove(db_path)
    
    # حذف مجلد البيانات إذا كان موجوداً
    data_dir = os.path.join(os.getcwd(), 'data')
    if os.path.exists(data_dir):
        print(f"حذف مجلد البيانات: {data_dir}")
        shutil.rmtree(data_dir)
    
    # إنشاء مجلد البيانات
    os.makedirs(data_dir, exist_ok=True)
    
    # إنشاء قاعدة البيانات الجديدة
    print("إنشاء قاعدة البيانات الجديدة...")
    
    # إنشاء جميع الجداول
    with app.app_context():
        db.create_all()
        print("تم إنشاء جميع الجداول بنجاح!")
        
        # إضافة المستخدمين
        print("إضافة المستخدمين...")
        users = [
            {
                'username': 'admin',
                'email': '<EMAIL>',
                'password': generate_password_hash('admin123'),
                'role': 'admin'
            },
            {
                'username': 'inspector',
                'email': '<EMAIL>',
                'password': generate_password_hash('inspector123'),
                'role': 'inspector'
            },
            {
                'username': 'teacher',
                'email': '<EMAIL>',
                'password': generate_password_hash('teacher123'),
                'role': 'teacher'
            },
            {
                'username': 'teacher2',
                'email': '<EMAIL>',
                'password': generate_password_hash('teacher123'),
                'role': 'teacher'
            }
        ]
        
        for user_data in users:
            user = User(**user_data)
            db.session.add(user)
        
        db.session.commit()
        
        # إضافة علاقة بين المفتش والأساتذة
        inspector = User.query.filter_by(username='inspector').first()
        teacher1 = User.query.filter_by(username='teacher').first()
        teacher2 = User.query.filter_by(username='teacher2').first()
        
        if inspector and teacher1 and teacher2:
            inspector.supervised_teachers.append(teacher1)
            inspector.supervised_teachers.append(teacher2)
            db.session.commit()
        
        # إضافة المستويات التعليمية
        print("إضافة المستويات التعليمية...")
        levels = [
            {'name': 'السنة الأولى ابتدائي', 'is_active': True, 'database_prefix': 'level1'},
            {'name': 'السنة الثانية ابتدائي', 'is_active': True, 'database_prefix': 'level2'},
            {'name': 'السنة الثالثة ابتدائي', 'is_active': True, 'database_prefix': 'level3'},
            {'name': 'السنة الرابعة ابتدائي', 'is_active': True, 'database_prefix': 'level4'},
            {'name': 'السنة الخامسة ابتدائي', 'is_active': True, 'database_prefix': 'level5'}
        ]
        
        for level_data in levels:
            level = EducationalLevel(**level_data)
            db.session.add(level)
        
        db.session.commit()
        
        # إضافة قواعد البيانات المنفصلة
        print("إضافة قواعد البيانات المنفصلة...")
        for level in EducationalLevel.query.all():
            db_name = f"level_{level.id}_db"
            file_path = f"data/{db_name}.sqlite"
            
            new_db = LevelDatabase(
                level_id=level.id,
                name=f"قاعدة بيانات {level.name}",
                file_path=file_path,
                is_active=True
            )
            db.session.add(new_db)
        
        db.session.commit()
        
        # إضافة بيانات تجريبية للمستوى الأول
        print("إضافة بيانات تجريبية للمستوى الأول...")
        level1_db = LevelDatabase.query.filter_by(level_id=1).first()
        if level1_db:
            # إضافة مواد دراسية
            subjects = [
                {"name": "اللغة العربية", "description": "مادة اللغة العربية للسنة الأولى ابتدائي"},
                {"name": "الرياضيات", "description": "مادة الرياضيات للسنة الأولى ابتدائي"},
                {"name": "التربية العلمية", "description": "مادة التربية العلمية للسنة الأولى ابتدائي"}
            ]
            
            for subject_data in subjects:
                subject = LevelDataEntry(
                    database_id=level1_db.id,
                    entry_type="subject",
                    name=subject_data["name"],
                    description=subject_data["description"],
                    is_active=True
                )
                db.session.add(subject)
            
            db.session.commit()
            
            # إضافة ميادين/أنشطة للغة العربية
            arabic_subject = LevelDataEntry.query.filter_by(database_id=level1_db.id, entry_type="subject", name="اللغة العربية").first()
            if arabic_subject:
                domains = [
                    {"name": "فهم المنطوق", "description": "ميدان/نشاط فهم المنطوق للسنة الأولى ابتدائي"},
                    {"name": "التعبير الشفهي", "description": "ميدان/نشاط التعبير الشفهي للسنة الأولى ابتدائي"},
                    {"name": "فهم المكتوب", "description": "ميدان/نشاط فهم المكتوب للسنة الأولى ابتدائي"},
                    {"name": "التعبير الكتابي", "description": "ميدان/نشاط التعبير الكتابي للسنة الأولى ابتدائي"}
                ]
                
                for domain_data in domains:
                    domain = LevelDataEntry(
                        database_id=level1_db.id,
                        entry_type="domain",
                        parent_id=arabic_subject.id,
                        name=domain_data["name"],
                        description=domain_data["description"],
                        is_active=True
                    )
                    db.session.add(domain)
                
                db.session.commit()
    
    print("تم إعادة بناء قاعدة البيانات بنجاح!")

if __name__ == "__main__":
    rebuild_database()
