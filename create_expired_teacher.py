#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء حساب أستاذ منتهي الاشتراك للاختبار
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db
from models_new import User, Role, Subscription, SubscriptionPlan
from werkzeug.security import generate_password_hash
from datetime import datetime, timedelta

def create_expired_teacher():
    """إنشاء حساب أستاذ منتهي الاشتراك"""
    
    with app.app_context():
        print("=" * 60)
        print("إنشاء حساب أستاذ منتهي الاشتراك")
        print("=" * 60)
        
        # التحقق من وجود المستخدم
        existing_user = User.query.filter_by(username='teacher_expired').first()
        if existing_user:
            print("❌ المستخدم موجود بالفعل")
            print(f"   اسم المستخدم: {existing_user.username}")
            print(f"   البريد: {existing_user.email}")
            print(f"   الدور: {existing_user.role}")

            # التحقق من حالة الاشتراك
            if existing_user.has_active_subscription:
                print(f"   الاشتراك: نشط")

                # إنهاء الاشتراك عن طريق إنهاء الفترة التجريبية
                print("\n🔄 إنهاء الاشتراك...")
                existing_user.free_trial_end = datetime.now() - timedelta(days=1)
                db.session.commit()
                print("✅ تم إنهاء الاشتراك")
            else:
                print(f"   الاشتراك: منتهي")

            print(f"\n📋 بيانات الدخول:")
            print(f"   اسم المستخدم: teacher_expired")
            print(f"   كلمة المرور: 123456")
            print(f"   الرابط: http://127.0.0.1:5000/login")
            return
        
        # إنشاء المستخدم الجديد
        print("🔄 إنشاء حساب جديد...")
        
        new_user = User(
            username='teacher_expired',
            email='<EMAIL>',
            password=generate_password_hash('123456'),
            role=Role.TEACHER,
            phone_number='0555999888',  # رقم هاتف مطلوب
            wilaya_code='16',  # الجزائر العاصمة
            _is_active=True
        )
        
        db.session.add(new_user)
        
        try:
            db.session.commit()
            print("✅ تم إنشاء الحساب بنجاح")
            
            print(f"\n📋 بيانات الحساب الجديد:")
            print(f"   اسم المستخدم: teacher_expired")
            print(f"   كلمة المرور: 123456")
            print(f"   البريد الإلكتروني: <EMAIL>")
            print(f"   الدور: {Role.TEACHER}")
            print(f"   رقم الهاتف: 0555999888")
            print(f"   الولاية: الجزائر العاصمة")
            print(f"   حالة الاشتراك: منتهي ❌")
            
            print(f"\n🌐 للدخول:")
            print(f"   الرابط: http://127.0.0.1:5000/login")
            print(f"   اسم المستخدم: teacher_expired")
            print(f"   كلمة المرور: 123456")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ خطأ في إنشاء الحساب: {str(e)}")
        
        print("\n" + "=" * 60)
        print("💡 ملاحظات للاختبار:")
        print("- يجب أن يظهر زر 'تجديد الاشتراك' بدلاً من 'إضافة تقدم جديد'")
        print("- في صفحة البرنامج التعليمي: /teaching-program")
        print("- في لوحة التحكم: /dashboard/teacher")
        print("- عند محاولة إضافة تقدم: يجب إعادة توجيه لصفحة الاشتراكات")
        print("=" * 60)

if __name__ == "__main__":
    create_expired_teacher()
